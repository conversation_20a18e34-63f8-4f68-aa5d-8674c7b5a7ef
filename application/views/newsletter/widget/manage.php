<?php if (!isset($newsletter_form)) {$newsletter_form = Widget_Newsletter::form(array('lang' => $info['lang'], 'code' => 'list'));} ?>
<?php if ($newsletter_form): ?>
<div class="nw">
	<div class="wrapper">
		<!-- Newsletter Subscribe -->
		<div class="nw-title"><?php echo Arr::get($cmslabel, 'newsletter_widget_title'); ?></div>
		<div class="nw-subtitle"><?php echo Arr::get($cmslabel, 'newsletter_widget_subtitle'); ?></div>
		<div class="nw-cnt">
			<form class="nw-form" action="<?php echo $newsletter_form['url_manage']; ?>" method="POST" id="newsletter_subscribe_<?php echo $newsletter_form['id']; ?>">
				<div class="nw-form-main">
					<input type="hidden" name="lang" value="<?php echo $info['lang']; ?>"  />
					<input type="hidden" name="list" value="<?php echo $newsletter_form['id']; ?>"  />
					<input type="hidden" name="first_name" value="" />
					<input type="hidden" name="last_name" value="" />
					<input class="nw-input" type="text" name="email" placeholder="<?php echo Arr::get($cmslabel, 'enter_email'); ?>" />
					<button class="nw-button" type="submit"><?php echo Arr::get($cmslabel, 'newsletter_signup', 'Pošalji'); ?></button>
					<div id="field-error-email" class="nw-error newsletter-error" style="display: none;"></div>
				</div>

				<?php if (!empty($newsletter_form['gdpr_accept_label'])): ?>
					<div class="nw-checkbox nw-checkbox-static">
						<input type="hidden" name="gdpr_accept" value="0" />
						<input type="checkbox" name="gdpr_accept" value="1" id="gdpr_accept-1" />
						<label for="gdpr_accept-1"><span><?php echo str_replace(['<p>', '</p>'], " ", $newsletter_form['gdpr_accept_label']); ?></span><?php // echo str_replace(['<p>', '</p>'], " ", $newsletter_form['gdpr_accept_label']); ?></label>
						<span id="field-error-newsletter_gdpr_accept" class="error nw-error gdpr_accept-error" style="display: none"></span>
					</div>
				<?php endif; ?>
			</form>
			<div class="nw-success newsletter_subscribe_success" style="display: none;"><?php echo Arr::get($cmslabel, 'success_subscribe'); ?></div>
		</div>
	</div>	
</div>
<?php endif; ?>
