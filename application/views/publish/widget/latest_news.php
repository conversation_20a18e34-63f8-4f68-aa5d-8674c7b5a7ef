<?php $category = Widget_Publish::category($info['lang'], 'blog'); ?>
<?php $items = Widget_Publish::publishes(array('lang' => $info['lang'], 'category_code' => Arr::get($category, 'code'), 'limit' => 5, 'extra_fields' => ['short_description'])); ?>

<div class="hpw-title"><?php echo Arr::get($category, 'title'); ?></div>
<?php if(!empty($category['subtitle'])): ?>
	<div class="hpw-subtitle"><?php echo Arr::get($category, 'subtitle'); ?></div>
<?php endif; ?>

<div class="hpw-container clear">
	<?php $ln = 1; ?>
	<?php foreach ($items as $item): ?>
		<?php if($ln == 1): ?>	
			<div class="hpw-left">
				<a href="<?php echo $item['url']; ?>" class="pp">
					<div class="pp-image lloader">
						<span><img <?php echo Thumb::generate($item['main_image'], array('width' => 840, 'height' => 560, 'default_image' => '/media/images/no-image-820.jpg', 'placeholder' => '/media/images/no-image-820.jpg')); ?> alt="" /></span>
					</div>
					<div class="pp-cnt">
						<?php if(!empty($item['headline'])): ?>
							<div class="pp-headline"><?php echo $item['headline']; ?></div>
						<?php endif; ?>
						<div class="pp-title"><?php echo $item['title']; ?></div>
						<?php if(!empty($item['short_description'])): ?>
							<div class="pp-desc"><?php echo $item['short_description']; ?></div>
						<?php endif; ?>
					</div>
				</a>	
			</div>
			<div class="hpw-right">
		<?php endif; ?>

		<?php if($ln > 1): ?>
			<a href="<?php echo $item['url']; ?>" class="pp fz0">
				<div class="pp-image lloader">
					<span><img <?php echo Thumb::generate($item['main_image'], array('width' => 175, 'height' => 120, 'default_image' => '/media/images/no-image-175.jpg', 'placeholder' => '/media/images/no-image-175.jpg')); ?> alt="" /></span>
				</div>
				<div class="pp-cnt">
					<?php if(!empty($item['headline'])): ?>
						<div class="pp-headline"><?php echo $item['headline']; ?></div>
					<?php endif; ?>
					<div class="pp-title"><?php echo $item['title']; ?></div>
					<?php if(!empty($item['short_description'])): ?>
						<div class="pp-desc"><?php echo $item['short_description']; ?></div>
					<?php endif; ?>
				</div>
			</a>
		<?php endif; ?>
		<?php $ln++; ?>
	<?php endforeach; ?>
		<a href="<?php echo Arr::get($category, 'url'); ?>" class="btn btn-blue hpw-btn"><?php echo Arr::get($cmslabel, 'show_all_news'); ?></a>
	</div>
</div>

<div class="mobile-hpw-container">
	<div class="owl-carousel slider m-hpw-slider">
		<?php foreach ($items as $item): ?>
			<a href="<?php echo $item['url']; ?>" class="pp fz0">
				<div class="pp-image lloader">
					<span><img <?php echo Thumb::generate($item['main_image'], array('width' => 760, 'height' => 400, 'crop' => true, 'default_image' => '/media/images/no-image-175.jpg', 'placeholder' => '/media/images/no-image-175.jpg')); ?> alt="" /></span>
				</div>
				<div class="pp-cnt">
					<?php if(!empty($item['headline'])): ?>
						<div class="pp-headline"><?php echo $item['headline']; ?></div>
					<?php endif; ?>
					<div class="pp-title"><?php echo $item['title']; ?></div>
					<?php if(!empty($item['short_description'])): ?>
						<div class="pp-desc"><?php echo $item['short_description']; ?></div>
					<?php endif; ?>
				</div>
			</a>
		<?php endforeach; ?>
	</div>	
	<a href="<?php echo Arr::get($category, 'url'); ?>" class="btn btn-blue hpw-btn"><?php echo Arr::get($cmslabel, 'show_all_news'); ?></a>
</div>