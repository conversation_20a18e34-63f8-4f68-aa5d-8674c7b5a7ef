<?php $i = 1; ?>
<?php foreach ($items as $item): ?>
<?php $big = 0 ?>
<?php if (isset($pagination->current_page) AND $pagination->current_page == 1): ?>
	<?php if ($i == 1 OR $i == 2): ?>
		<?php $big = 1; ?>
	<?php endif ?>
<?php endif ?>

<a href="<?php echo $item['url']; ?>" class="lloader pp<?php if ($big == 1): ?> pp-big<?php endif ?>">
	<figure class="pp-image">
		<?php if ($big == 1): ?>
			<img <?php echo Thumb::generate($item['main_image'], array('width' => 820, 'height' => 546, 'crop' => TRUE, 'default_image' => '/media/images/no-image-820.jpg', 'placeholder' => '/media/images/no-image-820.jpg')); ?> title="<?php echo Text::meta($item['main_image_title']); ?>" alt="<?php echo Text::meta($item['main_image_description']); ?>" />
		<?php else: ?>
			<img <?php echo Thumb::generate($item['main_image'], array('width' => 390, 'height' => 260, 'crop' => TRUE, 'default_image' => '/media/images/no-image-390.jpg', 'placeholder' => '/media/images/no-image-390.jpg')); ?> title="<?php echo Text::meta($item['main_image_title']); ?>" alt="<?php echo Text::meta($item['main_image_description']); ?>" />
		<?php endif ?>
	</figure>
	<div class="pp-cnt">
		<p class="pp-headline"><?php echo $item['headline']; ?></p>
		<h2 class="pp-title"><?php echo $item['title']; ?></h2>
		<div class="pp-desc"><?php echo strip_tags(((!empty($item['short_description'])) ? $item['short_description'] : '')); ?></div>
	</div>
</a>
<?php $i++; ?>
<?php endforeach; ?>