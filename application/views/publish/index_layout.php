<?php $child_categories = Widget_Publish::categories(['lang' => $info['lang'], 'start_position' => '01']); ?>
<?php if (!empty($child_categories) AND is_array($child_categories) AND count($child_categories)): ?>
	<div class="p-categories-cnt">
		<a class="m-p-categories" href="javascript:void(0);"><?php echo Arr::get($cmslabel, 'categories'); ?></a>
		<ul class="p-categories">
			<!-- <a href="javascript:toggleBox(['.m-p-categories', '.p-categories', 'body']);" class="m-p-categories-close"><span><?php //echo Arr::get($cmslabel, 'categories'); ?></span></a> -->
			<?php $current = Utils::extract_segments($info['lang'], $info['cmspage_url'], 2); ?>
			<?php foreach ($child_categories as $child_category): ?>
				<li><a href="<?php echo $child_category['url']; ?>"<?php if ($child_category['url'] == $current): ?> class="active"<?php endif; ?>><span><?php echo $child_category['title']; ?></span></a></li>
			<?php endforeach; ?>
		</ul>
	</div>
<?php endif; ?>

<?php if (!empty($items) AND is_array($items) AND count($items)): ?>
	<div class="p-items fz0" id="items_<?php echo $kind['code']; ?>" data-infinitescroll="items_<?php echo $kind['code']; ?>" data-infinitescroll_next_page="<?php echo $pagination->next_page; ?>" data-infinitescroll_total_pages="<?php echo $pagination->total_pages; ?>" data-infinitescroll_auto_trigger="1">
		<?php echo View::factory('publish/index_entry', ['items' => $items, 'pagination' => $pagination]); ?>
	</div>

	<?php echo $pagination; ?>
	<?php if ($pagination): ?>
		<div class="load-more-container">
			<a href="javascript:void(0);" class="btn btn-blue load-more btn-load-more" style="display: none"><?php echo str_replace(array('%PER_PAGE%', '%TOTAL%', '%NEXT_PAGE_FIRST_ITEM%', '%NEXT_PAGE_LAST_ITEM%'), array($pagination->items_per_page, $pagination->total_items, $pagination->next_page_first_item, $pagination->next_page_last_item), Arr::get($cmslabel, 'load_more', '')); ?></a>
		</div>
	<?php endif; ?>
<?php else: ?>
	<?php echo Arr::get($cmslabel, 'no_publish'); ?>
<?php endif; ?>