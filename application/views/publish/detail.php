<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta($item['seo_title']); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/detail', ['item' => $item]); ?><?php $this->endblock('seo'); ?>

<?php $this->block('content_layout'); ?>
	<div class="bc">
		<?php $this->block('breadcrumb'); ?>
			<?php $breadcrumb = Widget_Cms::breadcrumb($info['lang'], $info['cmspage_url'], TRUE, $item['breadcrumbs']); ?>
			<?php echo View::factory('cms/widget/breadcrumb', ['breadcrumb' => $breadcrumb]); ?>
		<?php $this->endblock('breadcrumb'); ?>
	</div>
	<p class="pd-headline"><?php echo $item['headline'] ?></p>

	<h1 class="pd-title"><?php echo $item['seo_h1'] ?></h1>

	<?php if ($item['main_image']): ?>
		<div class="pd-main-image">
			<img <?php echo Thumb::generate($item['main_image'], ['width' => 1430, 'height' => 954, 'default_image' => '/media/images/no-image-1430.jpg', 'html_tag' => TRUE]); ?>  alt="<?php echo Text::meta($item['main_image_description']); ?>" />
		</div>
	<?php endif ?>	

	<div class="pd-content-cnt wrapper2<?php if (!$item['main_image']): ?> no-image<?php endif ?>">
		<p class="pd-date"><?php echo Arr::get($cmslabel, 'datetime_published'); ?>: <?php echo Date::humanize($item['datetime_published'], 'custom', 'd. F Y.'); ?></p>
		<div class="pd-content cms-content clear">
			<!-- Post excerpt/short description -->
			<div class="pd-short-desc extra"><?php echo $item['short_description']; ?></div>

			<?php echo $item['content']; ?>
		</div>
			
		<?php $images = Utils::get_files('publish', $item['id'], 'image', $info['lang'], 1); ?>
		<?php if ($images): ?>
			<div class="pd-thumbs">
				<?php foreach ($images as $file): ?>
					<a href="<?php echo $file['url']; ?>" class="fancybox lloader pd-thumb" rel="gallery" title="<?php echo Text::meta($file['title']); ?><?php if($file['description']): ?> - <?php endif; ?><?php echo Text::meta($file['description']); ?>">
						<figure><img <?php echo Thumb::generate($file['file'], ['width' => 990, 'default_image' => '/media/images/no-image-990.jpg', 'placeholder' => '/media/images/no-image-990.jpg']); ?> alt="<?php echo Text::meta($file['description']); ?>" /></figure>
					</a>
				<?php endforeach; ?>
			</div>
		<?php endif; ?>

		<?php echo View::factory('cms/widget/share', ['item' => isset($item) ? $item : []]); ?>
	</div>

	<!-- Related products -->
	<?php $related_items = (Kohana::config('app.catalog.use_productrelatedpublishes')) ? Widget_Catalog::products(['lang' => $info['lang'], 'related_publish_id' => $item['id'], 'limit' => 12]) : []; ?>
	<?php if ($related_items): ?>
		<div class="cd-related cd-related2">
			<div class="cd-related-title"><?php echo Arr::get($cmslabel, 'related_products'); ?></div>
			<div class="owl-carousel slider cd-related-content fz0">
				<?php echo View::factory('catalog/widget/related_products', ['related_list_items' => $related_items]); ?>
			</div>
		</div>
	<?php endif; ?>

	<!-- Other posts -->
	<?php $other_items = Widget_Publish::publishes(['lang' => $info['lang'], 'category_code' => $item['category_code'], 'id_exclude' => $item['id'], 'limit' => 4, 'extra_fields' => array('short_description')]); ?>
	<?php if ($other_items): ?>
		<div class="pd-other">
			<h4 class="pd-other-title"><?php echo Arr::get($cmslabel, 'publish_related'); ?></h4>
			<div class="pd-other-items slider owl-carousel fz0">
				<?php echo View::factory('publish/index_entry', ['items' => $other_items]); ?>
			</div>
		</div>
	<?php endif; ?>

<?php $this->endblock('content_layout'); ?>