<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-checkout page-shipping<?php $this->endblock('page_class'); ?>

<?php $this->block('header'); ?> 
	<div class="header-checkout">
		<a href="<?php echo Utils::homepage($info['lang']); ?>" class="logo"></a>
		<div class="hc-steps clear">	
			<!-- Steps -->
			<?php echo View::factory('webshop/widget/step', array('current_step' => 2)); ?>

			<div class="hc-label"><?php echo Arr::get($cmslabel, 'checkout_label'); ?></div>
		</div>
	</div>
<?php $this->endblock('header'); ?>

<?php $this->block('main_class'); ?> checkout-main<?php $this->endblock('main_class'); ?>

<?php $this->block('content_layout'); ?>
<div class="wc-container">
	<form class="step2 checkout-form form-inline form-label ajax_siteform ajax_siteform_loading" action="#webshop_form" method="post" name="webshop" id="webshop_form" accept-charset="utf-8">
		<!-- Column 1 -->
		<div class="wc-col wc-col1 wc-step2-col1">
			<div class="col-cont">
				<h2 class="wc-subtitle"><?php echo Arr::get($cmslabel, 'customer_details', 'Podaci za dostavu proizvoda'); ?></h2>
				<?php echo Arr::get($cms_page, 'content'); ?>
				<div class="global_error global-error"<?php if (empty($errors) OR (isset($errors) AND is_array($errors) AND !count($errors))): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, 'form_validation_error'); ?></div>
				<?php $i = 1; ?>
				<?php foreach ($customer_fields as $field): ?>
					<?php if ($field == 'shipping'): ?>
						<input type="hidden" name="shipping">
						<?php continue; ?>
					<?php endif; ?>
					<div class="field field-<?php echo $field; ?><?php if($i % 2 == 0): ?> last<?php endif; ?><?php if(count($available_shippings) <= 1): ?> single<?php endif; ?>">
						<?php $error = Valid::get_error($field, $errors); ?>
						<?php /* if ($field == 'shipping'): ?>
							<label for="field-<?php echo $field; ?>" class="label-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, 'field_shipping'); ?><?php if (in_array($field, $request_fields)): ?><?php endif; ?></label>
							<?php echo Form::select_as_radio2($field, $available_shippings, $data->shipping->id); ?>
						<?php else: */ ?>
							<?php if($field == 'country'): ?>
								<label for="field-<?php echo $field; ?>" class="label-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, $field); ?><?php if (in_array($field, $request_fields)): ?><?php endif; ?></label>
							<?php endif; ?>
							<?php echo $data->input($field, 'form'); ?>
						<?php //endif; ?>
						<?php if($field != 'shipping' AND $field != 'country'): ?>
							<?php if($field == 'message'): ?>
								<label for="field-<?php echo $field; ?>" class="label-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, 'field_message'); ?><?php if (in_array($field, $request_fields)): ?><?php endif; ?></label>
							<?php else: ?>
								<label for="field-<?php echo $field; ?>" class="label-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, $field); ?><?php if (in_array($field, $request_fields)): ?><?php endif; ?></label>
							<?php endif; ?>
						<?php endif; ?>
						<?php if($field == 'phone'): ?><span class="phone-tooltip"><?php echo Arr::get($cmslabel, 'phone_tooltip', 'Samo za potrebe dostavne službe'); ?></span><?php endif; ?>
						<span id="field-error-<?php echo $field; ?>" class="field_error error" <?php if ( ! $error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, "error_{$error}", $error); ?></span>
					</div>
					<?php $i++; ?>
				<?php endforeach; ?>

				<div class="clear"></div>
			</div>
		</div>

		<!-- Column 2 -->
		<div class="wc-col wc-col2 wc-step2-col2">
			<div class="col-cont">
				<?php if (Kohana::config('app.webshop.bill_on_shipping_step')): ?>
					<h2 class="wc-subtitle"><?php echo Arr::get($cmslabel, 'bill_shipping', 'Podaci za dostavu računa'); ?></h2>

					<?php if (!empty($customer_bill_fields) AND is_array($customer_bill_fields) AND count($customer_bill_fields)): ?>
						<?php $b = 0; ?>
						<?php foreach ($customer_bill_fields as $field): ?>
							<?php $error = Valid::get_error($field, $errors); ?>
							<?php if($field == 'b_same_as_shipping' OR $field == 'b_r1'): ?>
								<p class="field field-<?php echo $field; ?>">
									<?php echo $data->input($field, 'form'); ?>
									<label for="field-<?php echo $field; ?>" class="label-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, substr($field, 2, strlen($field))); ?><?php if (in_array($field, $request_fields)): ?> *:<?php endif; ?></label>
									<span id="field-error-<?php echo $field; ?>" class="field_error error" <?php if ( ! $error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, "error_{$error}", $error); ?></span>
								</p>
							<?php else: ?>
								<p class="field field-<?php echo $field; ?><?php if($b % 2 == 0): ?> last<?php endif; ?>">
									<?php if($field == 'b_country'): ?>
										<label for="field-<?php echo $field; ?>" class="label-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, substr($field, 2, strlen($field))); ?><?php if (in_array($field, $request_fields)): ?> *:<?php endif; ?></label>
									<?php endif; ?>
									<?php echo $data->input($field, 'form'); ?>
									<?php if($field != 'b_country'): ?>	
										<label for="field-<?php echo $field; ?>" class="label-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, substr($field, 2, strlen($field))); ?><?php if (in_array($field, $request_fields)): ?> *:<?php endif; ?></label>
									<?php endif; ?>
									<?php if($field == 'phone'): ?><span class="phone-tooltip"><?php echo Arr::get($cmslabel, 'phone_tooltip', 'Samo za potrebe dostavne službe'); ?>"></span><?php endif; ?>
									<span id="field-error-<?php echo $field; ?>" class="field_error error" <?php if ( ! $error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, "error_{$error}", $error); ?></span>
								</p>
							<?php endif; ?>
							<?php $b++; ?>
						<?php endforeach; ?>
					<?php else: ?>
						<?php echo Arr::get($cmslabel, 'same_as_shipping', 'Podaci za dostavu računa jednaki su podacima za dostavu proizvoda'); ?>
					<?php endif; ?>
				<?php endif; ?>
			</div>

			<button class="btn btn-green btn-checkout" type="submit"><span><?php echo Arr::get($cmslabel, 'goto_step3_button'); ?></span></button>
			<div class="checkout-finish"><?php echo Arr::get($cmslabel, 'checkout_finish_note'); ?></div>
		</div>
	</form>
</div>
<?php $this->endblock('content_layout'); ?>

<?php $this->block('newsletter'); ?> <?php $this->endblock('newsletter'); ?>

<?php $this->block('footer'); ?>
	<div class="footer checkout-footer fz0">
		<div class="clear wrapper">
			<div class="cf-col cf-col1">
				<div class="footer-title"><?php echo Arr::get($cmslabel, 'footer_title_contact'); ?></div>
				<ul class="footer-contact">
					<li class="item-tel"><?php echo Arr::get($cmslabel, 'contact_tel'); ?></li>
					<li class="item-mail"><?php echo Arr::get($cmslabel, 'contact_mail'); ?></li>		
				</ul>
				
			</div>
			<div class="cf-col cf-col2">
				<?php echo Arr::get($cmslabel, 'footer_logos'); ?>
			</div>
			<div class="cf-col cf-col3">
				<?php echo Arr::get($cmslabel, 'footer_logos_payment'); ?>
			</div>
			<div class="copy"><?php echo str_replace('%YEAR%', date('Y'), Arr::get($cmslabel, 'copyright', '')); ?></div>
			<div class="dev"><?php echo Kohana::config('signature.webshop.'.$info['lang']); ?></div>
			<div class="clear"></div>
		</div>
	</div>
<?php $this->endblock('footer'); ?>