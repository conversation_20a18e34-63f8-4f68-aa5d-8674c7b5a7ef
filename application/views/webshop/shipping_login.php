<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-checkout page-checkout-step1 page-checkout-login<?php $this->endblock('page_class'); ?>

<?php $this->block('main_class'); ?> checkout-login-main<?php $this->endblock('main_class'); ?>

<?php $this->block('content_layout'); ?>
<h1><?php echo Arr::get($cms_page, 'seo_h1'); ?></h1>

<div class="wc-container clear">
	<?php if (!$user): ?>
		<div class="wc-col wc-col1 wc-step1-col1">
			<h2><?php echo Arr::get($cmslabel, 'guest_checkout', 'Želite kupiti bez registracije?'); ?></h2>
			<div class="wc-cnt"><?php echo Arr::get($cms_page, 'content'); ?></div>
			<div class="wc-col-cnt">
				<form class="form-inline form-label wc-guest-form ajax_siteform" method="post" action="" accept-charset="utf-8" enctype="multipart/form-data" name="login" id="webshop_form_login">
					<input type="hidden" name="guest_checkout" value="1" />
					<?php $error = ($message_type AND $message) ? "{$message_type}_{$message}" : ""; ?>
					<p class="field fz0 field-email">
						<input type="email" name="email" id="field-email" />
						<label for="id_email"><?php echo Arr::get($cmslabel, 'email'); ?></label>
						<span id="field-error-email" class="field_error error" <?php if ( ! $error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, $error, $error); ?></span>
					</p>
					<button class="btn btn-wc-guest" type="submit" name="guest_checkout" value="1"><span><?php echo Arr::get($cmslabel, 'continue_without_signup', 'Nastavi kao gost'); ?></span></button>
				</form>
			</div>
		</div>

		<div class="wc-col wc-col2 wc-step1-col2">
			<h2 class="wc-subtitle"><?php echo Arr::get($cmslabel, 'have_account', 'Već imate korisnički račun?'); ?></h2>
			<div class="wc-cnt"><?php echo Arr::get($cmslabel, 'login_to_buy', 'Prijavite se putem emaila za brzu kupnju'); ?></div>
			<div class="wc-col-cnt">
				<form class="form-inline form-label wc-login-form step1 ajax_siteform" method="post" action="" accept-charset="utf-8" enctype="multipart/form-data" name="login" id="webshop_form_login">
					<?php $error = ($message_type AND $message) ? "{$message_type}_{$message}" : ""; ?>
					<input type="hidden" name="login" value="1" />
					<div class="global_success global-success" data-form_name="forgotten_password" style="display: none"><span class="close"></span><?php echo Arr::get($cmslabel, 'success_forgotten_password'); ?></div>
					<div class="global_error global-error" data-form_name="forgotten_password" style="display: none"><span class="close"></span><?php echo Arr::get($cmslabel, 'error_forgotten_password'); ?></div>

					<p class="field field-email">
						<input type="email" name="email" id="field-email" />
						<label for="id_email1"><?php echo Arr::get($cmslabel, 'email'); ?></label>
						<span id="field-error-email" class="field_error error" <?php if ( ! $error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, $error, $error); ?></span>
					</p>
					<p class="field field-password">
						<input type="password" name="password" id="field-password" />
						<label for="id_password"><?php echo Arr::get($cmslabel, 'password'); ?></label>
					</p>
					<p class="remember field-remember">
						<input type="checkbox" name="remember" id="id_rememberme" value="1" checked />
						<label for="id_rememberme"><?php echo Arr::get($cmslabel, 'remember'); ?></label>
					</p>
					<div class="login-buttons checkout-login-buttons">
					<button class="btn btn-wc-login" type="submit"><span><?php echo Arr::get($cmslabel, 'login', 'Prijava'); ?></span></button>
					<div class="auth-links wc-auth-links">
						<a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'forgotten_password', FALSE); ?>"><?php echo Arr::get($cmslabel, 'forgotten_password'); ?></a>
					</div>
					</div>
				</form>
			</div>		
		</div>
	<?php else: ?>
		<form class="ajax_siteform" method="post" action="" accept-charset="utf-8" enctype="multipart/form-data" name="login" id="webshop_form_login">	
			<p class="loggedin-next-step"><button class="btn-big" type="submit" name="already_login" value="1"><?php echo Arr::get($cmslabel, 'goto_step2_button', 'Nastavi na sljedeći korak'); ?></button></p>
		</form>
	<?php endif; ?>	
</div>
<?php $this->endblock('content_layout'); ?>

<?php $this->block('newsletter'); ?> <?php $this->endblock('newsletter'); ?>

<?php $this->block('footer'); ?>
	<div class="footer checkout-footer fz0">
		<div class="clear wrapper">
			<div class="cf-col cf-col1">
				<div class="footer-title"><?php echo Arr::get($cmslabel, 'footer_title_contact'); ?></div>
				<ul class="footer-contact">
					<li class="item-tel"><?php echo Arr::get($cmslabel, 'contact_tel'); ?></li>
					<li class="item-mail"><?php echo Arr::get($cmslabel, 'contact_mail'); ?></li>		
				</ul>

			</div>
			<div class="cf-col cf-col2">
				<?php echo Arr::get($cmslabel, 'footer_logos'); ?>
			</div>
			<div class="cf-col cf-col3">
				<?php echo Arr::get($cmslabel, 'footer_logos_payment'); ?>
			</div>
			<div class="copy"><?php echo str_replace('%YEAR%', date('Y'), Arr::get($cmslabel, 'copyright', '')); ?></div>
			<div class="dev"><?php echo Kohana::config('signature.webshop.'.$info['lang']); ?></div>
			<div class="clear"></div>
		</div>
	</div>
<?php $this->endblock('footer'); ?>