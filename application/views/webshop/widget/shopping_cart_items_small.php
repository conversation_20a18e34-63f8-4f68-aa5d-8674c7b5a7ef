<div class="shoppingcart_items_small cart-small">
	<h2 class="w-cart-title"><?php echo Arr::get($cmslabel, 'products_in_shopping_cart', 'U košarici'); ?> <span class="counter">(<?php echo count($products); ?>)</span></h2>
	<a class="w-btn-change" href="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', ''); ?>"><span><?php echo Arr::get($cmslabel, 'change_cart', 'Uredi sadržaj košarice'); ?></span></a>

	<div class="clear"></div>

	<!-- Cart items -->
	<div class="w-table w-table-small">
		<?php $i=1; ?>
		<?php foreach ($products as $product_code => $product_data): ?>
			<?php $product_status = $products_status[$product_code]; ?>
			<div class="wp<?php if(!empty($product_status['extra_price_lowest']) AND $product_status['extra_price_lowest'] > 0 AND $product_status['total_basic'] > $product_status['total']): ?> wp-lowest-pb<?php endif; ?>" id="product-<?php echo $product_code; ?>">
				<div class="wp-image">
					<figure>
						<img <?php echo Thumb::generate($product_data['main_image'], array('width' => 100, 'height' => 150, 'html_tag' => TRUE, 'default_image' => '/media/images/no-image-80.jpg')); ?> alt="<?php echo $product_data['title']; ?>" />
					</figure>
				</div>
				<div class="wp-cnt<?php if(empty($product_data['main_image'])): ?> no-image<?php endif; ?>">
					<h2 class="wp-title"><?php echo $product_data['title']; ?></h2>
					<div class="wp-code"><?php echo Arr::get($cmslabel, 'code'); ?>: <?php echo (!empty($product_data['variation'])) ? $product_data['variation_code'] : $product_data['code']; ?></div>
					<?php if (!empty($product_data['variation'])): ?>
						<div class="wp-variations">
							<?php if ($product_data['variation_attributes']): ?>
								<?php echo $product_data['variation_attributes']; ?>
							<?php elseif ($product_data['variation_title']): ?>
								<?php echo $product_data['variation_title']; ?>
							<?php elseif ($product_data['variation_code']): ?>
								<?php echo $product_data['variation_code']; ?>
							<?php endif; ?>
						</div>
					<?php endif; ?>
					<?php if (!empty($product_data['coupon_price'])): ?>
						<?php echo Arr::get($cmslabel, 'coupon_value'); ?>: <?php echo $product_data['coupon_price']; ?>
					<?php elseif (!empty($product_data['bonus_total'])): ?>
						<?php echo Arr::get($cmslabel, 'bonus_total_value'); ?>: <?php echo $product_data['bonus_total']; ?>
					<?php endif; ?>					
					<?php if ($product_data['type'] == 'coupon'): ?>
						<div class="wp-coupon">
							<?php echo str_replace('%TOTAL%', (int) $product_status['qty'], Arr::get($cmslabel, 'coupon_product_intro', '')); ?>
							<input type="hidden" name="personmessage[<?php echo $product_code; ?>][title]" value="<?php echo Text::meta($product_data['title']); ?>">
							<div class="couponrecipients">
								<?php $couponrecipients = ((isset($customer_data['couponrecipient'][$product_code])) ? $customer_data['couponrecipient'][$product_code] : []); ?>
								<?php for ($i = 1; $i <= $product_status['qty']; $i++): ?>
								<div class="wp-coupon-item">
									<div class="wp-coupon-title"><?php echo Arr::get($cmslabel, 'coupon_delivery'); ?> #<?php echo $i; ?></div>
									<?php $couponrecipient = ((isset($couponrecipients[$i])) ? $couponrecipients[$i] : []); ?>
									<?php $couponrecipienttype = Arr::get($couponrecipient, 'type', 'email'); ?>

									<input type="radio" name="couponrecipient[<?php echo $product_code; ?>][<?php echo $i; ?>][type]" data-couponrecipient="<?php echo $product_code; ?>-<?php echo $i; ?>" id="field-couponrecipient-<?php echo $product_code; ?>-<?php echo $i; ?>-email" value="email" <?php if ($couponrecipienttype == 'email'): ?>checked<?php endif; ?>> <label for="field-couponrecipient-<?php echo $product_code; ?>-<?php echo $i; ?>-email">Emailom</label>
									<input type="radio" name="couponrecipient[<?php echo $product_code; ?>][<?php echo $i; ?>][type]" data-couponrecipient="<?php echo $product_code; ?>-<?php echo $i; ?>" id="field-couponrecipient-<?php echo $product_code; ?>-<?php echo $i; ?>-address" value="address" <?php if ($couponrecipienttype == 'address'): ?>checked<?php endif; ?>> <label for="field-couponrecipient-<?php echo $product_code; ?>-<?php echo $i; ?>-address">Poštom</label>
									<span data-couponrecipient_extra="<?php echo $product_code; ?>-<?php echo $i; ?>-email" <?php if ($couponrecipienttype == 'email'): ?>class="active"<?php endif; ?>>
										<input type="text" name="couponrecipient[<?php echo $product_code; ?>][<?php echo $i; ?>][email]" maxlength="60" placeholder="Email adresa" value="<?php echo Arr::get($couponrecipient, 'email'); ?>">
										<span class="coupon-product-note"><?php echo Arr::get($cmslabel, 'coupon_email_note'); ?></span>
									</span>
									<span data-couponrecipient_extra="<?php echo $product_code; ?>-<?php echo $i; ?>-address" <?php if ($couponrecipienttype == 'address'): ?>class="active"<?php endif; ?>>
										<input type="text" name="couponrecipient[<?php echo $product_code; ?>][<?php echo $i; ?>][address]" maxlength="220" placeholder="Ime i adresa primatelja" value="<?php echo Arr::get($couponrecipient, 'address'); ?>">
										<span class="coupon-product-note"><?php echo Arr::get($cmslabel, 'coupon_address_note'); ?></span>
									</span>
								</div>
								<?php endfor; ?>
							</div>
						</div>
					<?php endif; ?>
				</div>
				<div class="wp-total">
					<div class="wp-price">
						<?php if ($product_status['total_basic'] > $product_status['total']): ?>
							<div<?php if(!$product_status['discount']): ?>style="display: none"<?php endif; ?> class="wp-price-old"><span class="product_total_basic" data-conversion_type="noconversion" data-shoppingcart_product_total_basic="<?php echo $product_code; ?>"><?php echo Utils::currency_format($product_status['total_basic'] * $currency['exchange'], $currency['display']); ?></span><span class="product_total_basic_second"data-currency_format="full_price_currency" data-display_format="%PRICE%" data-conversion_type="conversion"><?php echo Utils::get_second_pricetag_string($product_status['total_basic'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></span></div>
							<div class="wp-price-discount"><span class="product_total"data-currency_format="full_price_currency" data-conversion_type="noconversion" data-shoppingcart_product_total="<?php echo $product_code; ?>"><?php echo Utils::currency_format($product_status['total'] * $currency['exchange'], $currency['display']); ?></span></div>
						<?php else: ?>
							<div class="wp-price-current"><span class="product_total"data-currency_format="full_price_currency" data-conversion_type="noconversion" data-shoppingcart_product_total="<?php echo $product_code; ?>"><?php echo Utils::currency_format($product_status['total'] * $currency['exchange'], $currency['display']); ?></span></div>
						<?php endif ?>
					</div>					
					<div class="wp-qty-count" data-shoppingcart_product_qty_box="<?php echo $product_code; ?>" <?php if ((float) $product_status['qty'] == 1): ?> style="display: none;"<?php endif; ?>>
						<span class="product_qty"><?php echo $product_status['qty']; ?></span> x <span data-shoppingcart_product_price="<?php echo $product_code; ?>"><?php echo Utils::currency_format($product_status['price'] * $currency['exchange'], $currency['display']); ?></span>
					</div>
					<?php if(!empty($product_status['extra_price_lowest']) AND $product_status['extra_price_lowest'] > 0 AND $product_status['total_basic'] > $product_status['total']): ?>
						<div class="wp-lowest-price">
							<?php echo Arr::get($cmslabel, 'lowest_price'); ?> 
							<?php echo Utils::currency_format($product_status['extra_price_lowest'] * $currency['exchange'], $currency['display']); ?>
						</div>
					<?php endif; ?>
					<?php if (Kohana::config('app.catalog.multitax')): ?>
						<div class="wp-multitax"><?php echo Arr::get($cmslabel, 'tax'); ?> <?php echo ($product_status['tax'] * 100); ?>%</div>
					<?php endif; ?>
				</div>
			</div>
			<?php $i++; ?>
		<?php endforeach; ?>
	</div>
	
	<!-- Totals -->
	<div class="w-totals-cnt">
		<div class="w-totals-title"><?php echo Arr::get($cmslabel, 'cart_totals_title', 'Ukupno u košarici'); ?></div>
		<?php echo View::factory('webshop/widget/total', array('shopping_cart_info' => $shopping_cart_info)); ?>
	</div>
</div>