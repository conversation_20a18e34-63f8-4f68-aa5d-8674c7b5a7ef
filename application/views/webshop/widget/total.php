<?php $mode = (isset($mode) AND $mode == 'preview') ? 'ww-totals' : 'cart-totals'; ?>
<div class="clear <?php echo $mode; ?>">
	<div class="cart-total-without-discount cart_info_total_items_basic_without_discount_box" <?php if ($shopping_cart_info['total_items_basic_without_discount'] == 0): ?>style="display: none"<?php endif; ?>>
		<span class="w-totals-label"><?php echo Arr::get($cmslabel, 'total_minus_discount', 'Ukupno bez popusta'); ?>:</span>
		<span class="w-totals-value cart_info_total_items_basic_without_discount"><?php echo Utils::currency_format($shopping_cart_info['total_items_basic_without_discount'] * $currency['exchange'], $currency['display']); ?><?php echo Utils::get_second_pricetag_string($shopping_cart_info['total_items_basic_without_discount'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></span>
	</div>
	<div class="cart-total-discount cart_info_total_items_basic_discount_box" <?php if ($shopping_cart_info['total_items_basic_discount'] == 0): ?>style="display: none"<?php endif; ?>>
		<span class="w-totals-label"><?php echo Arr::get($cmslabel, 'total_discount', 'Popust'); ?>:</span>
		<span class="w-totals-value cart_info_total_items_basic_discount"><?php echo Utils::currency_format($shopping_cart_info['total_items_basic_discount'] * $currency['exchange'], $currency['display']); ?><?php echo Utils::get_second_pricetag_string($shopping_cart_info['total_items_basic_discount'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></span>
	</div>
	<div class="cart-total-without-tax">
		<span class="w-totals-label"><?php echo Arr::get($cmslabel, 'total_minus_tax', 'Osnovica za obračun PDV-a'); ?>:</span>
		<span class="w-totals-value cart_info_total_items_basic"><?php echo Utils::currency_format($shopping_cart_info['total_items_basic'] * $currency['exchange'], $currency['display']); ?><?php echo Utils::get_second_pricetag_string($shopping_cart_info['total_items_basic'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></span>
		<?php if (Kohana::config('app.catalog.multitax')): ?><small class="cart_info_total_basic_taxranks_description"><?php echo @$shopping_cart_info['total_basic_taxranks_description']; ?></small><?php endif; ?>
	</div>
	<div class="cart-total-tax">
		<span class="w-totals-label"><?php echo Arr::get($cmslabel, 'total_tax', 'Ukupan PDV'); ?><?php if (!Kohana::config('app.catalog.multitax')): ?> (<?php echo $shopping_cart_info['tax'] * 100 ?>%)<?php endif; ?>:</span>
		<span class="w-totals-value cart_info_total_items_tax"><?php echo Utils::currency_format($shopping_cart_info['total_items_tax'] * $currency['exchange'], $currency['display']); ?><?php echo Utils::get_second_pricetag_string($shopping_cart_info['total_items_tax'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></span>
		<?php if (Kohana::config('app.catalog.multitax')): ?><small class="cart_info_total_tax_taxranks_description"><?php echo @$shopping_cart_info['total_tax_taxranks_description']; ?></small><?php endif; ?>
	</div>
	<div class="cart-total-without-shipping">
		<span class="w-totals-label"><?php echo Arr::get($cmslabel, 'total_minus_shipping', 'Ukupno bez dostave'); ?>:</span>
		<span class="w-totals-value value cart_info_total_items_total"><?php echo Utils::currency_format($shopping_cart_info['total_items_total'] * $currency['exchange'], $currency['display']); ?><?php echo Utils::get_second_pricetag_string($shopping_cart_info['total_items_total'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></span>
	</div>
	<?php if (isset($shopping_cart_info['total_extra_payment'])): ?>
		<div class="total_extra_payment_box" <?php if ($shopping_cart_info['total_extra_payment'] == 0): ?>style="display: none"<?php endif; ?>>
			<span class="w-totals-label"><?php echo Arr::get($cmslabel, 'discount'); ?>:</span>
			<span class="w-totals-value total_extra_payment"><?php echo Utils::currency_format($shopping_cart_info['total_extra_payment'] * $currency['exchange'], $currency['display']); ?><?php echo Utils::get_second_pricetag_string($shopping_cart_info['total_extra_payment'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></span>
		</div>
	<?php endif; ?>
	<?php if (isset($shopping_cart_info['total_extra_discount'])): ?>
		<div class="cart_info_total_extra_discount_box" <?php if ($shopping_cart_info['total_extra_discount'] == 0): ?>style="display: none"<?php endif; ?>>
			<span class="w-totals-label"><?php echo Arr::get($cmslabel, 'discount'); ?>:</span>
			<span class="w-totals-value cart_info_total_extra_discount"><?php echo Utils::currency_format($shopping_cart_info['total_extra_discount'] * $currency['exchange'], $currency['display']); ?><?php echo Utils::get_second_pricetag_string($shopping_cart_info['total_extra_discount'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></span>
		</div>
	<?php endif; ?>
	<?php if (isset($shopping_cart_info['total_extra_coupon'])): ?>
		<div class="cart_info_total_extra_coupon_box" <?php if ($shopping_cart_info['total_extra_coupon'] == 0): ?>style="display: none"<?php endif; ?>>
			<span class="w-totals-label"><?php echo Arr::get($cmslabel, 'coupon'); ?>:</span>
			<span class="w-totals-value cart_info_total_extra_coupon"><?php echo Utils::currency_format($shopping_cart_info['total_extra_coupon'] * $currency['exchange'], $currency['display']); ?><?php echo Utils::get_second_pricetag_string($shopping_cart_info['total_extra_coupon'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></span>
		</div>
	<?php endif; ?>
	<?php if (isset($shopping_cart_info['total_extra_coupon_product'])): ?>
		<div class="cart_info_total_extra_coupon_product_box" <?php if ($shopping_cart_info['total_extra_coupon_product'] == 0): ?>style="display: none"<?php endif; ?>>
			<span class="w-totals-label"><?php echo Arr::get($cmslabel, 'coupon_product'); ?>:</span>
			<span class="w-totals-value cart_info_total_extra_coupon_product"><?php echo Utils::currency_format($shopping_cart_info['total_extra_coupon_product'] * $currency['exchange'], $currency['display']); ?><?php echo Utils::get_second_pricetag_string($shopping_cart_info['total_extra_coupon_product'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></span>
		</div>
	<?php endif; ?>
	<?php if (isset($shopping_cart_info['total_extra_affiliate'])): ?>
		<div class="cart_info_total_extra_affiliate_box" <?php if ($shopping_cart_info['total_extra_affiliate'] == 0): ?>style="display: none"<?php endif; ?>>
			<span class="w-totals-label"><?php echo Arr::get($cmslabel, 'affiliate'); ?>:</span>
			<span class="w-totals-value cart_info_total_extra_affiliate"><?php echo Utils::currency_format($shopping_cart_info['total_extra_affiliate'] * $currency['exchange'], $currency['display']); ?><?php echo Utils::get_second_pricetag_string($shopping_cart_info['total_extra_affiliate'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></span>
		</div>
	<?php endif; ?>
	<?php if (isset($shopping_cart_info['total_extra_abandonment'])): ?>
		<div class="cart_info_total_extra_abandonment_box" <?php if ($shopping_cart_info['total_extra_abandonment'] == 0): ?>style="display: none"<?php endif; ?>>
			<span class="w-totals-label"><?php echo Arr::get($cmslabel, 'abandonment'); ?>:</span>
			<span class="w-totals-value cart_info_total_extra_abandonment"><?php echo Utils::currency_format($shopping_cart_info['total_extra_abandonment'] * $currency['exchange'], $currency['display']); ?><?php echo Utils::get_second_pricetag_string($shopping_cart_info['total_extra_abandonment'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></span>
		</div>
	<?php endif; ?>
	<?php if (isset($shopping_cart_info['total_extra_loyalty'])): ?>
		<div class="cart_info_total_extra_loyalty_box" <?php if ($shopping_cart_info['total_extra_loyalty'] == 0): ?>style="display: none"<?php endif; ?>>
			<span class="w-totals-label"><?php echo Arr::get($cmslabel, 'loyalty', 'Kartica kupca'); ?> <span class="cart_info_total_extra_loyalty_discount_percent"><?php echo Arr::get($shopping_cart_info, 'total_extra_loyalty_discount_percent', 0); ?></span>%:</span>
			<span class="w-totals-value cart_info_total_extra_loyalty"><?php echo Utils::currency_format($shopping_cart_info['total_extra_loyalty'] * $currency['exchange'], $currency['display']); ?><?php echo Utils::get_second_pricetag_string($shopping_cart_info['total_extra_loyalty'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></span>
		</div>
	<?php endif; ?>
	<?php if (isset($shopping_cart_info['total_extra_cc_discount'])): ?>
		<div class="cart_info_total_extra_cc_discount_box" <?php if ($shopping_cart_info['total_extra_cc_discount'] == 0): ?>style="display: none"<?php endif; ?>>
			<span class="w-totals-label"><?php echo Arr::get($cmslabel, 'cc_discount'); ?>:</span>
			<span class="w-totals-value cart_info_total_extra_cc_discount"><?php echo Utils::currency_format($shopping_cart_info['total_extra_cc_discount'] * $currency['exchange'], $currency['display']); ?><?php echo Utils::get_second_pricetag_string($shopping_cart_info['total_extra_cc_discount'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></span>
		</div>
	<?php endif; ?>
	<?php if (isset($shopping_cart_info['total_extra_cover'])): ?>
		<div class="cart_info_total_extra_cover_box" <?php if ($shopping_cart_info['total_extra_cover'] == 0): ?>style="display: none"<?php endif; ?>>
			<span class="w-totals-label"><?php echo Arr::get($cmslabel, 'cover'); ?>:</span>
			<span class="w-totals-value cart_info_total_extra_cover"><?php echo Utils::currency_format($shopping_cart_info['total_extra_cover'] * $currency['exchange'], $currency['display']); ?><?php echo Utils::get_second_pricetag_string($shopping_cart_info['total_extra_cover'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></span>
		</div>
	<?php endif; ?>
	<?php if (isset($shopping_cart_info['total_extra_relatedlist'])): ?>
		<div class="cart_info_total_extra_relatedlist_box" <?php if ($shopping_cart_info['total_extra_relatedlist'] == 0): ?>style="display: none"<?php endif; ?>>
			<span class="w-totals-label"><?php echo Arr::get($cmslabel, 'relatedlist'); ?>:</span>
			<span class="w-totals-value cart_info_total_extra_relatedlist"><?php echo Utils::currency_format($shopping_cart_info['total_extra_relatedlist'] * $currency['exchange'], $currency['display']); ?><?php echo Utils::get_second_pricetag_string($shopping_cart_info['total_extra_relatedlist'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></span>
		</div>
	<?php endif; ?>
	<?php if (isset($shopping_cart_info['total_extra_supplement'])): ?>
		<div class="cart_info_total_extra_supplement_box" <?php if ($shopping_cart_info['total_extra_supplement'] == 0): ?>style="display: none"<?php endif; ?>>
			<span class="w-totals-label"><?php echo Arr::get($cmslabel, 'supplement'); ?>:</span>
			<span class="w-totals-value cart_info_total_extra_supplement"><?php echo Utils::currency_format($shopping_cart_info['total_extra_supplement'] * $currency['exchange'], $currency['display']); ?><?php echo Utils::get_second_pricetag_string($shopping_cart_info['total_extra_supplement'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></span>
		</div>
	<?php endif; ?>
	<div class="cart-total-shipping">
		<span class="w-totals-label"><?php echo Arr::get($cmslabel, 'shipping', 'Dostava'); ?>:</span>
		<span class="w-totals-value cart_info_total_extra_shipping"><?php if ($shopping_cart['total_extra_shipping'] == 0): ?><span class="w-totals-label-free"><?php echo Arr::get($cmslabel, 'free_shipping', 'Besplatna'); ?></span><?php else: ?><?php echo Utils::currency_format($shopping_cart['total_extra_shipping'] * $currency['exchange'], $currency['display']); ?><?php echo Utils::get_second_pricetag_string($shopping_cart['total_extra_shipping'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?><?php endif; ?></span>
	</div>
	<div class="ww-total cart-total">
		<span class="w-totals-label"><?php echo Arr::get($cmslabel, 'total_to_pay', 'Sveukupno'); ?>:</span>
		<span class="w-totals-value value cart_info_total"><?php echo Utils::currency_format($shopping_cart_info['total'] * $currency['exchange'], $currency['display']); ?><?php echo Utils::get_second_pricetag_string($shopping_cart_info['total'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></span>
	</div>
</div>