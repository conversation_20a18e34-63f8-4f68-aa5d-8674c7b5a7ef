<?php
$shippings = Webshop::shippings(['lang' => $info['lang']]);
$selected_shipping = $shopping_cart_info['selected_shipping'];

$shipping_address = Html::customer_address($customer_data, $info['lang'], Arr::extract($cmslabel, ['oib']));
$shipping_address .= '<br><a href="'.Utils::app_absolute_url($info['lang'], 'webshop', 'shipping').'#webshop_form" class="btn-change-address"><span>'.Arr::get($cmslabel, 'change_shipping_address', 'Promijeni adresu dostave').'</span></a>';
?>

<?php foreach ($shippings AS $shipping): ?>
	<div class="shipping-row <?php if (!isset($shopping_cart_info['available_shippings_option'][$shipping['id']])): ?>hidden<?php endif; ?><?php if(count($shippings) <= 1): ?> single-shipping<?php endif; ?>">
		<?php $shipping_selected = ($selected_shipping == $shipping['id'] AND isset($shopping_cart_info['available_shippings_option'][$shipping['id']])); ?>
		<span class="shipping_info shipping_info_<?php echo $shipping['id']; ?> price cart_info_total_extra_shipping" data-ignore_showhide="1" <?php if ($shipping_selected): ?>style="display: none"<?php endif; ?>><?php if (!$shopping_cart_info['total_extra_shipping']): ?><?php echo Arr::get($cmslabel, 'free'); ?><?php else: ?><?php echo Utils::currency_format($shopping_cart_info['total_extra_shipping'] * $currency['exchange'], $currency['display']); ?><?php echo Utils::get_second_pricetag_string($shopping_cart_info['total_extra_shipping'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?><?php endif; ?></span>
		<input type="radio" name="shipping" value="<?php echo $shipping['id']; ?>" id="field-shipping-<?php echo $shipping['id']; ?>" <?php if ($shipping_selected): ?>checked<?php endif; ?><?php if (!isset($shopping_cart_info['available_shippings_option'][$shipping['id']])): ?>disabled<?php endif; ?>>
		<label for="field-shipping-<?php echo $shipping['id']; ?>"><?php echo $shipping['title']; ?></label>
		<div class="shipping-data shipping_info shipping_info_<?php echo $shipping['id']; ?>" <?php if (!$shipping_selected): ?>style="display: none"<?php endif; ?>>
			<?php if ($shipping['show_shipping_address']): ?>
				<?php if ($shipping['description']): ?>
					<?php echo $shipping['description']; ?><br/><br/>
				<?php endif; ?>
				<?php echo $shipping_address; ?>
			<?php else: ?>
				<?php echo $shipping['description']; ?>
			<?php endif; ?>
			<?php if (isset($shipping['widget_content']) AND $shipping['widget_content']): ?>
				<?php echo $shipping['widget_content']; ?>
			<?php endif; ?>
		</div>
	</div>
<?php endforeach; ?>

<div class="form-label checkout-last">
	<p class="field step3-field-message">
		<textarea id="field-message" name="message" cols="40" rows="8" class="field_text field_text_message"><?php echo Arr::get($customer_data, 'message'); ?></textarea>
		<label for="field-message" class="label-message"><?php echo Arr::get($cmslabel, 'checkout_message_note', 'Napomena...'); ?></label>
		<span id="field-error-message" class="field_error error" style="display: none"></span>
	</p>
</div>