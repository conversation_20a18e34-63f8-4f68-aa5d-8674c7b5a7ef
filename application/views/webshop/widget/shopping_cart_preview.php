<div class="shoppingcart_items_small clear">

    <!-- Cart items -->
    <div class="ww-preview-items shopping_cart_preview_items">
        <table class="ww-preview-table">
            <?php foreach ($shopping_cart_products as $product_code => $product_data): ?>
                <?php $product_status = $shopping_cart_status[$product_code]; ?>
                <tr id="product-<?php echo $product_code; ?>">
                    <td class="ww-image">
                        <a href="<?php echo $product_data['url']; ?>">
                            <img <?php echo Thumb::generate($product_data['main_image'], array('width' => 75, 'height' => 120, 'html_tag' => TRUE, 'default_image' => '/media/images/no-image-50.jpg')); ?> alt="<?php echo $product_data['title']; ?>" />
                        </a>
                    </td>
                    <td class="ww-cnt">
                        <div class="ww-title">
                            <a href="<?php echo $product_data['url']; ?>"><?php echo $product_data['title']; ?></a>
                        </div>
                        <div class="ww-price">
                            <?php if ($product_status['total_basic'] > $product_status['total']): ?>
                                <span class="ww-old-price"><span class="product_total_basic" data-currency_format="full_price_currency" data-conversion_type="noconversion"><?php echo Utils::currency_format($product_status['total_basic'] * $currency['exchange'], $currency['display']); ?></span></span>
                                <span class="ww-discount-price"><span class="product_total" data-currency_format="full_price_currency" data-conversion_type="noconversion"><?php echo Utils::currency_format($product_status['total'] * $currency['exchange'], $currency['display']); ?></span></span>
                            <?php else: ?>
                                <span class="ww-current-price"><span class="product_total" data-currency_format="full_price_currency" data-conversion_type="noconversion"><?php echo Utils::currency_format($product_status['total'] * $currency['exchange'], $currency['display']); ?></span></span>
                            <?php endif ?>
                            <?php if(!empty($product_status['extra_price_lowest']) AND $product_status['extra_price_lowest'] > 0 AND $product_status['total_basic'] > $product_status['total']): ?>
                                <div class="ww-lowest-price">
                                    <?php echo Arr::get($cmslabel, 'lowest_price'); ?> 
                                    <?php echo Utils::currency_format($product_status['extra_price_lowest'] * $currency['exchange'], $currency['display']); ?>
                                 </div>
                            <?php endif; ?>
                        </div>
                        <?php if ($product_data['variation']): ?>
                            <div class="ww-variations">
                                <?php if ($product_data['variation_attributes']): ?>
                                    <?php $variation_items = explode(',', $product_data['variation_attributes']) ?>
                                    <?php foreach ($variation_items as $variation_item): ?>
                                        <p><?php echo $variation_item ?></p>
                                    <?php endforeach; ?>
                                <?php elseif ($product_data['variation_title']): ?>
                                    <?php echo $product_data['variation_title']; ?>
                                <?php elseif ($product_data['variation_code']): ?>
                                    <?php echo $product_data['variation_code']; ?>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </td>
                </tr>
            <?php endforeach; ?>
        </table>
    </div>

    <!-- Totals -->
    <?php echo View::factory('webshop/widget/total', ['shopping_cart_info' => $shopping_cart, 'mode' => 'preview']); ?>

    <!-- Min order alert -->
    <?php if (isset($shopping_cart['total_extra_shipping_min_total_error']) AND $shopping_cart['total_extra_shipping_min_total_error']): ?>
        <div class="ww-minprice minprice-tooltip" style="display:none;">
            <?php
            echo str_replace(array(
                '%TOTAL_MIN%',
                '%TOTAL_MISSING%',
                    ), array(
                Utils::currency_format($shopping_cart['total_extra_shipping_min_total'] * $currency['exchange'], $currency['display']).Utils::get_second_pricetag_string($shopping_cart['total_extra_shipping_min_total'], ['currency_code' => $currency['code'], 'display_format' => 'standard']),
                Utils::currency_format($shopping_cart['total_extra_shipping_min_total_missing'] * $currency['exchange'], $currency['display']).Utils::get_second_pricetag_string($shopping_cart['total_extra_shipping_min_total_missing'], ['currency_code' => $currency['code'], 'display_format' => 'standard']),
                    ), Arr::get($cmslabel, 'minimal_order_price_full', ''));

            ?>
        </div>
    <?php endif; ?>

    <a class="btn ww-btn-view" href="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', ''); ?>"><span><?php echo Arr::get($cmslabel, 'view_shopping_cart'); ?></span></a>
    <a class="btn btn-green ww-btn-finish cart-finish-shopping" href="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', 'payment'); ?>"><span><?php echo Arr::get($cmslabel, 'finish_shopping'); ?></span></a>
    <div class="empty_shopping_cart" style="display: none;"><?php echo Arr::get($cmslabel, 'empty_shopping_cart'); ?></div>
</div>