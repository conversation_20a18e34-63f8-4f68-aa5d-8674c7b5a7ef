<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-thankyou<?php $this->endblock('page_class'); ?>

<?php $this->block('main_class'); ?>thankyou-main<?php $this->endblock('main_class'); ?>

<?php $this->block('content_layout'); ?>
	<h1>
		<?php echo Arr::get($cms_page, 'seo_h1'); ?>
		<?php if($user): ?>
			<?php echo $user->first_name; ?>
		<?php endif; ?>
	</h1>
	<div class="checkout-cnt">
		<?php if ($order): ?>
			<?php echo Arr::get($cms_page, 'content'); ?>
			
			<!-- Autogenerated terms of purchase -->
			<?php $terms_pdf = Cms::page($info['lang'], 'webshop_terms', 'code', 'content_pdf'); ?>
			<?php if (Arr::get($terms_pdf, 'content_pdf')): ?>
				<p><a class="btn btn-download btn-download-pdf" href="<?php echo Utils::file_url($terms_pdf['content_pdf']);; ?>" target="_blank"><span><?php echo Arr::get($cmslabel, 'thank_you_download_terms_pdf', 'Preuzmite predugovorne obavijesti'); ?></span></a></p>
			<?php endif; ?>

			<!-- Shipping info & Invoice download -->
			<div class="invoice-container">
				<p><?php echo Arr::get($cmslabel, 'choosen_shipping', 'Odabran način dostave'); ?> "<?php echo $order->shipping->description($info['lang']); ?>".<br><?php echo Arr::get($cmslabel, 'order_email', '"E-mail sa svim detaljima poslali smo na Vaš email'); ?> <strong><?php echo $order->email; ?></strong>.</p>
				<p>
					<?php $payment_label = ($order AND $order->show_payment_transfer()) ? Arr::get($cmslabel, 'order_download_invoice_payment', 'Preuzmite potvrdu narudžbe i upute za plaćanje') : Arr::get($cmslabel, 'order_download_invoice', 'Preuzmite potvrdu narudžbe'); ?>
					<a href="<?php echo $order->get_absolute_url(); ?>?mode=pdf" class="btn btn-big btn-download-invoice" target="_blank"><?php echo $payment_label; ?></a>
				</p>
			</div>

			<?php if ($order AND $order->show_payment_transfer()): ?>
				<div class="ty-payment-transfer-cnt">
					<?php echo View::factory('webshop/widget/payment_transfer', ['order' => $order]); ?>
					<p><a class="btn btn-print" href="<?php echo $order->get_absolute_url(); ?>?mode=payment" target="_blank"><span><?php echo Arr::get($cmslabel, 'print_payment', 'Ispiši uplatnicu'); ?></span></a></p>
				</div>
			<?php endif; ?>

			<!-- Signup form -->
			<?php if (Kohana::config('app.auth.frontend') AND !$order->user_id AND !$user AND !User::user_exist($order->email)): ?>
				<div class="thank-you-wrapper">
					<div class="thank-you-content">
						<?php echo Arr::get($cmslabel, 'thank_you_signup'); ?>
					</div>
					<div class="thank-you-login">
						<form class="form-inline form-label ajax_siteform ajax_siteform_loading" action="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'signup', FALSE); ?>" method="post" name="signup" id="thankyou_signup">
							<input type="hidden" name="form_mode" value="thankyou_signup">  
							<p class="field field-email">
								<span class="input">
									<input type="text" id="field-email" name="email" value="<?php echo $order->email; ?>">
									<label for="field-email"><?php echo Arr::get($cmslabel, 'your_email'); ?></label>
								</span>
								<span id="field-error-email" class="field_error error" style="display: none"></span>
							</p>
							<p class="field field-password">
								<span class="input">
									<input type="password" name="password" id="field-password" data-typetoggle="#show-password">
									<label for="field-password"><?php echo Arr::get($cmslabel, 'enter_password'); ?></label>
								</span>
								<span id="field-error-password" class="field_error error" style="display: none"></span>
							</p>
							<p class="field field-show-password">
								<input type="checkbox" id="show-password">
								<label for="show-password"><?php echo Arr::get($cmslabel, 'show_password'); ?></label>
							</p>
							
							<p><button type="submit" class="btn"><?php echo Arr::get($cmslabel, 'save'); ?></button></p>
						</form>
						<div id="thankyou_signup_success" style="display: none"><?php echo Arr::get($cmslabel, 'success_signup'); ?></div>
					</div>
					<div class="thank-you-safe">
						<?php echo Arr::get($cmslabel, 'safe_purchase_thankyou'); ?>
					</div>
				</div>
			<?php endif; ?>
		<?php endif; ?>

		
	</div>
<?php $this->endblock('content_layout'); ?>

<?php $this->block('extrabody'); ?>
	<?php if ($order AND $order_first AND Kohana::$environment === 1): ?>
		<?php /* GOOGLE ECOMMERCE TRACKING - DEFAULTNO ISKLJUCENO SVIMA - UKLJUCUJE SE PO PRIHVACANJU PONUDE */ ?>
		<?php //echo View::factory('webshop/widget/ecommerce_universal_tracking', array('order' => $order)); ?>
		<?php //echo View::factory('webshop/widget/ecommerce_universal_tracking', array('order' => $order, 'mode' => 2)); ?>
		<?php //echo View::factory('webshop/widget/facebook_tracking', array('order' => $order)); ?>
		<?php //echo View::factory('webshop/widget/dynamic_remarketing', array('order' => $order, 'conversion_id' => '929235278')); ?>
	<?php endif; ?>
<?php $this->endblock('extrabody'); ?>