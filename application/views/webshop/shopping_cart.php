<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-shopping-cart<?php $this->endblock('page_class'); ?>

<?php $this->block('content_layout'); ?>

<h1 class="w-title"><?php echo Arr::get($cms_page, 'seo_h1'); ?><?php if(!empty($products) AND is_array($products) AND count($products)): ?><span class="w-counter<?php if ($shopping_cart['total_items']): ?> active<?php endif; ?>" data-shoppingcart_active="1">(<span class="cart_info_item_count"><?php echo $shopping_cart['item_count']; ?></span>)</span><?php endif; ?></h1>

<?php if (!empty($products) AND is_array($products) AND count($products)): ?>
	<?php echo Arr::get($cms_page, 'content'); ?>

	<div class="cart-container clear" id="view_cart">
		<div class="cart-left">
			<form action="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', 'set_qty', FALSE); ?>" method="post" name="shopping_cart">
				<!-- Cart table -->
				<div class="w-table">
					<?php $i=1; ?>
					<?php foreach ($products as $product_code => $product_data): ?>
						<?php $product_status = $products_status[$product_code]; ?>
                        <?php
                            $package_qty = (!empty($product_data['package_qty']) AND (int)$product_data['package_qty'] > 0) ? (int)$product_data['package_qty'] : 1;
                            $package_qty_decimals = 0;
                        ?>
                        <div class="wp fz0" id="product_details_<?php echo $product_code; ?>">
							<input type="hidden" name="product_price" value="<?php echo $product_status['price'] ?>" />
							<input type="hidden" name="product_total" value="<?php echo $product_status['total'] ?>" />
							<div class="wp-image">
								<figure>
									<img <?php echo Thumb::generate($product_data['main_image'], array('width' => 100, 'height' => 147, 'html_tag' => TRUE, 'default_image' => '/media/images/no-image-200.jpg')); ?> alt="<?php echo $product_data['title']; ?>" />
								</figure>
								<a class="wp-btn-delete wp-btn-delete-mobile" href="javascript:cmswebshop.shopping_cart.change_qty('<?php echo $product_code; ?>', 'remove');"><span><?php echo Arr::get($cmslabel, 'remove_product', 'Ukloni'); ?></span></a>
							</div>
							<div class="wp-cnt-right">	
								<div class="wp-cnt">
									<h2 class="wp-title">
										<a href="<?php echo $product_data['url']; ?>"><?php echo $product_data['title']; ?></a>
										<?php if ($product_data['attributes']): ?>
											<span class="wp-attribute"><?php echo $product_data['attributes']; ?></span>
										<?php endif; ?>
									</h2>
									<div class="wp-code"><?php echo Arr::get($cmslabel, 'code', 'Šifra'); ?>: <?php echo (!empty($product_data['variation_code'])) ? $product_data['variation_code'] : $product_data['code']; ?></div>
									<?php if (!empty($product_data['variation'])): ?>
										<div class="wp-variations">
											<?php if ($product_data['variation_attributes']): ?>
												<?php $variation_items = explode(',', $product_data['variation_attributes']) ?>
												<?php foreach ($variation_items as $variation_item): ?>
													<p><?php echo $variation_item; ?></p>
												<?php endforeach; ?>
											<?php elseif ($product_data['variation_title']): ?>
												<?php echo $product_data['variation_title']; ?>
											<?php elseif ($product_data['variation_code']): ?>
												<?php echo $product_data['variation_code']; ?>
											<?php endif; ?>
										</div>
									<?php endif; ?>

                                    <?php if (Arr::get($product_data, 'coupon_price')): ?>
										<?php echo Arr::get($cmslabel, 'coupon_value', 'Vrijednost kupona'); ?>: <?php echo $product_data['coupon_price']; ?>
									<?php elseif (Arr::get($product_data, 'bonus_total')): ?>
										<?php echo Arr::get($cmslabel, 'bonus_total_value'); ?>: <?php echo $product_data['bonus_total']; ?>
									<?php elseif ($product_data['type'] == 'download'): ?>
										<?php echo Arr::get($cmslabel, 'download_files', 'Preuzimanje datoteka'); ?>
									<?php endif; ?>
									
									<a class="wp-btn-delete" href="javascript:cmswebshop.shopping_cart.change_qty('<?php echo $product_code; ?>', 'remove');"><span><?php echo Arr::get($cmslabel, 'remove_product', 'Obriši'); ?></span></a>
								</div>
								<div class="wp-qty">
									<a class="wp-btn-qty wp-btn-dec" href="javascript:cmswebshop.shopping_cart.change_qty('<?php echo $product_code; ?>', '-', 0, 1, <?php echo $package_qty; ?>, 0, <?php echo $package_qty; ?>, <?php echo $package_qty_decimals; ?>);"></a>
									<input class="wp-input-qty product_qty_input" tabindex="<?php echo $i; ?>" type="text" name="qty[<?php echo $product_code; ?>]" value="<?php echo $product_status['qty']; ?>" />
									<a class="wp-btn-qty wp-btn-inc" href="javascript:cmswebshop.shopping_cart.change_qty('<?php echo $product_code; ?>', '+', 0, 1, <?php echo $package_qty; ?>, 0, <?php echo $package_qty; ?>, <?php echo $package_qty_decimals; ?>);"></a>
                                    <?php $unit = (!empty($product_data['unit'])) ? $product_data['unit'] : Arr::get($cmslabel, 'unit', 'kom'); ?>
									<span class="wp-unit"><?php echo $unit; ?></span>
									<span class="wp-message product_message product_message_<?php echo $product_code; ?>" style="display: none"></span>	
								</div>
								<div class="wp-total">
									<?php if ($product_status['total_basic'] > $product_status['total']): ?>
										<div class="wp-price-old"><span class="product_total_basic" data-currency_format="full_price_currency" data-conversion_type="noconversion"><?php echo Utils::currency_format($product_status['total_basic'] * $currency['exchange'], $currency['display']); ?></span></div>
										<div class="wp-price-discount"><span class="product_total" data-currency_format="full_price_currency" data-conversion_type="noconversion"><?php echo Utils::currency_format($product_status['total'] * $currency['exchange'], $currency['display']); ?></span></div>
										<div class="wp-price-old"><span class="product_total_basic_second" data-currency_format="full_price_currency" data-display_format="%PRICE%" data-conversion_type="conversion"><?php echo Utils::get_second_pricetag_string($product_status['total_basic'], ['currency_code' => $currency['code'], 'display_format' => 'none']); ?></span></div>
										<div class="wp-price-discount"><span class=" product_total_second" data-currency_format="full_price_currency" data-display_format="%PRICE%" data-conversion_type="conversion"><?php echo Utils::get_second_pricetag_string($product_status['total'], ['currency_code' => $currency['code'], 'display_format' => 'none']); ?></span></div>
									<?php else: ?>
										<div class="wp-price-current product_total" data-currency_format="full_price_currency" data-conversion_type="noconversion"><?php echo Utils::currency_format($product_status['total'] * $currency['exchange'], $currency['display']); ?></div>
										<div class="wp-price-current product_total_second" data-currency_format="full_price_currency" data-display_format="%PRICE%" data-conversion_type="conversion"><?php echo Utils::get_second_pricetag_string($product_status['total'], ['currency_code' => $currency['code'], 'display_format' => 'none']); ?></div>
									<?php endif ?>
									<div class="wp-qty-count" data-shoppingcart_product_qty_box="<?php echo $product_code; ?>" <?php if ((float) $product_status['qty'] == 1): ?> style="display: none;"<?php endif; ?>>
										<span class="product_qty"><?php echo $product_status['qty']; ?></span> x <?php echo Utils::currency_format($product_status['price'] * $currency['exchange'], $currency['display']); ?><?php echo Utils::get_second_pricetag_string($product_status['price'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
									</div>
									<?php if(!empty($product_status['extra_price_lowest']) AND $product_status['extra_price_lowest'] > 0 AND $product_status['total_basic'] > $product_status['total']): ?>
										<span class="wp-lowest-price-outer">
											<div class="wp-lowest-price">
												<?php echo Arr::get($cmslabel, 'wp_lowest_price'); ?> 
												<span><?php echo Utils::currency_format($product_status['extra_price_lowest'] * $currency['exchange'], $currency['display']); ?>
												<?php echo Utils::get_second_pricetag_string($product_status['extra_price_lowest'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></span>
											</div>
										</span>
									<?php endif; ?>
									<?php if (Kohana::config('app.catalog.multitax')): ?>
										<div class="wp-multitax"><?php echo Arr::get($cmslabel, 'tax'); ?> <?php echo ($product_status['tax'] * 100); ?>%</div>
									<?php endif; ?>
								</div>
							</div>
						</div>
						<?php $i++; ?>
					<?php endforeach; ?>	
				</div>
			</form>
		</div>

		<div class="cart-right">
			<!-- Totals -->
			<div class="cart-total-title"><?php echo Arr::get($cmslabel, 'cart_total'); ?></div>
			<?php echo View::factory('webshop/widget/total', array('shopping_cart_info' => $shopping_cart_info)); ?>

			<!-- Min order alert -->
			<?php if (isset($shopping_cart_info['total_extra_shipping_min_total_error']) AND $shopping_cart_info['total_extra_shipping_min_total_error']): ?>			
				<div class="w-minprice minprice-tooltip" style="display:none;">	
					<?php echo str_replace(array(
							'%TOTAL_MIN%', 
							'%TOTAL_MISSING%', 
							), 
						array(
							Utils::currency_format($shopping_cart_info['total_extra_shipping_min_total'] * $currency['exchange'], $currency['display']).Utils::get_second_pricetag_string($shopping_cart_info['total_extra_shipping_min_total'], ['currency_code' => $currency['code'], 'display_format' => 'standard']),
							Utils::currency_format($shopping_cart_info['total_extra_shipping_min_total_missing'] * $currency['exchange'], $currency['display']).Utils::get_second_pricetag_string($shopping_cart_info['total_extra_shipping_min_total_missing'], ['currency_code' => $currency['code'], 'display_format' => 'standard']),
						),
						Arr::get($cmslabel, 'minimal_order_price_full', ''));
					?>
				</div>
			<?php endif; ?>

			<!-- Finish shopping -->
			<a class="btn btn-green w-btn-finish cart-finish-shopping" href="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', 'payment'); ?>#webshop_form"><span><?php echo Arr::get($cmslabel, 'finish_shopping', 'Dovrši kupovinu'); ?></span></a>
		</div>
	</div>
	<div id="empty_shopping_cart" class="empty-cart" style="display: none;"><?php echo Arr::get($cmslabel, 'empty_shopping_cart'); ?></div>
<?php else: ?>
	<div class="empty-cart"><?php echo Arr::get($cmslabel, 'empty_shopping_cart'); ?></div>
<?php endif; ?>
<?php $this->endblock('content_layout'); ?>