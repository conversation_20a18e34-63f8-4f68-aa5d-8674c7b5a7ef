<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', array('cms_page' => isset($cms_page) ? $cms_page : array())); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-checkout page-payment<?php $this->endblock('page_class'); ?>

<?php $this->block('header'); ?> 
	<div class="header-checkout">
		<a href="<?php echo Utils::homepage($info['lang']); ?>" class="logo"></a>
		<div class="hc-steps clear">
			<!-- Steps -->
			<?php echo View::factory('webshop/widget/step', array('current_step' => 3)); ?>

			<div class="hc-label"><?php echo Arr::get($cmslabel, 'checkout_label'); ?></div>
		</div>
	</div>
<?php $this->endblock('header'); ?>

<?php $this->block('main_class'); ?>checkout-main checkout-main-payment<?php $this->endblock('main_class'); ?>

<?php $this->block('content_layout'); ?>
<div class="wc-container">
	<form class="step3 form-inline checkout-form ajax_siteform ajax_siteform_loading" action="#webshop_form" method="post" name="webshop" id="webshop_form">
		
		<!-- Column 1 -->
		<div class="wc-col wc-col1 wc-step3-col1">
			<h2 class="wc-subtitle"><?php echo Arr::get($cms_page, 'seo_h1'); ?></h2>

			<?php if (!empty($errors) AND is_array($errors) AND count($errors) > 0): ?>
				<p class="error global-error"><?php echo Arr::get($cmslabel, 'form_validation_error'); ?></p>
			<?php endif; ?>

			<!-- Payment options -->
			<div class="payment-options">
				<?php foreach ($customer_fields as $field): ?>
					<?php if (isset($errors[$field][0]) AND ($error = $errors[$field][0])): ?>
						<p class="error"><?php echo Arr::get($cmslabel, "error_{$error}", $error); ?></p>
					<?php endif; ?>
					<div class="field-<?php echo $field; ?>">
						<label for="field-<?php echo $field; ?>" class="label-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, $field); ?><?php if (in_array($field, $request_fields)): ?> *<?php endif; ?></label>
						<?php if ($field == 'payment'): ?>
							<?php if (!empty($available_payments_none) AND is_array($available_payments_none) AND count($available_payments_none) == 1): ?>
								<div class="cart_info_payment_box_normal <?php if ($shopping_cart_info['total'] > 0): ?>active<?php endif; ?>">
									<?php echo Form::select_as_radio2($field, $available_payments, ($shopping_cart_info['total'] > 0) ? $data->payment->id : NULL); ?>
								</div>
								<div class="cart_info_payment_box_0 <?php if ($shopping_cart_info['total'] == 0): ?>active<?php endif; ?>">
									<?php echo Form::select_as_radio2($field, $available_payments_none, ($shopping_cart_info['total'] == 0) ? key($available_payments_none) : NULL); ?>
								</div>
							<?php else: ?>
								<?php echo Form::select_as_radio2($field, $available_payments, $data->payment->id); ?>
							<?php endif; ?>
						<?php else: ?>
							<?php echo $data->input($field, 'form'); ?>
						<?php endif; ?>
					</div>
				<?php endforeach; ?>

				<?php echo Arr::get($cms_page, 'content'); ?>
			</div>

			<!-- Shopping cart -->
			<?php echo View::factory('webshop/widget/shopping_cart_items_small', array('shopping_cart_info' => $shopping_cart_info, 'products' => $products, 'products_status' => $products_status)); ?>		
		</div>
		
		<!-- Column 2 -->
		<div class="wc-col wc-col2 wc-step3-col2">
			<div class="col2-section-shipping">
				<h2 class="wc-subtitle"><?php echo Arr::get($cmslabel, 'shipping_type', 'Odaberite način dostave'); ?></h2>
				<div class="global_error global-error"<?php if (empty($errors) OR (isset($errors) AND is_array($errors) AND !count($errors))): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, 'form_validation_error'); ?></div>

				<?php if ( ! Kohana::config('app.webshop.bill_on_shipping_step')): ?>
					<fieldset>
						<?php if (!empty($customer_bill_fields) AND is_array($customer_bill_fields) AND count($customer_bill_fields)): ?>
							<?php foreach ($customer_bill_fields as $field): ?>
								<?php if (isset($errors[$field][0]) AND ($error = $errors[$field][0])): ?>
									<p class="error"><?php echo Arr::get(@$cmslabel, "error_{$error}", $error); ?></p>
								<?php endif; ?>
								<p class="field-<?php echo $field; ?>">
									<label for="field-<?php echo $field; ?>"><?php echo @$cmslabel[substr($field, 2, strlen($field))]; ?><?php if (in_array($field, $request_fields)): ?> *:<?php endif; ?></label>
									<?php echo $data->input($field, 'form'); ?>
								</p>
							<?php endforeach; ?>
							<?php foreach ($default_bill_value as $field => $value): ?>
								<input type="hidden" name="default-<?php echo $field; ?>" value="<?php echo $value; ?>" />
							<?php endforeach; ?>
						<?php else: ?>
							<?php echo Arr::get($cmslabel, 'same_as_shipping'); ?>
						<?php endif; ?>
					</fieldset>
			
				<?php endif; ?>		
						
				<?php if (Kohana::config('app.webshop.review_order_on_payment_step')): ?>
					<fieldset class="section-shipping">
						<?php echo View::factory('webshop/widget/shipping', array('shopping_cart_info' => $shopping_cart_info, 'customer_data' => $customer_data)); ?>				
					</fieldset>
					
					<?php if (Arr::get($customer_data, 'b_same_as_shipping') != 1 AND Arr::get($customer_data, 'b_first_name')): ?>
						<fieldset class="section-bill-address">
							<strong><?php echo Arr::get($cmslabel, 'bill_address'); ?></strong><br>
							<?php echo Arr::get($customer_data, 'b_first_name'); ?> <?php echo Arr::get($customer_data, 'b_last_name'); ?><?php if (Arr::get($customer_data, 'b_oib')): ?>, <?php echo Arr::get($cmslabel, 'oib'); ?>: <?php echo Arr::get($customer_data, 'b_oib'); ?><?php endif; ?><?php if(Arr::get($customer_data, 'b_address')): ?>, <?php echo Arr::get($customer_data, 'b_address'); ?><?php endif; ?>, <?php echo Arr::get($customer_data, 'b_zipcode'); ?> <?php echo Arr::get($customer_data, 'b_city'); ?><?php if(Arr::get($customer_data, 'b_country')): ?>,<?php endif; ?> <?php echo (Arr::get($customer_data, 'b_country')) ? Jelly::query('webshopcountry', Arr::get($customer_data, 'b_country'))->select()->description($info['lang']) : ''; ?>
							<?php if (Arr::get($customer_data, 'b_email')): ?>, <?php echo Arr::get($customer_data, 'b_email'); ?><?php endif; ?>
						</fieldset>
					<?php endif; ?>

					<?php if (Arr::get($customer_data, 'b_r1') == 1): ?>
						<fieldset class="section-bill-address">
							<strong><?php echo Arr::get($cmslabel, 'r1'); ?></strong>
							<br/><?php echo Arr::get($cmslabel, 'company_name'); ?>: <?php echo Arr::get($customer_data, 'b_company_name'); ?>
							<br/><?php echo Arr::get($cmslabel, 'company_oib'); ?>: <?php echo Arr::get($customer_data, 'b_company_oib'); ?>
							<br/><?php echo Arr::get($cmslabel, 'company_address'); ?>: <?php echo Arr::get($customer_data, 'b_company_address'); ?>
						</fieldset>
					<?php elseif (Arr::get($customer_data, 'b_company_name')): ?>
						<fieldset class="section-bill-address">
							<?php echo Arr::get($cmslabel, 'company_name'); ?>: <?php echo Arr::get($customer_data, 'b_company_name'); ?>
							<br/><?php echo Arr::get($cmslabel, 'company_oib'); ?>: <?php echo Arr::get($customer_data, 'b_company_oib'); ?>
							<br/><?php echo Arr::get($cmslabel, 'company_address'); ?>: <?php echo Arr::get($customer_data, 'b_company_address'); ?>
						</fieldset>
					<?php endif; ?>

					<div class="clear"></div>
				<?php endif; ?>	
			</div>
			<div class="section">
				<?php if (isset($shopping_cart_info['total_extra_shipping_min_total_error']) AND $shopping_cart_info['total_extra_shipping_min_total_error']): ?>			
					<div class="minprice-tooltip">	
					<?php echo str_replace(array(
							'%TOTAL_MIN%', 
							'%TOTAL_MISSING%', 
							), 
						array(
							Utils::currency_format($shopping_cart_info['total_extra_shipping_min_total'] * $currency['exchange'], $currency['display']).Utils::get_second_pricetag_string($shopping_cart_info['total_extra_shipping_min_total'], ['currency_code' => $currency['code'], 'display_format' => 'standard']),
							Utils::currency_format($shopping_cart_info['total_extra_shipping_min_total_missing'] * $currency['exchange'], $currency['display']).Utils::get_second_pricetag_string($shopping_cart_info['total_extra_shipping_min_total_missing'], ['currency_code' => $currency['code'], 'display_format' => 'standard']),
						),
						Arr::get($cmslabel, 'minimal_order_price_full', ''));
					?>
					</div>
					<a href="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', ''); ?>" class="btn btn-large btn-finish"><?php echo Arr::get($cmslabel, 'edit_shopping_cart'); ?></a> 
				<?php else: ?>
					<?php if (isset($accept_terms_field) AND isset($accept_terms_error)): ?>
						<?php echo View::factory('webshop/widget/accept_terms', array('accept_terms_field' => $accept_terms_field, 'accept_terms_error' => $accept_terms_error)); ?>
					<?php endif; ?>

					<button type="submit" class="btn btn-green btn-large btn-finish">
						<?php echo Arr::get($cmslabel, 'confirm_order'); ?>
					</button>	
				<?php endif; ?>
			</div>
		</div>

	</form>
</div>
<?php $this->endblock('content_layout'); ?>

<?php $this->block('newsletter'); ?> <?php $this->endblock('newsletter'); ?>

<?php $this->block('footer'); ?>
	<div class="footer checkout-footer fz0">
		<div class="clear wrapper">
			<div class="cf-col cf-col1">
				<div class="footer-title"><?php echo Arr::get($cmslabel, 'footer_title_contact'); ?></div>
				<ul class="footer-contact">
					<li class="item-tel"><?php echo Arr::get($cmslabel, 'contact_tel'); ?></li>
					<li class="item-mail"><?php echo Arr::get($cmslabel, 'contact_mail'); ?></li>		
				</ul>

			</div>
			<div class="cf-col cf-col2">
				<?php echo Arr::get($cmslabel, 'footer_logos'); ?>
			</div>
			<div class="cf-col cf-col3">
				<?php echo Arr::get($cmslabel, 'footer_logos_payment'); ?>
			</div>
			<div class="copy"><?php echo str_replace('%YEAR%', date('Y'), Arr::get($cmslabel, 'copyright', '')); ?></div>
			<div class="dev"><?php echo Kohana::config('signature.webshop.'.$info['lang']); ?></div>
			<div class="clear"></div>
		</div>
	</div>
<?php $this->endblock('footer'); ?>