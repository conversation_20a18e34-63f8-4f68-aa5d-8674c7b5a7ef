<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta($item['seo_title']); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/detail', ['item' => $item]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-catalog-detail<?php $this->endblock('page_class'); ?>

<?php $this->block('breadcrumb'); ?>
	<?php $breadcrumb = Cms::breadcrumb($info['lang'], $info['cmspage_url'], TRUE, $item['breadcrumbs']); ?>
	<?php echo View::factory('cms/widget/breadcrumb', ['breadcrumb' => $breadcrumb]); ?>
<?php $this->endblock('breadcrumb'); ?>

<?php $this->block('sidebar'); ?> <?php $this->endblock('sidebar'); ?>
<?php $this->block('main_class'); ?> cd-main<?php $this->endblock('main_class'); ?>
<?php $this->block('main_content_class'); ?> cd-main-content<?php $this->endblock('main_content_class'); ?>
<?php
    $package_qty = (!empty($item['package_qty']) AND (int)$item['package_qty'] > 0) ? (int)$item['package_qty'] : 1;
    $package_qty_decimals = 0;
?>
<?php $this->block('content'); ?>
	<div class="cd-body">
		<div class="cd-col1">
			<div id="product_images" class="cd-images fz0">
				<?php $images = Utils::get_files('catalogproduct', $item['id'], 'image', $info['lang']); ?>
			
				<?php if ($images): ?>
					<?php if(count($images) > 1): ?>
						<div class="cd-thumbs" data-variation_image_method="owl_carousel" data-variation_image_callback="owl_carousel">
							<?php $t = 0; ?>
							<?php foreach ($images as $file): ?>
									<div data-slide-index="<?php echo $t; ?>" href="#" class="owl-dot" <?php if (!empty($file['attributes_ids'])): ?> data-attributes_ids="<?php echo $file['attributes_ids']; ?>"<?php endif; ?>>
										<img <?php echo Thumb::generate($file['file'], array('width' => 120, 'height' => 165, 'default_image' => '/media/images/no-image-100.jpg', 'html_tag' => TRUE)); ?> alt="<?php echo Text::meta($file['description']); ?>" />
									</div>
								<?php $t++; ?>
							<?php endforeach; ?>
						</div>
					<?php endif; ?>
					<div class="cd-hero-image<?php if(count($images) <= 1): ?> special<?php endif; ?>">
						<div class="cd-hero-slider owl-carousel" data-imageslider="owlcarousel2">
							<?php $i = 0; ?>
							<?php foreach ($images as $file): ?>
								<div class="cd-hero-slide<?php if($i == 0): ?> cd-hero-slide-first<?php endif; ?>">
									<a href="<?php echo $file['url']; ?>" class="fancybox lloader" rel="gallery" title="<?php echo Text::meta($file['title']); ?>">
										<span><img <?php echo Thumb::generate($file['file'], array('width' => 900, 'height' => 900, 'default_image' => '/media/images/no-image-605.jpg', 'placeholder' => '/media/images/no-image-605.jpg')); ?> title="<?php echo Text::meta($file['title']); ?>" alt="<?php echo Text::meta($file['description']); ?>" data-zoom-image="<?php echo $file['url']; ?>" <?php if ($i == 0): ?>data-product_main_image="1"<?php endif; ?> /></span>
									</a>
								</div>
								<?php $i++; ?>
							<?php endforeach; ?>
						</div>					
					</div>
					<div id="zoom-container"></div>
				<?php else: ?>
					<div class="cd-hero-image special no-image">
						<img src="/media/images/no-image-605.jpg" alt="" data-product_main_image="1">
					</div>
				<?php endif; ?>
			</div>

			<!-- Related posts -->
			<?php $related_posts = Widget_Publish::publishes(array('lang' => $info['lang'], 'related_code' => 'related', 'related_product_id' => $item['id'], 'limit' => 2, 'extra_fields' => ['short_description'])); ?>
			<?php if ($related_posts): ?>
				<div class="cd-related-posts">
					<div class="cd-related-posts-title"><?php echo Arr::get($cmslabel, 'related_posts', 'Povezane objave'); ?></div>
					<div class="cd-related-posts-slider slider owl-carousel">	
						<?php foreach ($related_posts as $related_post): ?>	
							<a href="<?php echo $related_post['url']; ?>" class="cd-related-post">
								<figure class="cd-related-post-image lloader">
									<span>
										<?php if($info['user_device'] == 'm'): ?>
											<img <?php echo Thumb::generate($related_post['main_image'], array('width' => 760, 'default_image' => '/media/images/no-image-175.jpg', 'placeholder' => '/media/images/no-image-175.jpg')); ?> alt="" />
										<?php else:	?>
											<img <?php echo Thumb::generate($related_post['main_image'], array('width' => 175, 'height' => 125, 'default_image' => '/media/images/no-image-175.jpg', 'placeholder' => '/media/images/no-image-175.jpg')); ?> alt="" />
										<?php endif; ?>
									</span>
								</figure>
								<div class="pp-cnt cd-related-post-cnt">
									<?php if(!empty($related_post['headline'])): ?>
										<div class="pp-headline cd-related-post-headline"><?php echo $related_post['headline']; ?></div>
									<?php endif; ?>
									<div class="pp-title cd-related-post-title"><?php echo $related_post['title']; ?></div>
									<?php if(!empty($related_post['short_description'])): ?>
										<div class="pp-desc cd-related-post-desc"><?php echo $related_post['short_description']; ?></div>
									<?php endif; ?>
								</div>
							</a>
						<?php endforeach; ?>
					</div>
				</div>
			<?php endif; ?>
		</div>

		<div class="cd-col2">
			<div class="cd-col2-top clear">
				<div class="cd-col2-top-left">
					<h1 class="cd-title" data-product_title="1"><?php echo $item['seo_h1'] ?></h1>
					<!-- Product code number -->
					<div class="cd-code" data-variation_box="description" data-variations="1" data-variation_active_id="<?php echo Arr::get($active_variation, 'id'); ?>">
						<div data-variation_item_description="<?php echo $item['shopping_cart_code']; ?>"><span class="label"><?php echo Arr::get($cmslabel, 'code'); ?>:</span> <span data-product_code="<?php echo $item['shopping_cart_code']; ?>"><?php echo $item['code']; ?></span></div>
						<?php /* if ($variations): ?>
							<?php $product_statuses = Kohana::config('app.catalog.product_status.choices'); ?>
							<?php foreach ($variations AS $variation): ?>
								<div data-variation_item_description="<?php echo $variation['shopping_cart_code']; ?>" style="display: none">
									<span class="label"><?php echo Arr::get($cmslabel, 'code'); ?>:</span> <span data-product_code="<?php echo $variation['shopping_cart_code']; ?>"><?php echo $variation['code']; ?></span>
								</div>
							<?php endforeach;?>
						<?php endif; */ ?>
					</div>	
				</div>
				<?php if($item['price'] > 0): ?>
					<div class="cd-col2-top-right fz0">
						<!-- Discount badge -->
						<?php /* if(empty($variations)): ?>
							<?php if ($item['discount_percent'] > 0 OR $item['price_custom'] < $item['basic_price']): ?>
								<span class="cp-badge-discount cd-badge cd-badge-discount">-<?php echo $item['discount_percent']; ?>%</span>
							<?php endif; ?>
						<?php endif; */ ?>

						<!-- Product prices -->
						<div class="cd-price">
							<div data-variation_box="price">
								<div data-variation_item_price="<?php echo $item['shopping_cart_code']; ?>">
									<span data-collect_price="<?php echo $item['shopping_cart_code']; ?>">
										<?php if ($item['discount_percent'] > 0 OR $item['price_custom'] < $item['basic_price']): ?>			
											<div class="cd-old-price" data-product_basic_price="<?php echo $item['shopping_cart_code']; ?>"><span><?php echo Utils::currency_format($item['basic_price_custom'] * $currency['exchange'] * $package_qty, $currency['display']); ?></span><?php echo Utils::get_second_pricetag_string($item['basic_price_custom'] * $package_qty, ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></div>
											<div class="cd-current-price red" data-product_price="<?php echo $item['shopping_cart_code']; ?>"><?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'] * $package_qty, $currency['display']); ?><?php echo Utils::get_second_pricetag_string($item['price_custom'] * $package_qty, ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></div>
										<?php else: ?>
											<div class="cd-price-label">
												<?php if($package_qty != 1): ?>
													<?php echo Arr::get($cmslabel, 'price_original'); ?>
												<?php else: ?>
													<?php echo Arr::get($cmslabel, 'price'); ?>
												<?php endif; ?>
											</div>
											<div class="cd-current-price" data-product_price="<?php echo $item['shopping_cart_code']; ?>"><?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'] * $package_qty, $currency['display']); ?><?php echo Utils::get_second_pricetag_string($item['price_custom'] * $package_qty, ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></div>
										<?php endif; ?>
										<div class="cd-tax"><?php echo Arr::get($cmslabel, 'tax_included', 'PDV uključen u cijenu'); ?></div>
                                        <?php if ($package_qty > 1): ?>
                                            <div class="cd-unit-price">
                                                <?php echo Arr::get($cmslabel, 'unit_price', '1 kom:'); ?>
												<strong>
													<?php if ($item['discount_percent'] > 0 OR $item['price_custom'] < $item['basic_price']): ?>
														<span><?php echo Utils::currency_format($item['basic_price_custom'] * $currency['exchange'], $currency['display']); ?></span><?php echo Utils::get_second_pricetag_string($item['basic_price_custom'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
														<?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?><?php echo Utils::get_second_pricetag_string($item['price_custom'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
													<?php else: ?>
														<?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?><?php echo Utils::get_second_pricetag_string($item['price_custom'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
													<?php endif; ?>
												</strong>
                                            </div>
										<?php endif; ?>
										<?php if(!empty($item['extra_price_lowest']) AND $item['extra_price_lowest'] > 0 AND $item['discount_percent'] > 0): ?>
												<div class="cd-lowest-price">
													<?php echo Arr::get($cmslabel, 'lowest_price', 'Najniža cijena u posljednjih 30 dana'); ?>
													<?php echo Utils::currency_format($item['extra_price_lowest'] * $currency['exchange'], $currency['display']); ?>
													<?php echo Utils::get_second_pricetag_string($item['extra_price_lowest'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
												</div>
										<?php endif; ?>
									</span>
								</div>
								<?php /* if ($variations): ?>
									<?php foreach ($variations AS $variation): ?>
										<div data-variation_item_price="<?php echo $variation['shopping_cart_code']; ?>" style="display: none">
											<span data-collect_price="<?php echo $variation['shopping_cart_code']; ?>">
												<?php if ($variation['discount_percent'] > 0 OR $variation['price_custom'] < $variation['basic_price']): ?>
													<span class="cd-variation-discount">-<?php echo $variation['discount_percent']; ?>%</span>
													<div class="cd-variation-price">	
														<div class="cd-old-price" data-product_basic_price="<?php echo $variation['shopping_cart_code']; ?>"><span><?php echo Utils::currency_format($variation['basic_price_custom'] * $currency['exchange'], $currency['display']); ?></span><?php echo Utils::get_second_pricetag_string($variation['basic_price_custom'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></div>
														<div class="cd-current-price red" data-product_price="<?php echo $variation['shopping_cart_code']; ?>"><?php echo Utils::currency_format($variation['price_custom'] * $currency['exchange'], $currency['display']); ?><?php echo Utils::get_second_pricetag_string($variation['price_custom'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></div>
														<div class="cd-tax"><?php echo Arr::get($cmslabel, 'tax_included', 'PDV uključen u cijenu'); ?></div>
													</div>
												<?php else: ?>
													<div class="cd-price-label"><?php echo Arr::get($cmslabel, 'price', 'Cijena'); ?></div>
													<div class="cd-current-price" data-product_price="<?php echo $variation['shopping_cart_code']; ?>"><?php echo Utils::currency_format($variation['price_custom'] * $currency['exchange'], $currency['display']); ?><?php echo Utils::get_second_pricetag_string($variation['price_custom'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></div>
													<div class="cd-tax"><?php echo Arr::get($cmslabel, 'tax_included', 'PDV uključen u cijenu'); ?></div>
												<?php endif; ?>
												<?php if(!empty($variation['extra_price_lowest']) AND $variation['extra_price_lowest'] > 0 AND $variation['discount_percent'] > 0): ?>
													<div class="cd-lowest-price">
														<?php echo Arr::get($cmslabel, 'lowest_price', 'Najniža cijena u posljednjih 30 dana'); ?>
														<?php echo Utils::currency_format($variation['extra_price_lowest'] * $currency['exchange'], $currency['display']); ?>
														<?php echo Utils::get_second_pricetag_string($variation['extra_price_lowest'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
													</div>
											<?php endif; ?>
											</span>
										</div>
									<?php endforeach;?>
								<?php endif; */ ?>
							</div>
						</div>
					</div>
				<?php endif; ?>
			</div>
			
			<?php $documents = Utils::get_files('catalogproduct', $item['id'], '-image', $info['lang']); ?>
			<?php if(!empty($item['short_description']) OR !empty($documents)): ?>
				<div class="cd-col2-short-description">
					<?php if(!empty($item['short_description'])): ?>
						<div class="cd-short-desc"><?php echo $item['short_description']; ?></div>
					<?php endif ?>

					<?php if (!empty($documents)): ?>
						<?php foreach ($documents as $file): ?>
							<a class="btn-special btn-special-pdf cd-btn-special" href="<?php echo $file['url']; ?>" target="_blank" title="<?php echo Text::meta($file['description']); ?>"><span><?php echo Text::meta($file['title']); ?><?php // echo Arr::get($cmslabel, 'download'); ?></span></a>
						<?php endforeach; ?>
					<?php endif; ?>
				</div>
			<?php endif; ?>
			
			<?php $related_color_items = Widget_Catalog::products(array('lang' => $info['lang'], 'related_code' => 'related_color', 'related_item_id' => $item['id'], 'related_widget_data' => Arr::get($item, 'related_widget_data'), 'limit' => 10, 'always_to_limit' => TRUE, 'always_to_limit_strict_rules' => true)); ?>
			<?php if(!empty($related_color_items)): ?>
				<div class="related-other-colors">
					<div class="roc-title"><?php echo Arr::get($cmslabel, 'other_colors'); ?></div>
					<div class="roc-container fz0">
						<?php foreach ($related_color_items as $related_color_item): ?>
							<a href="<?php echo $related_color_item['url']; ?>" class="roc-item lloader" title="<?php echo $related_color_item['title']; ?>">
								<span><img <?php echo Thumb::generate($related_color_item['main_image'], array('width' => 95, 'height' => 135, 'default_image' => '/media/images/no-image-95.jpg', 'placeholder' => '/media/images/no-image-95.jpg')); ?> alt="" /></span>
							</a>	
						<?php endforeach; ?>
					</div>
				</div>
			<?php endif; ?>
            <?php $related_linija_items = Widget_Catalog::products(array('lang' => $info['lang'], 'related_code' => 'linija', 'related_item_id' => $item['id'], 'related_widget_data' => Arr::get($item, 'related_widget_data'), 'limit' => 10, 'always_to_limit' => TRUE, 'always_to_limit_strict_rules' => true)); ?>
            <?php if(!empty($related_linija_items)): ?>
                <div class="related-other-colors">
                    <div class="roc-title"><?php echo Arr::get($cmslabel, 'other_linija', 'Ovaj proizvod je dostupan i u drugim linijama'); ?></div>
                    <div class="roc-container fz0">
                        <?php foreach ($related_linija_items as $related_linija_item): ?>
                            <a href="<?php echo $related_linija_item['url']; ?>" class="roc-item lloader" title="<?php echo $related_linija_item['title']; ?>">
                                <span><img <?php echo Thumb::generate($related_linija_item['main_image'], array('width' => 95, 'height' => 135, 'default_image' => '/media/images/no-image-95.jpg', 'placeholder' => '/media/images/no-image-95.jpg')); ?> alt="" /></span>
                            </a>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>

			<?php $related_line_items = Widget_Catalog::products(array('lang' => $info['lang'], 'related_code' => 'linija', 'related_item_id' => $item['id'], 'related_widget_data' => Arr::get($item, 'related_widget_data'), 'limit' => 10, 'always_to_limit' => TRUE, 'always_to_limit_strict_rules' => true)); ?>
			<?php if(!empty($related_line_items)): ?>
				<div class="related-other-size empty-related-other-size">
					<div class="roc-title"><?php echo Arr::get($cmslabel, 'other_sizes'); ?></div>
					<div class="ros-container">
						<?php foreach ($related_line_items as $related_line_item): ?>
							<?php
								$related_attributes = CmsAttribute::attributeitems([
									'lang' => $info['lang'],
									'module' => 'catalog',
									//'mode' => 'id',
									'filters' => [
										'id' => Text::db_to_array($related_line_item['attributes_ids']),
									],
								]);
								$related_attr_title = '';
								if(!empty($related_attributes)){
									foreach($related_attributes as $related_attribute){
										if($related_attribute['attribute_code'] == 'velicina'){
											$related_attr_title = $related_attribute['title'];
										}
									}
								} 
							 ?>
							<?php if(!empty($related_attr_title)): ?>
								<a href="<?php echo $related_line_item['url']; ?>" class="ros-item">
									<span><?php echo $related_attr_title; ?></span>
								</a>
							<?php endif; ?>
						<?php endforeach; ?>
					</div>
				</div>
            <?php endif; ?>

            <div class="cd-buy-container">
				<!-- Add to cart section (if no variations) -->
				<?php $unit = (!empty($item['unit'])) ? $item['unit'] : Arr::get($cmslabel, 'unit', 'kom'); ?>
				<?php if ($item['is_available'] AND !$variations): ?>
					<div class="cd-buttons-container" id="buy-container">
                        <div class="wp-qty cd-qty">
                            <a class="wp-btn-qty wp-btn-dec" href="javascript:cmswebshop.shopping_cart.change_qty('<?php echo $item['shopping_cart_code']; ?>', '-', 1, 0, <?php echo $package_qty; ?>, 0, <?php echo $package_qty; ?>, <?php echo $package_qty_decimals; ?>);"></a>
                            <input type="text" name="qty[<?php echo $item['shopping_cart_code']; ?>]" class="wp-input-qty product_qty_input"value="<?php echo $package_qty; ?>" onchange="javascript:cmswebshop.shopping_cart.change_qty('<?php echo $item['shopping_cart_code']; ?>', 'input', 1, 0, <?php echo $package_qty; ?>, 0, <?php echo $package_qty; ?>, <?php echo $package_qty_decimals; ?>);" />
                            <a class="wp-btn-qty wp-btn-inc" href="javascript:cmswebshop.shopping_cart.change_qty('<?php echo $item['shopping_cart_code']; ?>', '+', 1, 0, <?php echo $package_qty; ?>, 0, <?php echo $package_qty; ?>, <?php echo $package_qty_decimals; ?>);"></a>
							<span class="wp-unit"><?php echo $unit; ?></span>
                        </div>

                        <a class="btn cd-btn-add btn cd-btn-add2 btn-green" href="javascript:cmswebshop.shopping_cart.add('<?php echo $item['shopping_cart_code']; ?>', '<?php echo $item['code']; ?>', 'simple', 'input', 3)">
							<span><?php echo Arr::get($cmslabel, 'add_to_shopping_cart'); ?></span>
						</a>
						<a href="javascript:toggleBox(['.cd-order-prescription', '.cd-prescription', '.cd-prescription-close']);" class="btn btn-white btn-white2 cd-order-prescription cd-order-prescription2"><span><?php echo Arr::get($cmslabel, 'prescription_order'); ?></span></a>
						<div class="cd-prescription cd-prescription2" style="display: none;">
							<a href="javascript:toggleBox(['.cd-order-prescription', '.cd-prescription', '.cd-prescription-close']);" class="cd-prescription-close"></a>
							<?php echo View::factory('siteforms/widget/prescriptionorder_form', array('mode' => 'homepage')); ?>
						</div>
					</div>
				<?php else: ?>
					<?php if (!$variations): ?>
						<?php if (isset($item['feedback_notification_widget'])): ?>
							<!-- Qty notification form -->
							<?php $form_content = 'catalogproduct_'.$item['id'].'_'.$info['lang']; ?>
							<?php echo View::factory('feedback/notification_form', array('form_content' => $form_content)); ?>
						<?php else: ?>
							<!-- Simple contact form -->
							<?php // echo View::factory('siteforms/widget/inquiry_form', array('subject' => Text::meta(Arr::get($cmslabel, 'inquiry_form_title', 'Upit o proizvodu').' '.$item['title'].', url: '.$info['url']))); ?>
						<?php endif; ?>
					<?php endif; ?>
				<?php endif; ?>

				<!-- Product variations (simple) -->
				<?php /* if ($variations): ?>
					<div class="cd-variations">	
						<div class="cd-variations-top fz0">
							<div class="cd-variations-title"><?php echo Arr::get($cmslabel, 'variations_size_available'); ?></div>

							<?php $size_guide_link = Arr::get($cmslabel, 'size_guide_link'); ?>
							<?php if(!empty($item['element_sizes'])): ?>
								<a class="btn-size-guide size-info-link" href="#tab-size-info"><span><?php echo Arr::get($cmslabel, 'size_guide'); ?></span></a>
								
								<div class="sizeinfo-content" id="tab-size-info" style="display: none;">
									<div class="size-guide-title"><?php echo Arr::get($cmslabel, 'size_guide_title'); ?></div>
									<?php echo $item['element_sizes']; ?>
								</div>
							<?php endif; ?>
						</div>
						<select name="select-product" id="product_choices">
							<?php foreach ($variations as $variation): ?>
								<?php $var_attrs = $variation['attributes']; ?>
								<?php foreach ($var_attrs as $var_attr): ?>
									<?php $var_title = $var_attr['title']; ?>
								<?php endforeach; ?>
								<option value="<?php echo $variation['shopping_cart_code']; ?>" <?php if ($variation['id'] == Arr::get($active_variation, 'id')): ?>selected<?php endif; ?> data-product_attributes="<?php echo $variation['shopping_cart_code']; ?>"><?php echo $var_title; ?><?php if($variation['is_available']): ?> <?php echo Arr::get($cmslabel, 'available'); ?><?php else: ?> <?php echo Arr::get($cmslabel, 'not_available_variation'); ?><?php endif; ?></option>
							<?php endforeach; ?>
						</select>
					</div>
					
					<div data-variation_box="attribute" class="cd-buttons-container">
						<?php foreach ($variations as $variation): ?>
							<div<?php if((int) $variation['available_qty'] == 1): ?> class="cd-qty-limit"<?php endif; ?> data-variation_item_attribute="<?php echo $variation['shopping_cart_code']; ?>" <?php if ($variation['id'] != Arr::get($active_variation, 'id')): ?>style="display: none"<?php endif; ?>>
								<?php if ($variation['is_available']): ?>
									<a class="btn btn-large btn-green btn-arrow cd-btn-add" href="javascript:cmswebshop.shopping_cart.add('<?php echo $variation['shopping_cart_code']; ?>', '', 'simple_loader', 'input', 3)"><span><?php echo Arr::get($cmslabel, 'add_to_shopping_cart'); ?></span></a>
									<a href="javascript:toggleBox(['.cd-order-prescription', '.cd-prescription', '.cd-prescription-close']);" class="btn btn-white btn-white2 cd-order-prescription"><span><?php echo Arr::get($cmslabel, 'prescription_order'); ?></span></a>
								<?php else: ?>
									<?php if (isset($item['feedback_notification_widget'])): ?>
										<!-- Qty notification form -->
										<?php $form_content = 'catalogproductvariation_'.$variation['id'].'_'.$info['lang']; ?>
										<?php echo View::factory('feedback/notification_form', array('form_content' => $form_content)); ?>
									<?php else: ?>
										<!-- Simple contact form -->
										<?php echo View::factory('siteforms/widget/inquiry_form', array('subject' => Text::meta(Arr::get($cmslabel, 'inquiry_form_title', 'Upit o proizvodu').' '.$item['title'].', šifra '.$variation['code'].', url: '.$info['url']))); ?>
									<?php endif; ?>
								<?php endif; ?>
							</div>
						<?php endforeach; ?>
						<div class="cd-prescription" style="display: none;">
							<a href="javascript:toggleBox(['.cd-order-prescription', '.cd-prescription', '.cd-prescription-close']);" class="cd-prescription-close"></a>
							<?php echo View::factory('siteforms/widget/prescriptionorder_form', array('mode' => 'homepage')); ?>
						</div>
					</div>
				<?php endif; */ ?>
			</div>
			
			<?php if(!empty($item['content']) OR !empty($item['element_diagnosis']) OR !empty($item['element_sizes'])): ?>
				<div class="cd-tabs">
					<?php if(!empty($item['content'])): ?>
						<div class="cd-tab<?php if(empty($item['element_diagnosis']) AND empty($item['element_sizes'])): ?> active<?php endif; ?>">
							<div class="cd-tab-title"><?php echo Arr::get($cmslabel, 'product_desc'); ?></div>
							<div class="cd-tab-desc"><?php echo $item['content']; ?></div>
						</div>
					<?php endif; ?>
					<?php if(!empty($item['element_diagnosis'])): ?>	
						<div class="cd-tab<?php if(empty($item['content']) AND empty($item['element_sizes'])): ?> active<?php endif; ?>">
							<div class="cd-tab-title"><?php echo Arr::get($cmslabel, 'diagnosis'); ?></div>
							<div class="cd-tab-desc"><?php echo $item['element_diagnosis'] ?></div>
						</div>
					<?php endif; ?>
					<?php if(!empty($item['element_sizes'])): ?>	
						<div class="cd-tab<?php if(empty($item['content']) AND empty($item['element_diagnosis'])): ?> active<?php endif; ?>">
							<div class="cd-tab-title"><?php echo Arr::get($cmslabel, 'sizes'); ?></div>
							<div class="cd-tab-desc"><?php echo $item['element_sizes'] ?></div>
						</div>
					<?php endif; ?>
				</div>
			<?php endif; ?>	

			<!-- Share -->
			<?php echo View::factory('cms/widget/share', array('item' => isset($item) ? $item : array())); ?>
		</div>
	</div>

	<div class="clear"></div>
	
	<!-- Related list products -->	
	<?php $related_list_items = Widget_Catalog::products(array('lang' => $info['lang'], 'related_code' => 'related_list_products', 'related_item_id' => $item['id'], 'related_widget_data' => Arr::get($item, 'related_widget_data'), 'limit' => 10, 'always_to_limit' => TRUE)); ?>
	<?php if(!empty($related_list_items)): ?>
		<div class="cd-related">
			<div class="cd-related-title"><?php echo Arr::get($cmslabel, 'related_list_products'); ?></div>
			<div class="owl-carousel slider cd-related-content fz0">
				<?php echo View::factory('catalog/widget/related_products', ['related_list_items' => $related_list_items]); ?>
			</div>
		</div>
	<?php endif; ?>

	<!-- Related products -->
	<?php $related_items = Widget_Catalog::products(array('lang' => $info['lang'], 'related_code' => 'related', 'related_item_id' => $item['id'], 'limit' => 10)); ?>
	<?php if ($related_items): ?>
		<div class="cd-related cd-related2">
			<div class="cd-related-title"><?php echo Arr::get($cmslabel, 'related_products'); ?></div>
			<div class="owl-carousel slider cd-related-content fz0">
				<?php echo View::factory('catalog/widget/related_products', ['related_list_items' => $related_items]); ?>
			</div>
		</div>
	<?php endif; ?>
<?php $this->endblock('content'); ?>