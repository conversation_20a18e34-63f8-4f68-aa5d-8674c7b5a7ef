<?php if (!empty($items) AND is_array($items) AND count($items)): ?>
	<div id="items_catalog" class="fz0 c-items" data-infinitescroll="items_catalog" data-infinitescroll_next_page="<?php echo $pagination->next_page; ?>" data-infinitescroll_total_pages="<?php echo $pagination->total_pages; ?>" data-infinitescroll_auto_trigger="2">
		<?php echo View::factory('catalog/index_entry'.$items_layout_sufix, array('items' => $items)); ?>
	</div>

	<?php echo $pagination; ?>
	<?php if($pagination): ?>
	<div class="load-more-container c-load-more-container">
		<a href="javascript:void(0);" class="btn btn-secondary load-more btn-load-more" style="display: none;"><?php echo str_replace(array('%PER_PAGE%', '%TOTAL%', '%NEXT_PAGE_FIRST_ITEM%', '%NEXT_PAGE_LAST_ITEM%'), array($pagination->items_per_page, $pagination->total_items, $pagination->next_page_first_item, $pagination->next_page_last_item), Arr::get($cmslabel, 'load_more_catalog', 'Učitaj još proizvoda')); ?></a>
	</div>	
	<?php endif; ?>
<?php else: ?>
	<!-- IS SEARCH -->
	<?php if ($q): ?>
		<div class="c-empty"><?php echo Arr::get($cmslabel, 'search_no_products'); ?></div>
	<?php else: ?>
		<div class="c-empty"><?php echo Arr::get($cmslabel, 'no_products'); ?></div>
	<?php endif; ?>
<?php endif; ?>