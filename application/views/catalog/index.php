<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(((!empty($kind['seo_title_full'])) ? $kind['seo_title_full'] : Arr::get($cms_page, 'seo_title')).((!empty($pagination->current_page) AND $pagination->current_page > 1) ? sprintf(Arr::get($cmslabel, 'current_page', ' - stranica %s od %s'), $pagination->current_page, $pagination->total_pages) : '')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/index', ['cms_page' => (!empty($cms_page) ? $cms_page : []), 'kind' => $kind, 'extra_kind' => (!empty($extra_kind) ? $extra_kind : []), 'pagination' => $pagination]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> catalog-index <?php if($q): ?>page-search catalog-search<?php endif; ?><?php $this->endblock('page_class'); ?>

<?php if($kind_content == 'manufacturer'): ?>
<?php $this->block('page_class'); ?> page-brand<?php $this->endblock('page_class'); ?>
<?php endif; ?>

<?php if(!$q): ?>
	<?php $this->block('breadcrumb'); ?>
		<?php $breadcrumb = Cms::breadcrumb($info['lang'], $info['cmspage_url'], TRUE, ($kind ? $kind['breadcrumbs'] : [])); ?>
		<?php echo View::factory('cms/widget/breadcrumb', ['breadcrumb' => $breadcrumb]); ?>
<?php $this->endblock('breadcrumb'); ?>
<?php endif; ?>

<?php $search_fields = Widget_Catalog::search_filters(array('filters' => $filters, 'hide_total_0' => TRUE, 'search_id' => ((isset($kind['search_id']) AND $kind['search_id']) ? $kind['search_id'] : 1))); ?>
<?php $active_filters = (isset($search_fields['_basic']['selected']) AND $search_fields['_basic']['selected']) ? $search_fields['_basic']['selected'] : array(); ?>

<!-- If is search page -->
<?php if($q): ?>
	<?php $this->block('search_top_container'); ?>
	<div class="page-header search-header">
		<h1 class="c-title"><?php echo ($kind) ? $kind['seo_h1'] : Arr::get($cms_page, 'seo_h1'); ?><?php echo (isset($extra_kind) AND $extra_kind AND $extra_kind['seo_title']) ? ' - '.Text::meta($extra_kind['seo_title']) : ''; ?><?php if ($q): ?><span><?php echo $q; ?></span><?php endif; ?></h1>

		<div class="page-header-cnt page-header-cnt2 s-toolbar">
			<?php if($items_total > 1): ?>
				<!-- Total items counter -->
				<div class="c-counter">
					<?php echo Arr::get($cmslabel, 'found_products'); ?> <?php echo $items_total ?> proizvod/a
				</div>
			<?php endif; ?>

			<?php $catalog_search_url = Utils::app_absolute_url($info['lang'], 'catalog'); ?>
			<?php $search_url = Utils::app_absolute_url($info['lang'], 'search'); ?>
			<?php $search_totals = Widget_Search::totals($info['lang'], $q); ?>
			<?php 
			if ((int) Arr::get($search_totals, 'catalog', 0) === 0) {
				if ((int) Arr::get($search_totals, 'publish', 0) > 0) {
					Request::current()->redirect($search_url . '?search_q=' . $q . '&search_content=publish');
				} elseif ((int) Arr::get($search_totals, 'cms', 0) > 0) {
					Request::current()->redirect($search_url . '?search_q=' . $q . '&search_content=cms');
				}
			}
			?>
			<ul class="s-nav<?php if($items_total < 2): ?> special<?php endif; ?>">
				<li class="selected"><a href="<?php echo $catalog_search_url; ?>?search_q=<?php echo $q; ?>"><?php echo Arr::get($cmslabel, "search_catalog"); ?> <span class="s-counter">(<?php echo (int) Arr::get($search_totals, 'catalog', 0); ?>)</span></a></li>
				<li><a href="<?php echo $search_url; ?>?search_q=<?php echo $q; ?>&search_content=publish"><?php echo Arr::get($cmslabel, "search_publish"); ?> <span class="s-counter">(<?php echo (int) Arr::get($search_totals, 'publish', 0); ?>)</span></a></li>
				<li><a href="<?php echo $search_url; ?>?search_q=<?php echo $q; ?>&search_content=cms"><?php echo Arr::get($cmslabel, "search_cms"); ?> <span class="s-counter">(<?php echo (int) Arr::get($search_totals, 'cms', 0); ?>)</span></a></li>
			</ul>

			<?php if ($items_total > 1): ?>	
			<!-- Sort options -->
			<div class="sort c-sort search-sort">
				<?php $sort_base_url = Url::query($_GET, FALSE, 'page,sort'); ?>
				<?php $sort_base_url .= ($sort_base_url)  ? '&' : '?'; ?>
				<?php $selected_sort = Arr::get($_GET, 'sort', ''); ?>
				<select onchange="window.location.href=this.options[this.selectedIndex].value">
					<option value="<?php echo $sort_base_url; ?>sort=position"<?php if ($selected_sort == 'position'): ?> selected="selected"<?php endif; ?>><?php echo Arr::get($cmslabel, 'ordering_position', 'Zadano'); ?></option>
					<?php /* 
					<option value="<?php echo $sort_base_url; ?>sort=new"<?php if ($selected_sort == 'new'): ?> selected="selected"<?php endif; ?>><?php echo Arr::get($cmslabel, 'ordering_recent', 'Najnovije'); ?></option> 
					<option value="<?php echo $sort_base_url; ?>sort=old"<?php if ($selected_sort == 'old'): ?> selected="selected"<?php endif; ?>><?php echo Arr::get($cmslabel, 'ordering_older', 'Najstarije'); ?></option> 
					*/ ?>
					<option value="<?php echo $sort_base_url; ?>sort=cheaper"<?php if ($selected_sort == 'cheaper'): ?> selected="selected"<?php endif; ?>><?php echo Arr::get($cmslabel, 'ordering_cheaper', 'Najjeftinije'); ?></option>
					<option value="<?php echo $sort_base_url; ?>sort=expensive"<?php if ($selected_sort == 'expensive'): ?> selected="selected"<?php endif; ?>><?php echo Arr::get($cmslabel, 'ordering_expensive', 'Najskuplje'); ?></option>
				</select>
			</div>
		<?php endif; ?>
		</div>
	</div>
	<?php $this->endblock('search_top_container'); ?>
<?php endif; ?>
	
<?php if($items_total >= 1): ?>
	<!-- Catalog toolbar -->
	<div class="clear c-toolbar<?php if($q): ?> c-toolbar-search<?php endif; ?><?php if(empty($category_rotator_items)): ?> no-dropdown<?php endif; ?>">

		<?php if ($items_total > 1): ?>	
			<!-- Sort options -->
			<div class="sort c-sort">
				<?php $sort_base_url = Url::query($_GET, FALSE, 'page,sort'); ?>
				<?php $sort_base_url .= ($sort_base_url)  ? '&' : '?'; ?>
				<?php $selected_sort = Arr::get($_GET, 'sort', ''); ?>
				<select onchange="window.location.href=this.options[this.selectedIndex].value">
					<option value="<?php echo $sort_base_url; ?>sort=position"<?php if ($selected_sort == 'position'): ?> selected="selected"<?php endif; ?>><?php echo Arr::get($cmslabel, 'ordering_position', 'Zadano'); ?></option>
					<?php /* 
					<option value="<?php echo $sort_base_url; ?>sort=new"<?php if ($selected_sort == 'new'): ?> selected="selected"<?php endif; ?>><?php echo Arr::get($cmslabel, 'ordering_recent', 'Najnovije'); ?></option> 
					<option value="<?php echo $sort_base_url; ?>sort=old"<?php if ($selected_sort == 'old'): ?> selected="selected"<?php endif; ?>><?php echo Arr::get($cmslabel, 'ordering_older', 'Najstarije'); ?></option> 
					*/ ?>
					<option value="<?php echo $sort_base_url; ?>sort=cheaper"<?php if ($selected_sort == 'cheaper'): ?> selected="selected"<?php endif; ?>><?php echo Arr::get($cmslabel, 'ordering_cheaper', 'Najjeftinije'); ?></option>
					<option value="<?php echo $sort_base_url; ?>sort=expensive"<?php if ($selected_sort == 'expensive'): ?> selected="selected"<?php endif; ?>><?php echo Arr::get($cmslabel, 'ordering_expensive', 'Najskuplje'); ?></option>
				</select>
			</div>
		<?php endif; ?>
	</div>
<?php endif; ?>

<?php $this->block('content'); ?> 
	<?php if(!$q): ?>
		<?php if(!empty($kind) AND !empty($kind['main_image_2'])): ?>
			<img <?php echo Thumb::generate($kind['main_image_2'], array('width' => 450, 'height' => 120, 'default_image' => '/media/images/no-image-270.jpg', 'html_tag' => true)); ?> alt="" />
		<?php else: ?>
			<h1 class="c-title"><?php echo ($kind) ? $kind['seo_h1'] : Arr::get($cms_page, 'seo_h1'); ?><?php echo (isset($extra_kind) AND $extra_kind AND $extra_kind['seo_title']) ? ' - '.Text::meta($extra_kind['seo_title']) : ''; ?><?php if ($q): ?>: <span><?php echo $q; ?></span><?php endif; ?></h1>
		<?php endif; ?>
	<?php endif; ?>

	<!-- Category content -->
	<?php if (!empty($kind) AND !empty($kind['content'])): ?>
		<div class="c-desc">
			<?php echo $kind['content']; ?>
		</div>
	<?php endif; ?>

	<?php if(!empty($kind)): ?>
		<?php $cur_category = $kind['code']; ?>
	
		<?php $category_rotator_items = Widget_Rotator::elements(['lang' => $info['lang'], 'category_code' => $cur_category, 'limit' => 0]); ?>
		<?php if(!empty($category_rotator_items)): ?>
			<div class="c-extra-desc<?php if($kind['level'] > 1): ?> special<?php endif; ?>">
				<?php foreach ($category_rotator_items as $category_rotator_item): ?>
					<div class="ced-container">
						<div class="ced-title"><?php echo $category_rotator_item['title']; ?></div>
						<div class="ced-content"><?php echo $category_rotator_item['content']; ?></div>
					</div>
				<?php endforeach; ?>
			</div>
		<?php endif; ?>
	<?php endif; ?>
	
	<div class="mobile-div fz0">
		<a href="javascript:toggleBox(['.mobile-filters', '.sidebar', 'body']);" class="mobile-filters<?php if($items_total < 1): ?> special<?php endif; ?>"><span><?php echo Arr::get($cmslabel, 'filters'); ?></span></a>

		<?php if($items_total >= 1 AND !$q): ?>
			<!-- Catalog toolbar -->
			<div class="clear c-toolbar<?php if($q): ?> c-toolbar-search<?php endif; ?><?php if(empty($category_rotator_items)): ?> no-dropdown<?php endif; ?>">
				
				<!-- Total items counter -->
				<div class="c-counter">
					<?php echo Arr::get($cmslabel, 'found_products'); ?> <?php echo $items_total ?> proizvod/a
				</div>

				<!-- Sort options -->
				<div class="sort c-sort">
					<?php $sort_base_url = Url::query($_GET, FALSE, 'page,sort'); ?>
					<?php $sort_base_url .= ($sort_base_url)  ? '&' : '?'; ?>
					<?php $selected_sort = Arr::get($_GET, 'sort', ''); ?>
					<select onchange="window.location.href=this.options[this.selectedIndex].value">
						<option value="<?php echo $sort_base_url; ?>sort=position"<?php if ($selected_sort == 'position'): ?> selected="selected"<?php endif; ?>><?php echo Arr::get($cmslabel, 'ordering_position', 'Zadano'); ?></option>
						<?php /* 
						<option value="<?php echo $sort_base_url; ?>sort=new"<?php if ($selected_sort == 'new'): ?> selected="selected"<?php endif; ?>><?php echo Arr::get($cmslabel, 'ordering_recent', 'Najnovije'); ?></option> 
						<option value="<?php echo $sort_base_url; ?>sort=old"<?php if ($selected_sort == 'old'): ?> selected="selected"<?php endif; ?>><?php echo Arr::get($cmslabel, 'ordering_older', 'Najstarije'); ?></option> 
						*/ ?>
						<option value="<?php echo $sort_base_url; ?>sort=cheaper"<?php if ($selected_sort == 'cheaper'): ?> selected="selected"<?php endif; ?>><?php echo Arr::get($cmslabel, 'ordering_cheaper', 'Najjeftinije'); ?></option>
						<option value="<?php echo $sort_base_url; ?>sort=expensive"<?php if ($selected_sort == 'expensive'): ?> selected="selected"<?php endif; ?>><?php echo Arr::get($cmslabel, 'ordering_expensive', 'Najskuplje'); ?></option>
					</select>
				</div>
			</div>
		<?php endif; ?>
	</div>



	<div id="items_catalog_layout">
		<?php echo View::factory('catalog/index_layout', [
			'cms_page' => $cms_page,
			'kind' => $kind,
			'extra_kind' => $extra_kind,
			'q' => $q,
			'items' => $items,
			'items_per_page' => $items_per_page,
			'items_all' => $items_all,
			'items_layout_sufix' => $items_layout_sufix,
			'items_total' => $items_total,
			'pagination' => $pagination,
			'selected_sort' => $selected_sort,
		]); ?>
	</div>
<?php $this->endblock('content'); ?>

<?php $this->block('sidebar'); ?>
	<aside class="sidebar">
		<a href="javascript:toggleBox(['.mobile-filters', '.sidebar', 'body']);" class="m-close-filters"><?php echo Arr::get($cmslabel, 'filters'); ?></a>
		<?php if ($active_filters): ?>
			<div class="fz0 cf-active<?php if($q): ?> cf-active-search<?php endif; ?>">
				<div class="cf-active-filters-title"><?php echo Arr::get($cmslabel, 'active_filters'); ?></div>
				<?php foreach ($active_filters AS $active_filter_code => $active_filter): ?>
					<a class="cf-active-item" href="<?php echo $active_filter['remove_url']; ?>"><span><?php echo $active_filter['title']; ?></span></a>
				<?php endforeach; ?>
			</div>
		<?php endif; ?>

		<?php echo View::factory('catalog/widget/sidebar_categories'); ?>
		<?php if(!empty($kind) AND $kind['level'] > 1 OR $q): ?>
			<!-- Filter -->
			<?php if ($search_fields): ?>
				<div class="cf<?php if ($items_total < 2): ?> special<?php endif; ?>">
					
					<div class="m-cf-top clear">
						<a href="javascript:toggleBox(['.m-filters', '.cf', '.m-cf-close', 'body'])" class="m-cf-title"><?php echo Arr::get($cmslabel, 'filters'); ?>
							<div class="m-cf-close"></div>
						</a>	
					</div>

					<form action="" method="get" id="attribute_filters_select">		
						<?php echo $search_fields['_basic']['field']; ?>
						<?php $si = 1; ?>
						<?php foreach ($search_fields AS $search_filter_field => $search_filter): ?>
							<?php $options = Arr::get($search_filter, 'options_details'); ?>
							<?php $template = Arr::get($search_filter, 'template'); ?>
							<?php if (!$options AND $search_filter_field !== 'categories') {continue;} ?>
							<?php if ($search_filter_field == 'categories' AND $kind_content != 'manufacturer' AND !$q): ?>
								<?php if(!empty($kind)): ?>	
									<?php if(!empty($kind['position_h'])): ?>

										<?php $current_cat = $kind['code']; ?>
										<?php $main_cat = reset($kind['parents'])['code']; ?>
										<?php $main_cat_title = reset($kind['parents'])['title']; ?>
										<?php $start_position = (!empty($kind['parents'])) ? reset($kind['parents'])['position_h'] : $kind['position_h']; ?>
										
										<?php $filter_categories = Widget_Catalog::categories(array('lang' => $info['lang'], 'level_range' => '1.1', 'code' => $main_cat)); ?>
										<?php if(!empty($filter_categories)): ?>
											<div class="filter-category<?php if($active_filters): ?> active-filters<?php endif; ?>">	
												<div class="filter-category-title"><?php echo $main_cat_title; ?></div>
												<ul class="filters-category">
													<?php foreach ($filter_categories as $filter_category): ?>
														<?php $filter_subcategories = Widget_Catalog::categories(array('lang' => $info['lang'], 'level_range' => '2.2', 'start_position' => $filter_category['position_h'])); ?>
														<?php if(!empty($filter_subcategories)): ?>	
																<?php foreach ($filter_subcategories as $filter_subcategory): ?>
																	<?php $filter_subsubcategories = Widget_Catalog::categories(array('lang' => $info['lang'], 'level_range' => '3.3', 'start_position' => $filter_subcategory['position_h'])); ?>
																	<li class="<?php if($info['url'] == $filter_subcategory['url']): ?> selected<?php endif; ?><?php if(!empty($filter_subsubcategories)): ?> has-children<?php endif; ?>">
																		<a href="<?php echo $filter_subcategory['url']; ?>"><span><?php echo $filter_subcategory['title']; ?></span></a>
																		<?php if(!empty($filter_subsubcategories)): ?>
																			<ul>
																				<?php foreach ($filter_subsubcategories as $filter_subsubcategory): ?>
																					<li class="<?php if($info['url'] == $filter_subsubcategory['url']): ?> selected<?php endif; ?>"><a href="<?php echo $filter_subsubcategory['url']; ?>"><span><?php echo $filter_subsubcategory['title']; ?></span></a></li>
																				<?php endforeach; ?>
																			</ul>
																		<?php endif; ?>		
																	</li>
																<?php endforeach; ?>
														<?php endif; ?>
													<?php endforeach; ?>
												</ul>
											</div>
										<?php endif; ?>
									<?php endif; ?>
								<?php endif; ?>

							<?php else: ?>		
								
								<?php if($si == 2 AND !$q): ?>
									<div class="cf-main-title"><?php echo Arr::get($cmslabel, 'filters'); ?></div>
								<?php endif; ?>
								<?php if (!$options AND $search_filter_field == 'categories') {continue;} ?>
								<div class="cf-item cf-item-<?php echo $search_filter_field; ?><?php if($search_filter['options_show']): ?> active<?php endif; ?><?php if($kind_content == 'manufacturer' OR $q): ?>cf-item-notitle<?php endif; ?>">		
									<!-- Filter title  -->
									<?php if($search_filter_field != 'categories'): ?>
										<div class="cf-title cf-title" onClick="toggleBox('.cf-item-<?php echo $search_filter_field; ?>');">
											<?php $filter_title = Arr::get($cmslabel, 'f_filter_'.$search_filter_field); ?>
											<?php if($filter_title): ?>
												<?php echo $filter_title; ?>
											<?php else: ?>
												<?php echo Arr::get($search_filter, 'label', Arr::get($cmslabel, $search_filter_field, $search_filter_field)); ?>
											<?php endif; ?>
										</div>
									<?php endif; ?>

									<div class="cf-item-wrapper scrollable">
										<?php $options_total = count($options); ?>
										<?php $i = 1; ?>
										<?php foreach ($options AS $option_id => $option): ?>
											<?php if ( ! $option_id) {$options_total--; continue;} ?>
											<div class="cf-row<?php if(!empty($option['level'])): ?> cf-row-level<?php echo $option['level']; ?><?php endif; ?><?php if($option['total_available'] <= 0): ?> cf-row-unavailable<?php endif; ?>">					
												<?php if(!empty($option['level']) AND $option['level'] > 1 OR $search_filter_field != 'categories'): ?>
													<input<?php if($option['total_available'] <= 0): ?> disabled<?php endif; ?> type="checkbox" id="search-<?php echo $search_filter_field; ?>-<?php echo $i; ?>" name="<?php echo $search_filter_field; ?>" value="<?php echo $option_id; ?>" <?php if ($option['selected']): ?> checked <?php endif; ?>>
												<?php endif; ?>
												<label for="search-<?php echo $search_filter_field; ?>-<?php echo $i; ?>"><?php echo $option['title']; ?> <span class="cf-counter"><?php echo $option['total_available']; ?></span></label>
											</div>
											<?php $i++; ?>
										<?php endforeach; ?>
									</div>
								</div>
							<?php endif; ?>
							<?php $si++; ?>
						<?php endforeach; ?>	
						<input type="hidden" name="search_q" value="<?php echo Arr::get($_GET, 'search_q', ''); ?>" />
						
						<?php if (!empty($search_fields['discount']['field'])): ?>
							<div class="cf-item cf-item-sale">
								<?php echo $search_fields['discount']['field']; ?> <label for="searchfield-discount"><?php echo Arr::get($cmslabel, 'sale_item'); ?></label>
							</div>
						<?php endif; ?>

						<div class="m-filter-bottom">
							<button class="btn btn-green confirm-filters" data-cmsfilter_manual_submit="1" data-cmsfilter_element="1" <?php if (empty($search_data)): ?> data-cmsfilter_empty="1" <?php endif; ?> type="submit"><?php echo Arr::get($cmslabel, 'confirm_filters'); ?></button>
							<?php if($active_filters): ?>	
								<a href="<?php if($q): ?>?search_q=<?php echo $q ?><?php else: ?>?<?php endif; ?>" class="cf-active-item btn-cf-active-clear m-btn-cf-active-clear"><span><?php echo Arr::get($cmslabel, 'clear_filtering'); ?></span></a>
							<?php endif; ?>
						</div>
					</form>
				</div>
			<?php endif; ?>
		<?php endif; ?>
	</aside>
<?php $this->endblock('sidebar'); ?>