<?php $categories = Widget_Catalog::categories(array('lang' => $info['lang'], 'level_range' => '1.1')); ?>
<?php if(!empty($categories)): ?>
	<?php
		$site_url = $info['site_url'];
		$active_first_level = $site_url . Utils::extract_segments($info['lang'], $info['basic_url'], 2);
	?>
	<ul class="c-categories" data-current_url="<?php echo urlencode($info['url']); ?>">
		<?php foreach ($categories as $category): ?>
			<?php $subcategories = Widget_Catalog::categories(array('lang' => $info['lang'], 'level_range' => '2.2', 'start_position' => $category['position_h'])); ?>
			<li class="<?php if(!empty($subcategories)): ?> has-children<?php endif; ?><?php if($active_first_level == $category['url']): ?> selected active<?php endif; ?>">
				<a href="<?php echo $category['url']; ?>"><span><?php echo $category['title']; ?></span></a>
				<?php if(!empty($subcategories)): ?>	
					<ul>	
						<?php foreach ($subcategories as $subcategory): ?>
							<?php $subsubcategories = Widget_Catalog::categories(array('lang' => $info['lang'], 'level_range' => '3.3', 'start_position' => $subcategory['position_h'])); ?>
							<li class="<?php if(!empty($subsubcategories)): ?> has-children2<?php endif; ?><?php if($info['url'] == $subcategory['url']): ?> selected<?php endif; ?>">
								<a href="<?php echo $subcategory['url']; ?>"><span><?php echo $subcategory['title']; ?></span></a>
								<?php if(!empty($subsubcategories)): ?>	
									<ul>
										<?php foreach ($subsubcategories as $subsubcategory): ?>
											<li class="<?php if($info['url'] == $subsubcategory['url']): ?>selected<?php endif; ?>">
												<a href="<?php echo $subsubcategory['url']; ?>">
													<span><?php echo $subsubcategory['title']; ?></span>
												</a>
												<?php $categories_l4 = Widget_Catalog::categories(array('lang' => $info['lang'], 'level_range' => '4.4', 'start_position' => $subsubcategory['position_h'])); ?>
												<?php if(!empty($categories_l4)): ?>
													<ul>
														<?php foreach ($categories_l4 as $category_l4): ?>
															<li class="<?php if($info['url'] == $category_l4['url']): ?>selected<?php endif; ?>">
																<a href="<?php echo $category_l4['url']; ?>">
																	<span><?php echo $category_l4['title']; ?></span>
																</a>
															</li>
														<?php endforeach; ?>
													</ul>
												<?php endif; ?>
											</li>
										<?php endforeach; ?>
									</ul>
								<?php endif; ?>
							</li>
						<?php endforeach; ?>
						<li><a href="<?php echo $category['url']; ?>"><?php echo Arr::get($cmslabel, 'show_all'); ?></a></li>
					</ul>
				<?php endif; ?>
			</li>
		<?php endforeach; ?>
	</ul>
<?php endif; ?>