<?php $product_priorities = (isset($product_priorities)) ? $product_priorities : Kohana::config('app.catalog.product_priorities'); ?>
<?php foreach ($related_list_items as $related_list_item): ?>
	<a href="<?php echo $related_list_item['url']; ?>" class="cd-related-item">
		<figure class="cd-related-item-image<?php if(empty($related_list_item['main_image'])): ?> no-image<?php endif; ?> lloader">
			<span>
				<img <?php echo Thumb::generate($related_list_item['main_image'], array('width' => 760, 'default_image' => '/media/images/no-image-530.jpg', 'placeholder' => '/media/images/no-image-530.jpg')); ?> alt="" />
			</span>

			<?php /* if (!empty($related_list_item['variation_request']) AND $related_list_item['variation_discount_percent_max'] > 0): ?>
				<span class="cp-badge cp-badge-discount">-<?php echo $related_list_item['variation_discount_percent_max']; ?>%</span>
			<?php elseif (empty($related_list_item['variation_request']) AND $related_list_item['discount_percent'] > 0): ?>
				<span class="cp-badge cp-badge-discount">-<?php echo $related_list_item['discount_percent']; ?>%</span>
			<?php else: */ ?>
				<?php $priority = Arr::get($product_priorities, (isset($related_list_item['priority_2']) ? $related_list_item['priority_2'] : '')); ?>
				<?php if ($priority): ?>
					<span class="cp-badge <?php echo Arr::get($priority, 'code'); ?>"><?php echo Arr::get($priority, 'title'); ?></span>
				<?php endif; ?>
			<?php //endif; ?>
		</figure>
		<div class="cd-related-item-cnt">
			<div class="cd-related-item-title"><?php echo $related_list_item['title']; ?></div>
			<div class="cd-related-item-price">
				<?php if (!empty($related_list_item['variation_request'])): ?>
					<?php if ((int) $related_list_item['variation_price_min'] > 0): ?>
						<div class="cd-related-item-label"><?php echo Arr::get($cmslabel, 'price_variation'); ?></div>
						<div class="cd-related-item-current cp-variation-price">
							<span data-product_price="<?php echo $related_list_item['shopping_cart_code']; ?>"><?php echo Utils::currency_format($related_list_item['variation_price_min'] * $currency['exchange'], $currency['display']); ?><?php echo Utils::get_second_pricetag_string($related_list_item['variation_price_min'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></span>
						</div>
					<?php endif; ?>
				<?php else: ?>	
					<?php if ($related_list_item['discount_percent'] > 0 OR $related_list_item['price_custom'] < $related_list_item['basic_price']): ?>
						<div class="cp-price-inline">
							<div class="cp-inline-left">
								<div class="cd-related-item-old" data-product_basic_price="<?php echo $related_list_item['shopping_cart_code']; ?>"><span><?php echo Utils::currency_format($related_list_item['basic_price_custom'] * $currency['exchange'], $currency['display']); ?></span></div>
								<div class="cd-related-item-current red" data-product_price="<?php echo $related_list_item['shopping_cart_code']; ?>"><span><?php echo Utils::currency_format($related_list_item['price_custom'] * $currency['exchange'], $currency['display']); ?></span></div>
							</div>
							<div class="cp-inline-right">
								<div class="cd-related-item-old"><?php echo Utils::get_second_pricetag_string($related_list_item['basic_price_custom'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></div>
								<div class="cd-related-item-current red"><?php echo Utils::get_second_pricetag_string($related_list_item['price_custom'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></div>
							</div>
						</div>
					<?php else: ?>
						<div class="cd-related-item-label"><?php echo Arr::get($cmslabel, 'price'); ?></div>
						<div class="cd-related-item-current" data-product_price="<?php echo $related_list_item['shopping_cart_code']; ?>"><?php echo Utils::currency_format($related_list_item['price_custom'] * $currency['exchange'], $currency['display']); ?><?php echo Utils::get_second_pricetag_string($related_list_item['price_custom'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></div>
					<?php endif; ?>
				<?php endif; ?>
				<?php if(!empty($related_list_item['extra_price_lowest']) AND $related_list_item['extra_price_lowest'] > 0 AND $related_list_item['discount_percent'] > 0): ?>
						<div class="cd-related-lowest-price">
							<?php echo Arr::get($cmslabel, 'lowest_price', 'Najniža cijena u posljednjih 30 dana'); ?>
							<?php echo Utils::currency_format($related_list_item['extra_price_lowest'] * $currency['exchange'], $currency['display']); ?>
							<?php echo Utils::get_second_pricetag_string($related_list_item['extra_price_lowest'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
						</div>
				<?php endif; ?>
			</div>
			<div class="btn btn-white btn-white2 cd-related-btn"><?php echo Arr::get($cmslabel, 'read_more'); ?></div>
		</div>
	</a>
<?php endforeach; ?>