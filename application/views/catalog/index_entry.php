<?php $product_priorities = (isset($product_priorities)) ? $product_priorities : Kohana::config('app.catalog.product_priorities'); ?>
<?php foreach ($items as $item): ?>
	<?php
		$package_qty = (!empty($item['package_qty']) AND (int)$item['package_qty'] > 0) ? (int)$item['package_qty'] : 1;
		$package_qty_decimals = 0;
	?>
	<a href="<?php echo $item['url']; ?>" class="clear cp lloader">
		<figure class="cp-image lloader">
			<span><img <?php echo Thumb::generate($item['main_image'], array('width' => 200, 'height' => 300, 'default_image' => '/media/images/no-image-200.jpg', 'placeholder' => '/media/images/no-image-200.jpg')); ?> title="<?php echo Text::meta($item['main_image_title']); ?>" alt="<?php echo Text::meta($item['main_image_description']); ?>" data-product_main_image="<?php echo $item['shopping_cart_code']; ?>" /></span>
			
			<?php /* if (!empty($item['variation_request']) AND $item['variation_discount_percent_max'] > 0): ?>
				<span class="cp-badge cp-badge-discount">-<?php echo $item['variation_discount_percent_max']; ?>%</span>
			<?php elseif (empty($item['variation_request']) AND $item['discount_percent'] > 0): ?>
				<span class="cp-badge cp-badge-discount">-<?php echo $item['discount_percent']; ?>%</span>
			<?php else: */ ?>
				<?php $priority = Arr::get($product_priorities, (isset($item['priority_2']) ? $item['priority_2'] : '')); ?>
				<?php if ($priority): ?>
					<span class="cp-badge <?php echo Arr::get($priority, 'code'); ?>"><?php echo Arr::get($priority, 'title'); ?></span>
				<?php endif; ?>
			<?php //endif; ?>
		</figure>

		<div class="clear cp-cnt">
			<h2 class="cp-title" data-product_title="<?php echo $item['shopping_cart_code']; ?>"><?php echo $item['title']; ?></h2>
			<div class="cp-code" data-product_code="<?php echo $item['shopping_cart_code']; ?>"><?php echo Arr::get($cmslabel, 'code'); ?>: <?php echo $item['code']; ?></div>
			<?php if(!empty($item['short_description'])): ?>
				<div class="cp-short-description"><?php echo $item['short_description']; ?></div>
			<?php endif; ?>

			<div class="cp-bottom-container clear">
				<?php if($item['price'] > 0): ?>
					<div class="cp-price">
						<?php if (!empty($item['variation_request'])): ?>
							<?php if ((int) $item['variation_price_min'] > 0): ?>
								<div class="cp-price-label"><?php echo Arr::get($cmslabel, 'price_variation'); ?></div>
								<div class="cp-current-price cp-variation-price">
									<span data-product_price="<?php echo $item['shopping_cart_code']; ?>"><?php echo Utils::currency_format($item['variation_price_min'] * $currency['exchange'], $currency['display']); ?><?php echo Utils::get_second_pricetag_string($item['variation_price_min'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></span>
								</div>
							<?php endif; ?>
						<?php else: ?>	
							<?php if ($item['discount_percent'] > 0 OR $item['price_custom'] < $item['basic_price']): ?>
								<div class="cp-old-price" data-product_basic_price="<?php echo $item['shopping_cart_code']; ?>"><span><?php echo Utils::currency_format($item['basic_price_custom'] * $currency['exchange'] * $package_qty, $currency['display']); ?></span><?php echo Utils::get_second_pricetag_string($item['basic_price_custom'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></div>
								<div class="cp-discount-price" data-product_price="<?php echo $item['shopping_cart_code']; ?>"><?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'] * $package_qty, $currency['display']); ?><?php echo Utils::get_second_pricetag_string($item['price_custom'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></div>
							<?php else: ?>
								<div class="cp-price-label">
									<?php if($package_qty != 1): ?>
										<?php echo Arr::get($cmslabel, 'price_original'); ?>
									<?php else: ?>
										<?php echo Arr::get($cmslabel, 'price'); ?>
									<?php endif; ?>
								</div>
								<div class="cp-current-price" data-product_price="<?php echo $item['shopping_cart_code']; ?>"><?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'] * $package_qty, $currency['display']); ?><?php echo Utils::get_second_pricetag_string($item['price_custom'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></div>
							<?php endif; ?>
						<?php endif; ?>
						<?php if ($package_qty > 1): ?>
							<div class="cp-unit-price">
								<?php echo Arr::get($cmslabel, 'unit_price', '1 kom:'); ?>
								<strong>Varaždin
									<?php if ($item['discount_percent'] > 0 OR $item['price_custom'] < $item['basic_price']): ?>
										<span><?php echo Utils::currency_format($item['basic_price_custom'] * $currency['exchange'], $currency['display']); ?></span><?php echo Utils::get_second_pricetag_string($item['basic_price_custom'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
										<?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?><?php echo Utils::get_second_pricetag_string($item['price_custom'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
									<?php else: ?>
										<?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?><?php echo Utils::get_second_pricetag_string($item['price_custom'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
									<?php endif; ?>
								</strong>
							</div>
						<?php endif; ?>
						<?php if(!empty($item['extra_price_lowest']) AND $item['extra_price_lowest'] > 0 AND $item['discount_percent'] > 0): ?>
							<div class="cp-lowest-price">
								<?php echo Arr::get($cmslabel, 'lowest_price', 'Najniža cijena u posljednjih 30 dana:'); ?>
								<?php echo Utils::currency_format($item['extra_price_lowest'] * $currency['exchange'], $currency['display']); ?>
								<?php echo Utils::get_second_pricetag_string($item['extra_price_lowest'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
							</div>
					<?php endif; ?>
					</div>
				<?php endif; ?>
				<div class="cp-btn-addtocart cp-btn-details<?php if($item['price'] == 0): ?> special<?php endif; ?>"><?php echo Arr::get($cmslabel, 'read_more'); ?></div>
			</div>
		</div>
	</a>
<?php endforeach; ?>