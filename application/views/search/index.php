<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-search page-search-<?php echo $search_content; ?><?php $this->endblock('page_class'); ?>

<?php $this->block('sidebar'); ?> <?php $this->endblock('sidebar'); ?>
<?php $this->block('main_content_class'); ?> search-main-content<?php $this->endblock('main_content_class'); ?>

<?php $catalog_search_url = Utils::app_absolute_url($info['lang'], 'catalog'); ?>
<?php $search_url = Utils::app_absolute_url($info['lang'], 'search'); ?>
<?php $search_content = Arr::get($_GET, 'search_content'); ?>
<?php $search_totals = Widget_Search::totals($info['lang'], $query, $search_content); ?>
<?php $other_results = array_sum($search_totals); ?>

<?php $this->block('content'); ?>
	<div class="page-header search-header">
		<h1 class="s-h1"><?php echo Arr::get($cms_page, 'seo_h1'); ?><span class="s-keyword"><?php echo $query; ?></span></h1>
		<?php echo Arr::get($cms_page, 'content'); ?>
		
		<div class="page-header-cnt s-toolbar">
			<ul class="s-nav">
				<li><a href="<?php echo $catalog_search_url; ?>?search_q=<?php echo $query; ?>"><?php echo Arr::get($cmslabel, "search_catalog"); ?> (<span class="s-counter"><?php echo (int) Arr::get($search_totals, 'catalog', 0); ?>)</span></a></li>
				<li<?php if($search_content == 'publish'): ?> class="selected"<?php endif; ?>><a href="<?php echo $search_url; ?>?search_q=<?php echo $query; ?>&search_content=publish"><?php echo Arr::get($cmslabel, "search_publish"); ?> <span class="s-counter">(<?php echo (int) Arr::get($search_totals, 'publish', (($items AND $search_content == 'publish') ? count($items['publish']) : 0)); ?>)</span></a></li>
				<li<?php if($search_content == 'cms'): ?> class="selected"<?php endif; ?>><a href="<?php echo $search_url; ?>?search_q=<?php echo $query; ?>&search_content=cms"><?php echo Arr::get($cmslabel, "search_cms"); ?> <span class="s-counter">(<?php echo (int) Arr::get($search_totals, 'cms', (($items AND $search_content == 'cms') ? count($items['cms']) : 0)); ?>)</span></a></li>
			</ul>
		</div>
	</div>

		<?php if (!empty($items) AND is_array($items) AND count($items) > 0 ): ?>
			<?php foreach ($items as $module => $results): ?>
				<div class="search">
					<?php if ($module == 'publish'): ?>
						<?php echo View::factory('publish/index_entry', array('items' => $results, 'mode' => 'search')); ?>
					<?php elseif ($module == 'catalog'): ?>
						<?php echo View::factory('catalog/index_entry', array('items' => $results, 'mode' => 'search')); ?>
					<?php else: ?>
						<?php foreach ($results as $item): ?>
							<article class="s-item">
								<h2 class="s-item-title"><a href="<?php echo $item['url']; ?>"><?php echo $item['title']; ?></a></h2>
								<?php if (isset($item['content']) AND $item['content']): ?><p><?php echo Text::limit_words(strip_tags($item['content']), 50, '...'); ?></p><?php endif; ?>
								<a href="<?php echo $item['url']; ?>" class="btn btn-white"><?php echo Arr::get($cmslabel, 'read_more'); ?></a>
							</article>
						<?php endforeach; ?>
					<?php endif; ?>
				</div>
				<div class="clear"></div>
			<?php endforeach; ?>
		<?php else: ?>
			<div class="s-no-results"><?php echo Arr::get($cmslabel, 'nothing_found', 'Nema rezultata za traženi pojam'); ?></div>
		<?php endif; ?>

<?php $this->endblock('content'); ?>