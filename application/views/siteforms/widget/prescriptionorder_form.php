<?php $extra_form = Widget_Cms::form($info['lang'], 'code:prescription_form', $info); ?>

<?php $mode = (isset($mode))? $mode : ''; ?>

<?php if ($extra_form): ?>
<form action="<?php echo Arr::get($extra_form, 'form_url'); ?>#prescriptionorder_form" method="post" name="prescriptionorder_form" id="prescriptionorder_form" class="ajax_siteform<?php if($mode == 'homepage'): ?> homepage-form<?php endif; ?>" data-siteform_response="show_hide" accept-charset="utf-8" enctype="multipart/form-data">
    <?php foreach (@$extra_form['fields'] as $field => $field_element): ?>
        <?php $error = isset($extra_form['errors'][$field][0]) ? $extra_form['errors'][$field][0] : ''; ?>
	    <?php if($field == 'health_insurance'): ?> 
	    	<div class="field field-special2">
	    <?php endif; ?>
	        <div class="<?php if($field != 'health_insurance' AND $field != 'insurance_provider'): ?>field<?php endif; ?> field-<?php echo $field; ?><?php if($field == 'mbo' OR $field == 'prescription_number'): ?> field-special<?php endif; ?><?php if($field == 'mbo' AND Arr::get($cmslabel, 'mbo_content') OR $field == 'prescription_number' AND Arr::get($cmslabel, 'prescription_number_content')): ?> has-tooltip<?php endif; ?>">
	            <label for="field-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, $field); ?><?php if (isset($extra_form['requested']) AND in_array($field, $extra_form['requested'])): ?> *<?php endif; ?></label>

	            <?php echo $field_element; ?>
	            <span id="field-error-<?php echo $field; ?>" class="field_error error booking-form-error" <?php if (!$error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, "error_{$error}", $error); ?></span>
	       		<?php if($field == 'mbo'): ?>
	       			<div class="field-special-tooltip field-special-tooltip1"><?php echo Arr::get($cmslabel, 'mbo_content'); ?></div>
	       		<?php endif; ?>
	       		<?php if ($field == 'prescription_number'): ?>
	       			<div class="field-special-tooltip field-special-tooltip2"><?php echo Arr::get($cmslabel, 'prescription_number_content'); ?></div>
	       		<?php endif; ?>
	        </div>
	    <?php if($field == 'insurance_provider'): ?>
	   		</div>
	   	<?php endif; ?>
    <?php endforeach; ?>
    <p class="req homepage-req"><?php echo Arr::get($cmslabel, 'request_fields'); ?></p>
    <p class="submit">
    	<button class="btn homepage-submit-btn" type="submit">
    		<span class="normal"><?php echo Arr::get($cmslabel, 'send'); ?></span>
    		<span class="special"><?php echo Arr::get($cmslabel, 'send_order'); ?></span>
    	</button>
    </p>
	<p class="req homepage-req m-req"><?php echo Arr::get($cmslabel, 'request_fields'); ?></p>
</form>
<div id="prescriptionorder_form_success" class="global-success contact-success prescription-success" style="display: none;"><?php echo Arr::get($cmslabel, 'sent_success'); ?></div>
<?php endif; ?>