<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-contact<?php $this->endblock('page_class'); ?>

<?php $this->block('content'); ?>
	<h1><?php echo Arr::get($cms_page, 'seo_h1'); ?></h1>
	
	<?php echo Arr::get($cms_page, 'content'); ?>
	<div class="clear"></div>
	
	<?php echo View::factory('cms/widget/share'); ?>
	<?php if (!empty($gdpr_approved) AND is_array($gdpr_approved) AND in_array('marketing', $gdpr_approved)): ?>
		<?php $map_points = Widget_Location::points(array('lang' => $info['lang'])); ?>
		<?php if ($map_points): ?>
			<div id="mapContainer" class="map">
				<div id="map_canvas" style="width:100%;height:100%;"></div>
			</div>
			<?php foreach ($map_points AS $point): ?>
				<div style="display: none;"
					data-gmap_object_id="<?php echo $point['id']; ?>"
					data-gmap_object_lat="<?php echo $point['gmap']['lat']; ?>"
					data-gmap_object_lon="<?php echo $point['gmap']['lon']; ?>"
					data-gmap_object_tooltip="_content"
					data-id="<?php echo Text::meta($point['id']); ?>">
					<?php if($point['main_image']): ?>
						<figure class="image">
							<img src="<?php echo Thumb::generate($point['main_image'], 270, 135, TRUE, 'thumb', TRUE, '/shared/images/space.gif'); ?>" alt=\"\">
						</figure>
					<?php endif; ?>
					<span class="title"><?php echo $point['title']; ?></span>
					<span class="address"><?php echo $point['address']; ?></span>
					<span class="business-hour"><?php echo $point['business_hour']; ?></span>
					<span class="contact"><?php echo $point['contact']; ?></span>
				</div>
			<?php endforeach; ?>
		<?php endif; ?>
	<?php endif; ?>
<?php $this->endblock('content'); ?>

<?php $this->block('extrabody'); ?>
	<?php if ($map_points): ?>
		<?php echo Html::media('cmslocation,infobox', 'js'); ?>
	<?php endif; ?>
<?php $this->endblock('extrabody'); ?>