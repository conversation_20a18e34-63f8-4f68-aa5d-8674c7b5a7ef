<?php $this->extend('default_quick'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>

<?php $this->block('content_layout'); ?>
<div class="tellfriend">
	<h1 style="word-break: break-all;"><?php echo Arr::get($cms_page, 'seo_h1'); ?></h1>
	<?php echo Arr::get($cms_page, 'content'); ?>

	<?php if ($info['message_type'] == 'success' AND $info['message'] == 'siteform_tellafriend'): ?>
		<?php echo Arr::get($cmslabel, 'tellafriend_thankyou', __('Zatvori')); ?> <a href="javascript:window.close();"><?php echo Arr::get($cmslabel, 'close', __('Zatvori')); ?></a>
		<script type="text/javascript">setTimeout(function () {window.close();}, 3000);</script>
	<?php else: ?>
		<form action="#tellafriend_form" method="post" name="tellafriend_form" id="tellafriend_form" class="form-label ajax_siteform">
			<?php foreach ($form['fields'] as $field => $field_element): ?>
				<?php if (in_array($field, ['url', 'message'])) {continue;} ?>
				<?php $error = Valid::get_error($field, Arr::get($form, 'errors')); ?>
				<p class="field field-<?php echo $field; ?>">
					<?php echo $field_element; ?>
					<label for="field-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, 'f_tellafriend_'.$field, Arr::get($cmslabel, $field)); ?><?php if (isset($form['requested']) AND in_array($field, $form['requested'])): ?> *<?php endif; ?></label>
					<span id="field-error-<?php echo $field; ?>" class="field_error error" <?php if (!$error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, "error_{$error}", $error); ?></span>
				</p>
			<?php endforeach; ?>
            <p class="clear">
            	<button type="submit" class="btn-tellfriend"><?php echo Arr::get($cmslabel, 'send'); ?></button> 
            	<a href="javascript:window.close();" class="btn-tellfriend-close"><?php echo Arr::get($cmslabel, 'canceled', __('Odustani')); ?></a>
            </p>
		</form>
	<?php endif; ?>
</div>
<?php $this->endblock('content_layout'); ?>