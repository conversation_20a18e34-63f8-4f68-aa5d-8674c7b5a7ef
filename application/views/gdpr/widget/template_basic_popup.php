<!-- GDPR konfigurator -->
<div id="gdpr_configurator">
	<div class="gdpr-popup-content">
		<?php if(Arr::get($cmslabel, 'gdpr_popup_header')): ?>
			<div class="gdpr-popup-header">
				<?php echo Arr::get($cmslabel, 'gdpr_popup_header'); ?>
			</div>
		<?php endif; ?>
		<form id="gdpr_configurator_form" data-gdpr_ajax="1" action="<?php echo Utils::app_absolute_url($info['lang'], 'gdpr', 'user', false); ?>?redirect=<?php echo $this->info['redirect_url']; ?>" method="POST">
			<div class="gdpr-popup-cnt">
				<?php echo str_replace('{OBJECTS}', $gdpr_objects_html, $gdpr_template['content']); ?>
			</div>

			<div class="gdpr-popup-btns">
				<button class="btn btn-secondary gdpr-popup-btn gdpr-btn-submit" type="submit" name="submit" value="1"><?php echo Arr::get($cmslabel, 'gdpr_popup_save'); ?></button>
				<div class="gdpr-popup-reject-allow-btns">
					<a class="btn btn-white btn-border gdpr-btn-new gdpr_submit_request_button" href="javascript:void(0);"><?php echo Arr::get($cmslabel, 'gdpr_only_necessary'); ?></a> 
					<a class="btn btn-white btn-border gdpr-btn-new gdpr_submit_all_button" href="javascript:void(0);"><?php echo Arr::get($cmslabel, 'gdpr_select_all'); ?> </a>
				</div>
			</div>
		</form>
	</div>
</div>
