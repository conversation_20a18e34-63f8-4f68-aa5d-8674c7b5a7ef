<?php $active_menu_item = Utils::active_urls($info['lang'], $info['cmspage_url']); ?>
<?php foreach ($items as $menu_item): ?>

	<?php $menu_class = $menu_item['style']; ?>	
	<?php if($menu_class == 'osnovni'): ?>
		<?php $menu_code = 'cms_osnovni'; ?>
	<?php elseif ($menu_class == 'djelatnik'): ?>
		<?php $menu_code = 'cms_djelatnik'; ?>
	<?php elseif ($menu_class == 'zaposlenik'): ?>
		<?php $menu_code = 'cms_zaposlenik'; ?>
	<?php endif; ?>

	<?php $submenu = Widget_Cms::menu(array('lang' => $info['lang'], 'code' => $menu_code, 'selected' => $active_menu_item, 'start_position' => $menu_item['position_h'], 'level_range' => '2.2')); ?>
	<li class="<?php echo $menu_item['style']; ?><?php if(!empty($submenu)): ?> has-children<?php endif; ?><?php if($info['basic_url'] == $menu_item['url']): ?> selected<?php endif; ?>">

		<a href="<?php echo $menu_item['url']; ?>"><span><?php echo $menu_item['title']; ?></span></a>
		<?php if(!empty($submenu)): ?>
			<ul>
				<?php foreach ($submenu as $submenu_item): ?>
					<?php $subsubmenu = Widget_Cms::menu(array('lang' => $info['lang'], 'code' => $menu_code, 'selected' => $active_menu_item, 'start_position' => $submenu_item['position_h'], 'level_range' => '3.3')); ?>
					<li class="<?php if(!empty($subsubmenu)): ?> has-children<?php endif; ?><?php if($info['basic_url'] == $submenu_item['url']): ?> selected<?php endif; ?>">
						<a <?php if($info['basic_url'] == $submenu_item['url']): ?>class="active"<?php endif; ?> href="<?php echo $submenu_item['url']; ?>"><span><?php echo $submenu_item['title']; ?></span></a>
						<?php if(!empty($subsubmenu)): ?>
							<ul>
								<?php foreach ($subsubmenu as $subsubmenu_item): ?>
									<li class="<?php if($info['basic_url'] == $subsubmenu_item['url']): ?>selected<?php endif; ?>"><a <?php if($info['basic_url'] == $subsubmenu_item['url']): ?>class="active"<?php endif; ?> href="<?php echo $subsubmenu_item['url']; ?>"><span><?php echo $subsubmenu_item['title']; ?></span></a></li>
								<?php endforeach; ?>
							</ul>
						<?php endif; ?>
					</li>
				<?php endforeach; ?>
			</ul>
		<?php endif; ?>
	</li>	
<?php endforeach; ?>