<?php $this->extend('cms/default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-cms page-faq<?php $this->endblock('page_class'); ?>

<?php $this->block('content'); ?>
	<h1><?php echo Arr::get($cms_page, 'seo_h1'); ?></h1>
	
	<div class="cms-content clear">
		<?php $rotator_items = Widget_Rotator::elements(['lang' => $info['lang'], 'category_code' => 'faq', 'limit' => 0]); ?>
		<?php $last_header_title = ''; ?>
		<?php foreach($rotator_items as $item): ?>
			<?php if(!empty($item['title2']) AND $last_header_title != $item['title2']): ?>	
				<div class="faq-group-title"><?php echo $item['title2']; ?></div>
			<?php endif; ?>
			<article class="fp">
				<h2 href="javascript:;" class="fp-title"><span><?php echo $item['title']; ?></span></h2>
				<div class="fp-cnt"><?php echo $item['content']; ?></div>
			</article>
			<?php $last_header_title = $item['title2']; ?>
		<?php endforeach; ?> 
	</div>

	<?php echo View::factory('cms/widget/share', ['item' => isset($cms_page) ? $cms_page : []]); ?>
<?php $this->endblock('content'); ?>