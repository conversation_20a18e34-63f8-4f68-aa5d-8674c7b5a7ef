<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-homepage<?php $this->endblock('page_class'); ?>

<?php $this->block('main_class'); ?> homepage-main<?php $this->endblock('main_class'); ?>

<?php $this->block('content_layout'); ?>
	<div class="homepage-top-intro">
		<div class="homepage-intro clear">
			<div class="homepage-intro-left">
				<div class="hil-contact"><?php echo Arr::get($cmslabel, 'homepage_contact'); ?></div>
				<div class="hil-label"><?php echo Arr::get($cmslabel, 'homepage_intro'); ?></div>
			</div>
			
			<div class="homepage-intro-right">
				<a href="javascript:toggleBox(['.hir-note-buttons', '.homepage-form', '.homepage-intro-right', '.homepage-intro', '.categories-cnt', '.hir-note', '.hir-close-btn', 'body', '.header']);" class="hir-close-btn"><span><?php echo Arr::get($cmslabel, 'form_close'); ?></span></a>
				<div class="hir-form-container">
					<div class="hir-note">
						<div class="hir-note-title"><?php echo Arr::get($cmslabel, 'homepage_intro_note_title'); ?></div>
						<div class="hir-note-subtitle"><?php echo Arr::get($cmslabel, 'homepage_intro_note_subtitle'); ?></div>
						<?php echo Arr::get($cmslabel, 'homepage_intro_note_button'); ?>
					</div>
					
					<div class="hir-note-buttons">
						<a href="javascript:toggleBox(['.hir-note-buttons', '.homepage-form', '.homepage-intro-right', '.homepage-intro', '.categories-cnt', '.hir-note', 'body', '.header']);" class="btn homepage-intro-form-button"><?php echo Arr::get($cmslabel, 'homepage_intro_form_button'); ?></a>
						<?php echo Arr::get($cmslabel, 'homepage_intro_note_button'); ?>
					</div>
					<?php echo View::factory('siteforms/widget/prescriptionorder_form', array('mode' => 'homepage')); ?>
				</div>
			</div>
		</div>
		
		<?php $homepage_intro = Widget_Rotator::elements(['lang' => $info['lang'], 'category_code' => 'homepage_intro', 'limit' => 0]); ?>
		<div class="owl-carousel slider homepage-slider">
			<?php foreach ($homepage_intro as $intro_item): ?>
				<div class="hs-item">
					<picture>
						<?php if(!empty($intro_item['image_2'])): ?>
							<source srcset="<?php echo Thumb::generate($intro_item['image_2'], 760, 1500, FALSE, 'thumb', TRUE, '/media/images/no-image-1920.jpg'); ?>" media="(max-width: 760px)">
						<?php endif; ?>
						<img <?php echo Thumb::generate($intro_item['image'], array('width' => 1920, 'default_image' => '/media/images/no-image-1920.jpg', 'html_tag' => true)); ?> alt="" />
					</picture>
				</div>
			<?php endforeach; ?>
		</div>
	</div>

	<div class="homepage-post-intro">
		<div class="wrapper fz0">
			<div class="hpi-left"><?php echo Arr::get($cmslabel, 'years_label'); ?></div>
			<div class="hpi-right"><?php echo Arr::get($cmslabel, 'special_shopping'); ?></div>
		</div>
		<div class="hpi-info">
			<div class="hpi-info-title"><?php echo Arr::get($cmslabel, 'shop_consultation'); ?></div>
			<div class="hpi-info-content">
				<div class="hpi-cnt hpi-address"><?php echo Arr::get($cmslabel, 'address_location'); ?></div>
				<div class="hpi-cnt hpi-phone"><?php echo Arr::get($cmslabel, 'phone_contact'); ?></div>
				<div class="hpi-cnt hpi-business-time"><?php echo Arr::get($cmslabel, 'business_time'); ?></div>
			</div>
		</div>
	</div>
	
	<div class="homepage-pw">
		<div class="wrapper">
			<?php echo View::factory('publish/widget/latest_news'); ?>
		</div>
	</div>

	<div class="homepage-cw">
		<div class="wrapper">
			<div class="hcw-container fz0">
				<?php $categories = Widget_Catalog::categories(array('lang' => $info['lang'], 'level_range' => '1.1', 'extra_fields' => ['short_description'])); ?>
				
				<?php $hcw = 1; ?>
				<?php foreach ($categories as $category): ?>
					<?php $subcategories = Widget_Catalog::categories(array('lang' => $info['lang'], 'level_range' => '2.2', 'start_position' => $category['position_h'])); ?>
					
					<?php if(empty($subcategories)): ?>
						<a href="<?php echo $category['url']; ?>" class="hcw-col hcw-col<?php echo $hcw; ?>">
					<?php else: ?>
						<div class="hcw-col hcw-col<?php echo $hcw; ?>">
					<?php endif; ?>
							<?php if(!empty($category['main_image'])): ?>
								<figure class="hcw-image lloader">
									<a href="<?php echo $category['url']; ?>"><img <?php echo Thumb::generate($category['main_image'], array('width' => 335, 'height' => 277, 'default_image' => '/media/images/no-image-355.jpg', 'placeholder' => '/media/images/no-image-355.jpg')); ?> alt="" /></a>
								</figure>
							<?php endif; ?>
							
							<a href="<?php echo $category['url']; ?>" class="hcw-cnt<?php if(empty($category['main_image'])): ?> no-image<?php endif; ?>">
								<div class="hcw-cnt-title"><span><?php echo $category['title']; ?></span></div>
								<?php if(!empty($category['short_description'])): ?>	
									<div class="hcw-cnt-desc"><?php echo $category['short_description']; ?></div>
								<?php endif; ?>
								<span class="btn btn-white"><?php echo Arr::get($cmslabel, 'read_more'); ?></span>
							</a>
							<?php if(!empty($subcategories)): ?>
								<div class="hcw-back">
									<div class="hcw-back-title"><span><a href="<?php echo $category['url']; ?>"><?php echo $category['title']; ?></a></span></div>
									<ul class="hcw-back-list">
										<?php foreach ($subcategories as $subcategory): ?>
											<li><a href="<?php echo $subcategory['url']; ?>"><span><?php echo $subcategory['title']; ?></span></a></li>
										<?php endforeach; ?>
									</ul>
								</div>
							<?php endif; ?>
					<?php if(empty($subcategories)): ?>
						</a>
					<?php else: ?>
						</div>
					<?php endif; ?>
					<?php $hcw++; ?>
				<?php endforeach; ?>
			</div>
		</div>
	</div>

	<div class="homepage-about">
		<div class="wrapper clear">
			<div class="ha-left">
				<div class="ha-content"><?php echo Arr::get($cms_page, 'content'); ?></div>
			</div>
			<?php $homepage_items = Widget_Cms::menu(array('lang' => $info['lang'], 'code' => 'homepage_info')); ?>
			<?php if(!empty($homepage_items)): ?>
				<div class="ha-right">
					<ul class="ha-right-list">
						<?php foreach ($homepage_items as $homepage_item): ?>
							<li><a href="<?php echo $homepage_item['url']; ?>" class="ha-link <?php echo $homepage_item['style']; ?>"><strong><?php echo $homepage_item['title']; ?></strong> <?php echo $homepage_item['anchor_text']; ?></a></li>
						<?php endforeach; ?>
					</ul>
				</div>
			<?php endif; ?>
		</div>
		<?php $homepage_images = Widget_Rotator::elements(['lang' => $info['lang'], 'category_code' => 'homepage_about_images', 'limit' => 3]); ?>
		<?php if(!empty($homepage_images)): ?>	
			<div class="ha-images">
				<?php foreach ($homepage_images as $homepage_image): ?>
					<div class="ha-image">
						<img <?php echo Thumb::generate($homepage_image['image'], array('width' => 323, 'height' => 257, 'default_image' => '/media/images/no-image-355.jpg', 'html_tag' => true)); ?> alt="" />
					</div>
				<?php endforeach; ?>
			</div>
		<?php endif; ?>
	</div>
<?php $this->endblock('content_layout'); ?>