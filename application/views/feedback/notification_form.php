<div class="cd-unavailable">
	<h2 class="cd-unavailable-title"><?php echo Arr::get($cmslabel, 'not_available', '<PERSON>ba<PERSON><PERSON><PERSON>i me kada postane dostupno'); ?></h2>
	<div class="cd-unavailable-subtitle"><?php echo Arr::get($cmslabel, 'not_available_subtitle'); ?></div>
	<div class="cd-notify-form">
		<div id="notifyme-<?php echo $form_content; ?>">
			<form action="#notifyme_form" method="post" name="notifyme_form" id="notifyme_add_form_<?php echo $form_content; ?>" class="cd-unavailable-form clear">
				<input type="hidden" name="id" value="<?php echo $form_content; ?>" />
				<input type="text" name="email" placeholder="<?php echo Arr::get($cmslabel, 'enter_email'); ?>" <?php if ($info['user_id'] AND $info['user_email']): ?>value="<?php echo $info['user_email']; ?>"<?php endif; ?>>
				<button type="submit" class="btn btn-green btn-unavailable btn-large"><?php echo Arr::get($cmslabel, 'notifyme'); ?></button>
				<span id="field-error-email" class="field_error error" style="display: none"><?php echo Arr::get($cmslabel, 'error_email', 'error_email'); ?></span>
			</form>
			<div class="notifyme_success message success-message" style="display: none">
				<?php echo Arr::get($cmslabel, 'notifyme_catalog_ty'); ?>
			</div>
		</div>
	</div>
</div>