<?php $this->extend('auth/default'); ?>

<?php $this->block('h1'); ?>
	<h1 class="a-dashboard-title"><?php echo Arr::get($cmslabel, 'welcome', 'Pozdrav'); ?> <?php echo $user->first_name; ?></h1>
<?php $this->endblock('h1'); ?>

<?php $this->block('content2'); ?>

<div class="clear a-intro">
	<div class="a-intro-left">
		<div class="a-intro-title"><?php echo Arr::get($cmslabel, 'you_can'); ?></div>
		<ul class="list a-menu">
			<li><?php echo str_replace("%LINK%", Utils::app_absolute_url($info['lang'], 'auth', 'my_webshoporder', false), Arr::get($cmslabel, 'auth_view_orders', '')); ?></li>
			<li><?php echo str_replace("%LINK%", Utils::app_absolute_url($info['lang'], 'auth', '', FALSE), Arr::get($cmslabel, 'auth_view_coupons', '')); ?></li>
			<li><?php echo str_replace("%LINK%", Utils::app_absolute_url($info['lang'], 'auth', 'edit', FALSE), Arr::get($cmslabel, 'auth_edit_profile', '')); ?></li>
			<li><?php echo str_replace("%LINK%", Utils::app_absolute_url($info['lang'], 'auth', 'change_password', FALSE), Arr::get($cmslabel, 'auth_change_password', '')); ?></li>
			<li><?php echo str_replace("%LINK%", Utils::app_absolute_url($info['lang'], 'auth', 'logout', FALSE), Arr::get($cmslabel, 'auth_logout', '')); ?></li>
		</ul>
	</div>

	<div class="a-intro-user">
		<div class="a-intro-title"><?php echo $user->first_name; ?> <?php echo $user->last_name; ?></div>
		<p>
			<span class="a-intro-email"><?php echo $user->email; ?></span><br>
			<?php echo $user->phone; ?>
		</p>
		<?php if($user->address): ?>
		<p>
			<?php echo $user->address; ?><br>
			<?php echo $user->zipcode; ?> <?php echo $user->city; ?>
		</p>
		<?php endif; ?>
		
		<?php if ($user->loyalty_code): ?>
			<div class="a-intro-loyalty">
				<strong><?php echo Arr::get($cmslabel, 'card_number'); ?>:</strong> <?php echo $user->loyalty_code; ?> 
				<br><strong><?php echo Arr::get($cmslabel, 'loyalty_points', 'Broj loyalty bodova'); ?>:</strong> <?php echo $user->loyalty_point; ?> 
				<br><strong><?php echo Arr::get($cmslabel, 'loyalty_discount', 'Ostvareni popust'); ?>:</strong> <?php echo $user->loyalty_discount_percent; ?>%		
			</div>
		<?php endif; ?>
		
		<p><a class="btn btn-auth-edit" href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'edit', FALSE); ?>"><span><?php echo Arr::get($cmslabel, 'edit_data'); ?></span></a></p>
	</div>	
</div>

<!-- Orders -->
<?php $orders = $user->get_webshoporder(TRUE, 5); ?>
<?php if(count($orders)): ?>
	<div class="auth-box orders-container" id="orders">
		<h2 class="a-section-title"><a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'my_webshoporder', FALSE); ?>"><?php echo Arr::get($cmslabel, 'latest_orders'); ?></a></h2>
		<?php echo View::factory('auth/widgetlist/webshoporder', array('items' => $orders, 'dashboard' => true)); ?>
	</div>
<?php endif; ?>
<?php $this->endblock('content2'); ?>

<?php $this->block('extrabody'); ?>
	<?php echo Html::media('cmsauth,barcode', 'js'); ?>
	<script>
		$(function(){
			<?php if (!empty($loyalty_display)): ?>$("#loyalty_code").barcode("<?php echo $loyalty_display[0]; ?> ", "<?php echo $loyalty_display[1]; ?>", {barWidth:2, barHeight:50});<?php endif; ?>
		});
	</script>
<?php $this->endblock('extrabody'); ?>