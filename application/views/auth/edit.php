<?php $this->extend('auth/default'); ?>

<?php $this->block('content2'); ?>
	<form method="post" action="" accept-charset="utf-8" enctype="multipart/form-data" name="edit" class="ajax_siteform form-label auth-form auth-edit-profile-form">
		<div class="global_error global-error" style="display: none"><?php echo Arr::get($cmslabel, 'form_validation_error'); ?></div>
		<fieldset>
			<?php foreach ($customer_fields as $field): ?>
				<?php $error = Valid::get_error($field, $message); ?>
				<?php if($field == 'company_name'): ?>
					<div class="a-edit-subtitle a-edit-subtitle-company"><?php echo Arr::get($cmslabel, 'company_data'); ?></div>
				<?php endif; ?>
				<?php if ($field == 'healthy_institution'): ?>
					<div class="healthy_fields" style="display: none;">
				<?php endif; ?>
					<p class="field-<?php echo $field; ?>">
						<?php if($field == 'newsletter'): ?>
							<?php echo $item->input($field, 'form'); ?>	
							<label for="field-<?php echo $field; ?>" class="label-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, $field); ?><?php if (in_array($field, $request_fields)): ?> *:<?php endif; ?></label>
						<?php else: ?>
							<?php echo $item->input($field, 'form'); ?>	
							<label for="field-<?php echo $field; ?>" class="label-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, $field); ?><?php if (in_array($field, $request_fields)): ?> *:<?php endif; ?></label>
						<?php endif; ?>
						<span id="field-error-<?php echo $field; ?>" class="field_error error" <?php if (!$error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, "error_{$error}", $error); ?></span>
					</p>
				<?php if ($field == 'healthy_speciality'): ?>
					</div>
				<?php endif; ?>	
			<?php endforeach; ?>
		</fieldset>
		<p class="submit"><button type="submit"><?php echo Arr::get($cmslabel, 'save'); ?></button></p>
		<p class="req"><?php echo Arr::get($cmslabel, 'request_fields'); ?></p>
	</form>
<?php $this->endblock('content2'); ?>
