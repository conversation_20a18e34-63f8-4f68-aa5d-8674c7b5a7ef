<?php $this->extend('auth/default'); ?>

<?php $this->block('content2'); ?>
	<form method="post" action="" accept-charset="utf-8" enctype="multipart/form-data" name="edit" class="ajax_siteform form-label auth-form auth-change-password-form">
		<div class="global_error global-error" style="display: none"><?php echo Arr::get($cmslabel, 'form_validation_error'); ?></div>
		<fieldset>
			<?php foreach ($customer_fields as $field): ?>
				<?php $error = Valid::get_error($field, $message); ?>
				<p class="field-<?php echo $field; ?>">
					<?php $label = (Arr::get($cmslabel, 'reset_'.$field)) ? Arr::get($cmslabel, 'reset_'.$field) : Arr::get($cmslabel, $field, $field); ?>
					<?php echo $item->input($field, 'form'); ?>
					<label for="field-<?php echo $field; ?>" class="label-<?php echo $field; ?>"><?php echo $label; ?></label>
					<span id="field-error-<?php echo $field; ?>" class="field_error error" <?php if (!$error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, "error_{$error}", $error); ?></span>
				</p>
			<?php endforeach; ?>
		</fieldset>
		<p class="submit"><button type="submit"><?php echo Arr::get($cmslabel, 'save'); ?></button></p>
	</form>
<?php $this->endblock('content2'); ?>