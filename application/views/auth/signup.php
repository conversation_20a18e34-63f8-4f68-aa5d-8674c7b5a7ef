<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-auth-login page-signup<?php $this->endblock('page_class'); ?>

<?php $this->block('main_class'); ?> pal-main <?php $this->endblock('main_class'); ?>

<?php $this->block('sidebar'); ?> <?php $this->endblock('sidebar'); ?>
<?php $this->block('breadcrumb'); ?> <?php $this->endblock('breadcrumb'); ?>

<?php $this->block('content_layout'); ?>
<h1><?php echo Arr::get($cms_page, 'seo_h1'); ?></h1>

<div class="pal-main-content clear">
	<div class="pal-left">
		<?php if ($message_type AND $message): ?>
			<?php if (is_array($message)): ?>
				<p class="global-<?php echo $message_type; ?>"><?php echo Arr::get($cmslabel, 'form_validation_error'); ?></p>
			<?php else: ?>
				<p class="global-<?php echo $message_type; ?>"><?php echo Arr::get($cmslabel, "{$message_type}_{$message}"); ?></p>
			<?php endif; ?>
		<?php endif; ?>

		<?php if ($message_type != 'success'): ?>
			<!-- Signup form -->
			<h2><?php echo Arr::get($cmslabel, 'register_info'); ?></h2>
			<form method="post" action="" accept-charset="utf-8" enctype="multipart/form-data" name="signup" class="ajax_siteform form-label auth-form auth-signup-form">
				<div class="global_error global-error" style="display: none"><?php echo Arr::get($cmslabel, 'form_validation_error'); ?></div>
				<fieldset>
					<?php foreach ($customer_fields as $field): ?>
						<?php $error = Valid::get_error($field, $message); ?>
						<?php /* if($field == 'first_name'): ?>
							<h2 class="special"><?php echo Arr::get($cmslabel, 'personal_data'); ?></h2>
						<?php endif; */ ?>
						<?php if ($field == 'healthy_institution'): ?>
							<div class="healthy_fields" style="display: none;">
						<?php endif; ?>
						<p class="field-<?php echo $field; ?>">
							<?php if (in_array($field, ['healthy_worker', 'newsletter', 'accept_terms'])): ?>
								<?php echo $item->input($field, 'form'); ?>
								<label for="field-<?php echo $field; ?>" class="label-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, $field, $item->meta()->field($field)->label); ?><?php echo (in_array($field, $request_fields) ? '' : ''); ?></label>
							<?php else: ?>
								<?php echo $item->input($field, 'form'); ?>
								<label for="field-<?php echo $field; ?>" class="label-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, $field, $item->meta()->field($field)->label); ?><?php echo (in_array($field, $request_fields) ? '' : ''); ?></label>
							<?php endif; ?>
							<span id="field-error-<?php echo $field; ?>" class="field_error error" <?php if (!$error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, "error_{$error}", $error); ?></span>
						</p>
						<?php if ($field == 'healthy_speciality'): ?>
							</div>
						<?php endif; ?>							
					<?php endforeach; ?>
				</fieldset>
				<p class="submit"><button type="submit"><?php echo Arr::get($cmslabel, 'submit'); ?></button></p>
			</form>
			<!-- / Signup form -->

			<!-- Social auth -->
			<?php if (Kohana::config('app.auth.use_usersocials') === TRUE): ?>
				<div class="auth-social">
					<?php $usersocial_base = Utils::app_absolute_url($info['lang'], 'auth', 'hybridauth', FALSE); ?>
					<?php foreach (Kohana::config('authsocial.providers') as $provider => $provider_config): ?>
						<?php if (Arr::get($provider_config, 'enabled')): ?>
							<div class="auth-social-<?php echo strtolower($provider); ?>"><a href="<?php echo $usersocial_base; ?>?provider=<?php echo $provider; ?>">Sign-in with <?php echo $provider; ?></a></div>
						<?php endif; ?>
					<?php endforeach; ?>
				</div>
			<?php endif; ?>
			<!-- / Social auth -->

		<?php endif; ?>
	</div>

	<div class="pal-right">
		<?php echo Arr::get($cms_page, 'content'); ?>
		<div class="login-section clear">
			<div class="ls-left"><?php echo Arr::get($cmslabel, 'signup_content_button'); ?></div>
			<div class="ls-right"><?php echo Arr::get($cmslabel, 'signup_forgotten'); ?></div>
		</div>
	</div>
</div>
<?php $this->endblock('content_layout'); ?>