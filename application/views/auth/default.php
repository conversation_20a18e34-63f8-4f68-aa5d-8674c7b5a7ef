<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-auth<?php $this->endblock('page_class'); ?>

<?php $this->block('breadcrumbs'); ?> <?php $this->endblock('breadcrumbs'); ?>
<?php $this->block('sidebar'); ?> <?php $this->endblock('sidebar'); ?>

<?php $this->block('main_class'); ?> auth-main<?php $this->endblock('main_class'); ?>

<?php $this->block('content_layout'); ?>
<?php $this->block('h1'); ?><h1><?php echo Arr::get($cms_page, 'seo_h1'); ?></h1><?php $this->endblock('h1'); ?>

<?php if ($user): ?>
	<ul class="auth-menu fz0">
		<li><a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', '', FALSE); ?>" <?php if (Utils::app_absolute_url($info['lang'], 'auth', '', FALSE) == $info['url']): ?> class="active"<?php endif; ?>><?php echo Arr::get($cmslabel, 'my_profile'); ?></a></li>
		<li<?php if (Utils::app_absolute_url($info['lang'], 'auth', 'my_webshoporder', false)== $info['url']): ?> class="selected"<?php endif; ?>><a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'my_webshoporder', false); ?>#main"><?php echo Arr::get($cmslabel, 'my_orders'); ?></a></li>
		<?php if (Kohana::config('app.catalog.use_wishlists')): ?>
			<li><a href="<?php echo Utils::app_absolute_url($info['lang'], 'catalog', 'wishlist'); ?>" <?php if (Utils::app_absolute_url($info['lang'], 'webshop', 'wishlist') == $info['url']): ?> class="active"<?php endif; ?>><?php echo Arr::get($cmslabel, 'my_wishlist'); ?></a></li>
		<?php endif; ?>
		<li><a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'edit', FALSE); ?>" <?php if (Utils::app_absolute_url($info['lang'], 'auth', 'edit', FALSE) == $info['url']): ?> class="active"<?php endif; ?>><?php echo Arr::get($cmslabel, 'edit_profile'); ?></a></li>
		<li><a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'change_password', FALSE); ?>" <?php if (Utils::app_absolute_url($info['lang'], 'auth', 'change_password', FALSE) == $info['url']): ?> class="active"<?php endif; ?>><?php echo Arr::get($cmslabel, 'change_password'); ?></a></li>
		<li><a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'logout', FALSE); ?>?redirect=<?php echo $info['redirect_url']; ?>"><?php echo Arr::get($cmslabel, 'logout'); ?></a></li>
	</ul>
<?php endif; ?>

<?php $message_type = (isset($message_type) AND $message_type) ? $message_type : Arr::get($info, 'message_type', ''); ?>
<?php $message = (isset($message) AND $message) ? $message : Arr::get($info, 'message', ''); ?>
<?php if ($message_type AND $message): ?>
	<?php if (is_array($message)): ?>
		<p class="global-<?php echo $message_type; ?>"><?php echo Arr::get($cmslabel, 'form_validation_error'); ?></p>
	<?php else: ?>
		<p class="global-<?php echo $message_type; ?>"><?php echo Arr::get($cmslabel, "{$message_type}_{$message}", "{$message_type}_{$message}"); ?></p>
	<?php endif; ?>
<?php endif; ?>

<?php echo Arr::get($cms_page, 'content'); ?>

<?php $this->block('content2'); ?><?php $this->endblock('content2'); ?>

<?php $this->endblock('content_layout'); ?>