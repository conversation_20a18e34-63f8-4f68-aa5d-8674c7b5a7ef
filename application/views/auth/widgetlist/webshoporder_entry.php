<?php foreach ($items as $item): ?>
	<div class="order-row clear fz0" id="order-<?php echo $item->number; ?>">
		
		<!-- Order Item -->
		<a class="table-order" href="javascript:toggleBox(['div#order-<?php echo $item->number; ?>', 'div#btn-order-<?php echo $item->number; ?>']);">
			<div class="table-col col-order-num"><?php echo $item->number; ?> - <?php echo Date::humanize($item->datetime_created); ?></div>
			<div class="table-col col-order-status"><?php echo Arr::get($cmslabel, 'status'); ?>: <?php echo $item->status->title($item->lang); ?></div>
			<div class="table-col col-order-total"><?php echo Arr::get($cmslabel, 'total'); ?>: <?php echo Utils::currency_format($item->total * $item->exchange, $item->currency->display); ?><?php echo Utils::get_second_pricetag_string($item->total, ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></div>
			<div class="table-col col-order-btns">
				<div class="btn-order-details" id="btn-order-<?php echo $item->number; ?>">
					<span class="btn-inactive"><?php echo Arr::get($cmslabel, 'webshop_btn_order_details', 'Prikaži detalje'); ?></span>
					<span class="btn-active"><?php echo Arr::get($cmslabel, 'webshop_btn_hide_order_details', 'Sakrij detalje'); ?></span>
				</div>
			</div>
		</a>

		<!-- Order Item Details (subitems) -->
		<?php if ($item->items): ?>
			<?php  $items_qty = Catalog::products(array('lang' => $info['lang'], 'filters' => array('id' => $item->items->as_array(NULL, 'product_id')))); ?>
			<div class="order-details clear">
				<div class="w-table w-table-details">
					<?php foreach ($item->items AS $item_item): ?>
						<?php $item_shopping_cart_code = (!empty($items_qty[$item_item->product_id]['shopping_cart_code'])) ? $items_qty[$item_item->product_id]['shopping_cart_code'] : '' ?>
						<div class="wp wp-details fz0">
							<div class="wp-image">
								<figure>
									<img <?php echo Thumb::generate($item_item->main_image, array('width' => 100, 'height' => 150, 'html_tag' => TRUE, 'default_image' => '/media/images/no-image-200.jpg')); ?> alt="<?php echo $item_item->title; ?>" data-product_main_image="<?php echo $item_shopping_cart_code; ?>" />
								</figure>
							</div>
							<div class="wp-cnt">
								<h2 class="wp-title" data-product_title="<?php echo $item_shopping_cart_code; ?>"><?php echo $item_item->title; ?></h2>
								<div class="wp-code"><span><?php echo Arr::get($cmslabel, 'code'); ?>:</span> <span data-product_code="<?php echo $item_shopping_cart_code; ?>"><?php echo $item_item->code; ?></span></div>
								<?php if($item_item->description): ?>
									<div class="wp-variations"><?php echo $item_item->description; ?></div>
								<?php endif; ?>
							</div>
							<div class="wp-total">
								<div class="wp-price">
									<?php if ($item_item->basic_price > $item_item->price): ?>
										<span class="wp-price-old product_total_basic" data-product_basic_price="<?php echo $item_shopping_cart_code; ?>"><span><?php echo Utils::currency_format($item_item->basic_price * $item->exchange, $item->currency->display); ?></span><?php echo Utils::get_second_pricetag_string($item_item->basic_price, ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></span>
										<span class="wp-price-current wp-price-discount product_total" data-product_price="<?php echo $item_shopping_cart_code; ?>"><?php echo Utils::currency_format($item_item->price * $item->exchange, $item->currency->display); ?><?php echo Utils::get_second_pricetag_string($item_item->price, ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></span>
									<?php else: ?>
										<span class="wp-price-current product_total" data-product_price="<?php echo $item_shopping_cart_code; ?>"><?php echo Utils::currency_format($item_item->price * $item->exchange, $item->currency->display); ?><?php echo Utils::get_second_pricetag_string($item_item->price, ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></span>
									<?php endif ?>
								</div>
								<div class="wp-qty-count">
									<span class="product_qty"><?php echo $item_item->qty; ?></span> x <?php echo Utils::currency_format($item_item->price * $item->exchange, $item->currency->display); ?><?php echo Utils::get_second_pricetag_string($item_item->price, ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
								</div>
							</div>
						</div>
					<?php endforeach; ?>
					<div class="wp-details-sum">
						<span><?php echo Arr::get($cmslabel, 'total'); ?>: <?php echo Utils::currency_format($item->total * $item->exchange, $item->currency->display); ?><?php echo Utils::get_second_pricetag_string($item->total, ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></span>
					</div>
				</div>
			</div>
		<?php endif; ?>
	</div>
<?php endforeach; ?>