<?php if (count($items)): ?>
	<div class="orders" <?php if (isset($pagination)): ?>id="items_widgetlist_webshop" data-infinitescroll="items_widgetlist_webshop" data-infinitescroll_next_page="<?php echo $pagination->next_page; ?>" data-infinitescroll_total_pages="<?php echo $pagination->total_pages; ?>" data-infinitescroll_auto_trigger="2" <?php endif; ?>>
		<?php echo View::factory('auth/widgetlist/webshoporder_entry', ['items' => $items]); ?>
	</div>

	<?php if(isset($dashboard) AND count($items) > 5): ?>
		<a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'my_webshoporder', FALSE); ?>?all=1"><?php echo Arr::get($cmslabel, 'all_webshoporder', 'Sve narudž<PERSON>'); ?></a>
	<?php else: ?>
		<?php if (isset($pagination)): ?>
			<?php echo $pagination; ?>
			<?php if ($pagination): ?><a href="javascript:void(0);" class="load-more" style="display: none"><?php echo Arr::get($cmslabel, 'load_more', 'Učitaj još'); ?></a><?php endif; ?>
		<?php endif; ?>
	<?php endif; ?>
<?php else: ?>
	<div class="auth-no-orders"><?php echo Arr::get($cmslabel, 'auth_no_orders', 'Nemate zabilježenu nijednu narudžbu.'); ?></div>
<?php endif; ?>