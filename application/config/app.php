<?php

defined('SYSPATH') or die('No direct script access.');

return [
    'sitename' => 'stoma-medical.hr',
    'site_version' => 1,
    //'email_server' => array('info@', ''),
    'email_server' => ['<EMAIL>', 'stoma-medical.hr'],
    //'email_server_reply' => array('info@', ''),
    'email_manager' => [
        'to' => ['<EMAIL>', 'stoma-medical.hr'],
        'bcc_1' => ['<EMAIL>', 'stoma-medical.hr'],
    ],
    'languages' => [
        'hr' => 'Hrvatski',
    //'en' => 'English',
    //'de' => 'Deutsch',
    //'it' => 'Italiano',
    ],
    'language' => 'hr',
    'is_site_multilingual' => FALSE,
    'context' => ['cms.redirect', 'cms.labels', 'webshop.shoppingcart_info', 'gdpr.configurator', 'catalog.currency'],

    /* cms */
    'cmspage_templates' => [
        '' => 'Osnovni',
        'cms/homepage' => 'Naslovnica',
        'cms/faq' => 'Česta pitanja',
        'cms/zastitna' => 'Zaštitna',
        'siteforms/contact' => 'Kontakt',
        'siteforms/prescriptionorder' => 'Narudžba uz doznaku',
    ],
    'cmspage_forms' => [
    /* syntax: 'template' => 'class', */
    'siteforms/prescriptionorder' => 'Siteform_PrescriptionOrder',
    ],
    'cmspage_access_permission' => array(
        0 => 'Svi',
        3 => 'Medicinsko osoblje',
        4 => 'Zaposlenici',
    ),
    'cms' => [
        'menu_use_style' => TRUE,
    ],
    'search' => [
        'modules' => ['publish', 'cms', 'catalog'],
    ],
    'rotator' => [
        'element_use_title' => true, // polje za naslov
        'element_use_url' => true, // polje za url
        'element_use_image' => TRUE, // polje za unos slike
        'element_images' => 2, // broj slika
        'element_use_title2' => true, // polje za dodatni naslov
        'element_use_content' => true, // polje za sadržaj
    ],
    /* module publish */
    'publish' => [
        'template' => [
            'choices' => [
            //'news' => 'Novosti',
            //'articles' => 'Članci',
            //'gallery' => 'Fotogalerija',
            ],
        ],
        'publish_use_related' => true,
        'use_relatedproducts' => true,
        'publish_use_headline' => true, // nadnaslov objave
        'per_page' => 10,
        'per_page_extracontent' => [
            'index_1' => 10, // broj postova koji se prikazuje kod prvog učitavanja. Svako iduće učitavanje postova koristi per_page broj
        ],
        'category_use_subtitle' => true, // podnaslov kategorije
        'category_access_permission' => array(
            0 => 'Svi',
            3 => 'Medicinsko osoblje',
            4 => 'Zaposlenici',
        ),
        'category_access_always_visible' => 2, // do kojeg permissiona svi vide clanke (ovisno o integraciji, moze se vidjeti cijeli ili samo dio sadrzaja)
        'publish_access_permission' => array(
            0 => 'Svi',
            3 => 'Medicinsko osoblje',
            4 => 'Zaposlenici',
        ),
        'publish_access_always_visible' => 2, // do kojeg permissiona svi vide clanke (ovisno o integraciji, moze se vidjeti cijeli ili samo dio sadrzaja)

    ],
    'catalog' => [
        // postavljeno defaultno sortiranje (prema internom dogovoru 2016-09-12)
        'product_priorities' => [
            1 => [
                'code' => 'available',
                'title' => 'Dostupno',
            ],
            2 => [
                'code' => 'new',
                'title' => 'Novo',
                'older' => 90 * 86400, // 3 dana
            ],
            3 => [
                'code' => 'action',
                'title' => 'Akcija',
            ],
        ],
        'order_default' => ['priority', 'new'],
        'product_use_firstlevel_url' => TRUE,
        'use_attributes' => TRUE,
        'use_attributeitems' => TRUE,
        'attribute_models' => [
            /* syntax: model => title */
            'catalogproduct' => 'Proizvod',
            //'catalogproductvariation' => 'Varijacija',
        ],
        'attribute_fields' => ['select', 'selectm'],
        'attributeitem_use_image' => TRUE, 
        'product_use_short_description' => true, // kratak opis
        'product_use_disable_added' => TRUE,
        //'product_variation_request' => TRUE,
        'product_use_internal_code' => true,
        //'use_variations' => true,
        //'use_productvariations' => true,
        //'variation_images' => 0,
        //'variation_update_product_price' => true,
        //'variation_update_product_price_only_available' => false,
        'use_relateds' => true, // povezani proizvodi
        'use_productrelateds' => true, // povezani proizvodi
        'use_productrelatedpublishes' => true,
        'use_relatedrules' => true,
        'category_use_image' => true, // slike kategorije
        'category_images' => 2,
        'category_use_short_description' => true, // kratak opis kategorije
        'product_import_allow_swap_codes' => true, // ugasiti nakon korištenja
        'product_description_elements' => [
            'diagnosis' => 'Dijagnoza',
            'sizes' => 'Veličine',
        ],
        'per_page' => 6,
        'use_productpricelowests' => true,
        'use_older_lowest_price' => true,
        'use_older_lowest_price_v2' => true,
        'product_extra_prices' => [
            'lowest' => 'Najniža cijena u zadnjih 30 dana',
        ],
        'product_use_package' => true, // npr. cijena proizvoda je za 1 kom, ali se prodaje samo u paketu od 25 kom, mora se onemogućiti naručivanje manje količine
    ],
    'webshop' => [
        'login_on_first_step' => TRUE,
        'skip_shipping_step_login_user' => TRUE, // preskakanje koraka za unos adresu dostave za registrirane korisnike
        'use_b_r1' => true,
        'use_b_company' => true,
    ],
    'location' => [
        'locationpoint_use_webshoporder' => true, // omoguci slanje webshop narudzbi
    ],
    'auth' => [
        'frontend' => TRUE,
        'usergroup_use_multifactor_login' => true, //koristi 2FA za grupe korisnika
        'multifactor_login' => [
            'code_expiration_time' => 300, //vremensko trajanje TOTP-a u sekundama
            'max_attempts' => 5, //maksimalan broj pokušaja unosa TOTP-a
            'max_attempts_exceded_timeout' => 300, //zabrana prijave ukoliko je premašen max_attemps
        ],
        'usergroup_use_login_email' => true, //slanje mailova prijave u CMS
        'use_profiles' => TRUE,
        'use_usercountries' => TRUE,
        'user_use_healthy_fields' => true,
        'signup_profile_fields' => ['email', 'password', 'password_confirm', 'first_name', 'last_name', 
            'healthy_worker', 'healthy_institution', 'healthy_department', 'healthy_speciality',
            'newsletter', 'accept_terms'],
        'edit_profile_fields' => ['first_name', 'last_name', 'address', 'zipcode', 'city', 'phone', 'company_name', 'company_oib', 'company_address', 'healthy_worker', 'healthy_institution', 
            'healthy_department', 'healthy_speciality', 'newsletter'],
        'levels' => array(
            3 => 'medicinsko osoblje',
            4 => 'zaposlenik',
        ),
        'signup_ip_restriction_period' => '-1 hours',
        'signup_ip_restriction_countries' => ['hr'],
    ],
    'feedback' => [
        'use_notifications' => TRUE,
        'notifications' => [
            'models' => [
                'catalogproduct' => 'Proizvodi',
                //'catalogproductvariation' => 'Varijacije proizvoda',
            ],
        ],
    ],
    'newsletter' => [
        'syncronize_lists' => [
            'list_hr' => [
                'service' => 'mailchimp', // service name
                'list_code' => 'list', // list code
            ],
        ],
    ],
    'utils' => [
        'kn_euro_conversion' => [
            'use_double_pricetags' => 1, // da li na site-u treba aktivirati prikaz dvojnih cijena
            'double_prices_begin_date' => '2022-06-31 00:00:00', // datum početka dvojnog prikaza cijena
            'display_format' => [
                'autocomplete' => ' <ins>|</ins> <dfn>%PRICE%</dfn>',
                'standard' => ' <ins>|</ins> <span>%PRICE%</span>',
                'none' => '%PRICE%',
            ],
            'cart_conversion_fields' => ['total_basic', 'total'], //shopping_cart_preview podaci za koje se treba prosljedit konverzija cijene
            'converted_price_show_in_front' => false, //ukljucit ako se euro prikazuje ispred kune
        ],
        'password_strong_validation' => true,
    ],
    'gdpr' => [
        'gdpr_object_cookie_saving' => true, // u gdpr_cookie se spremaju označene privole
    ],
    'services' => [
        'mailchimp' => [
            'api_key' => '*************************************
            ',
            'service_list_id' => '4cef5c51d8',
            'lang' => 'hr',
        ],
        'maps_googleapis_key' => 'AIzaSyAjYaHeNOp3n59s5uT4kr9g1FwNEx7go10',
    ],
    'syncronize' => [
        'choices' => [
            'newsletter.newsletterlist',
        ],
    ],

    /* module api */
    'api' => [
        'template' => [
            'choices' => [
                'api' => 'API',
            ],
        ],
        'import' => [
            'Catalog' => ['CatalogCategory', 'CatalogManufacturer', 'CatalogProduct'],
        ],
        // moduli i polja koja se ignoriraju kod importa
        'import_exclude_fields' => [
            'CatalogCategory.basic.fields.position_h',
            'CatalogCategory.basic.fields.visible',
            'CatalogCategory.Description.fields.short_decription',
            'CatalogCategory.Description.fields.content',
            'CatalogCategory.Description.fields.seo_title',
            'CatalogCategory.Description.fields.seo_h1',
            'CatalogManufacturer.basic.fields.position',
            'CatalogManufacturer.basic.fields.visible',
            'CatalogManufacturer.Description.fields.short_decription',
            'CatalogManufacturer.Description.fields.content',
            'CatalogManufacturer.Description.fields.seo_title',
            'CatalogManufacturer.Description.fields.seo_h1',
            'CatalogProduct.basic.fields.category',
            'CatalogProduct.basic.fields.discount_percent',
            'CatalogProduct.basic.fields.price',
            'CatalogProduct.basic.fields.visible',
            'CatalogProduct.Description.fields.short_decription',
            'CatalogProduct.Description.fields.content',
            'CatalogProduct.Description.fields.seo_title',
            'CatalogProduct.Description.fields.seo_h1',
        ],
        // moduli i polja koja se ignoriraju kod importa, ali samo ako se radi o azuriranju (ukljucena su i import_exclude_fields polja)
        'import_update_exclude_fields' => [
            'CatalogCategory.Description.fields.title',
            'CatalogManufacturer.Description.fields.title',
            'CatalogManufacturer.basic.fields.disable_added_to_cart',
            'CatalogManufacturer.basic.fields.minimum_qty',
            'CatalogProduct.basic.fields.category',
            'CatalogProduct.basic.fields.manufacturer',
            'CatalogProduct.Description.fields.title',
       ],
        'sync_models' => [
            'catalogcategory', 'catalogmanufacturer', 'catalogproduct',
        ],
    ],   
    'min' => [
        'css_gdefault' => 'fancybox,standard',
        //'js_gdefault' => 'jquery,fancybox,lazyload,cmsshare,cmsstandard,standard',
		'js_gdefault' => 'jquery,fancybox,cmsstandard,cmswebshop,cmsautocomplete,cmsnewsletter,cmsfeedback,owlcarousel2,cmssiteforms,cmsshare,ssm,cmsfilter,cmsinfinitescroll,lazyload,cmscatalog,showpassword,cmscatalogvariation,cmsgdpr,standard',
        'js_gmodernizr' => 'modernizr',
    ],
];
