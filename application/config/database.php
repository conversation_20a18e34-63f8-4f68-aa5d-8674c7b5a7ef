<?php

defined('SYSPATH') OR die('No direct access allowed.');

$host = (isset($_SERVER['HTTP_HOST'])) ? $_SERVER['HTTP_HOST'] : str_replace('/var/www/vhosts/stoma-medical.hr/httpdocs/', 'stoma-medical.hr', dirname(__FILE__));
if (in_array($host, ['stoma-medical.hr', 'www.stoma-medical.hr'])) {
	return [
	    'default' => [
	        'connection' => [
				'database'   => 'stomamedical_web',
				'username'   => 'stomamedical_web',
				'password'   => '=*o6R9$AzOGqdDhma1%w',
	         ],
	    //'caching'      => FALSE, // remove in production
	    //'profiling'    => TRUE, // remove in production
	    ],
	];
} elseif (in_array($host, ['ajz008stoma.markerdev.info'])) {
    return [
        'default' => [
            'connection' => [
                'hostname' => '**************', // 003
                'username' => 'marker-app',
                'password' => 'HNTYj87%*nm91@hnYNm*$kahn7849kN',
                'database' => 'stomamedical_dev',
            ],
        ],
    ];
} else {
    return [
        'default' => [
            'connection' => [
                'database' => 'stomamedical_dev',
            ],
            'caching' => false,
            'profiling' => true,
        ],
    ];
}