# Set environment: production, development, testing, or staging
SetEnv KOHANA_ENV "production"

RewriteEngine On

RewriteCond %{HTTP_USER_AGENT} libwww-perl.*
RewriteRule .* – [F,L]

#RewriteCond %{HTTP_HOST} ^185\.103\.216\.80 # s1
#RewriteCond %{HTTP_HOST} ^185\.103\.216\.81 # s1
#RewriteRule (.*) http://domain.com/$1 [R=301,L]

Options -Indexes
RewriteBase /

# Protect hidden files from being viewed
<Files .*>
	Order Deny,Allow
	Deny From All
</Files>

FileETag MTime Size

# Remove multiple slashes anywhere in URL
RewriteCond %{REQUEST_URI} ^(.*)//(.*)$
RewriteRule . %1/%2 [R=301,L]

# Force https://
RewriteCond %{HTTPS} !=on
RewriteRule ^(.*)$ https://%{HTTP_HOST}/$1 [R=301,L]

# Force non-WWW
RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
RewriteRule ^(.*)$ https://stoma-medical.hr/$1 [R=301,L]

# Disabled PHP execution 
RedirectMatch 403 ^/(media|shared|shared_extra|upload|upload_data|upload_importdata|upload_importfiles|data)/(.*)\.php$

# Redirect static files disabled in Kohana
RewriteCond %{THE_REQUEST} ^.*/index\.(php|aspx) 
RewriteRule ^(.*)$ / [R=301,L] 

# Forcing a trailing slash
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_URI} !(.*)/$
RewriteCond %{REQUEST_URI} !(.*)(\.txt|\.xml|\.json)$
RewriteCond %{REQUEST_URI} !^/(media|shared|shared_extra|upload|upload_data|upload_importdata|upload_importfiles|data)/
RewriteRule ^(.*)$ $1/ [R=301,NC,L]

# Protect application and system files from being viewed
RewriteRule ^(?:application|modules|system)\b.* index.php/$0 [L]

# Allow any files or directories that exist to be displayed directly
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/(media|shared|shared_extra|upload|upload_data|upload_importdata|upload_importfiles|data)/
RewriteCond %{REQUEST_URI} !^/min/$

# Rewrite all other URLs to index.php/URL
RewriteRule .* index.php [L] 

# Updated message (moved to new server)
#RewriteRule / /shared/error/503.php [L]
#RewriteRule ^(.*)index\.php$ /shared/error/503.php [L] 

ErrorDocument 403 " "
ErrorDocument 404 " "

# Security headers
Header set X-XSS-Protection "1; mode=block"
Header set X-Content-Type-Options "nosniff"
Header always append X-Frame-Options SAMEORIGIN
# Guarantee HTTPS for 1 Year including Sub Domains
#Header add Strict-Transport-Security "max-age=31536000; includeSubDomains"

# Disable server signature
#ServerSignature Off
#ServerTokens Prod