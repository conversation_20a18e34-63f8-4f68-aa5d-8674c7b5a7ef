/*------- normalize -------*/
*{margin: 0; padding: 0; border: 0; outline: none; -webkit-tap-highlight-color: transparent; box-sizing: border-box; -webkit-font-smoothing: antialiased;}
html{font-family: sans-serif; /* 1 */ -ms-text-size-adjust: 100%; /* 2 */ -webkit-text-size-adjust: 100%; /* 2 */}
article, aside, details, figcaption, figure, footer, header, hgroup, main, menu, nav, section, summary{display: block;}
audio, canvas, progress, video{display: inline-block; /* 1 */}
audio:not([controls]){display: none; height: 0;}
progress{vertical-align: baseline;}
[hidden], template{display: none;}
a{background-color: transparent; -webkit-text-decoration-skip: objects; /* 2 */}
a:active, a:hover{outline: 0; -webkit-tap-highlight-color: transparent;}
abbr[title]{border-bottom: 1px dotted;}
b, strong{font-weight: bold;}
dfn{font-style: italic;}
mark{background: #ff0; color: #000;}
small{font-size: 80%;}
sub, sup{font-size: 75%; line-height: 0; position: relative; vertical-align: baseline;}
sup{top: -0.5em;}
sub{bottom: -0.25em;}
svg:not(:root){overflow: hidden;}
hr{box-sizing: border-box; height: 0; border-bottom: 1px solid #ccc; margin-bottom: 10px; border-top-style: none; border-right-style: none; border-left-style: none;}
pre{overflow: auto;}
pre.debug{font-size: 14px !important;}
code, kbd, pre, samp{font-family: monospace, monospace; font-size: 1em;}
button, input, optgroup, select, textarea{color: inherit; /* 1 */ font: inherit; /* 2 */ margin: 0; /* 3 */}
button{overflow: visible;}
button, select{text-transform: none;}
button, html input[type="button"], input[type="reset"], input[type="submit"]{-webkit-appearance: button; /* 2 */ cursor: pointer; /* 3 */}
button[disabled], html input[disabled]{cursor: default;}
button::-moz-focus-inner, [type="button"]::-moz-focus-inner, [type="reset"]::-moz-focus-inner, [type="submit"]::-moz-focus-inner{border-style: none; padding: 0;}
input{line-height: normal; border-radius: 0; box-shadow: none;}
input[type="checkbox"], input[type="radio"]{box-sizing: border-box; /* 1 */ padding: 0; /* 2 */}
input[type="number"]::-webkit-inner-spin-button, input[type="number"]::-webkit-outer-spin-button{height: auto;}
[type="search"]::-webkit-search-cancel-button, [type="search"]::-webkit-search-decoration{-webkit-appearance: none;}
input[type=text], input[type=email], input[type=password], input[type=tel], input[type=search]{-webkit-appearance: none;}
input[type=number] {-moz-appearance:textfield; -webkit-appearance: textfield; -ms-appearance: textfield;}
input::-webkit-outer-spin-button, input::-webkit-inner-spin-button {-webkit-appearance: none;}
fieldset{border: none; margin: 0; padding: 0; min-height: 0;}
textarea{overflow: auto; resize: vertical;}
optgroup{font-weight: bold;}
table{border-collapse: collapse; border-spacing: 0;}

ins{text-decoration: none; font-weight: normal; color: @textColor;}
dfn{font-style: normal;}
/*------- /normalize -------*/

@import "defaults.less";

/*------- helpers -------*/
.display-n{display: none;}
.display-ib{display: inline-block;}
.display-b{display: block;}
.display-t{display: table;}
.display-tc{display: table-cell;}
.align-vt{vertical-align: top;}
.align-vm{vertical-align: middle;}
.align-l{text-align: left;}
.align-r{text-align: right;}
.align-c{text-align: center;}
.fz0{font-size: 0;}
.fs-i{font-style: italic;}
.fw-b{font-weight: bold;}
.fw-n{font-weight: normal;}
.float-l, .float-left{float: left;}
.float-r, .float-right{float: right;}
.pos-r{position: relative;}
.pos-a{position: absolute;}
.pos-s{position: static;}
.strong{font-weight: bold;}
.italic{font-style: italic;}
.uppercase{text-transform: uppercase;}
.first{margin-left: 0 !important;}
.last{margin-right: 0 !important;}
.image-left, .alignleft{float: left; margin: 5px 20px 10px 0px;}
.image-right, .alignright{float: right; margin: 5px 0px 10px 20px;}
.align-left{text-align: left;}
.align-right{text-align: right;}
.center{text-align: center;}
.underline{text-decoration: underline;}
.nounderline{text-decoration: none;}
.rounded{border-radius: @borderRadius;}

.red{color: @red;}
.green{color: @green;}
.orange{color: @orange;}

.no-touch{
	.tel, .tel a, a[href^=tel]{color: @textColor; cursor: default;}
}
.list{list-style: none; padding: 0; margin: 5px 0 20px 20px;
	li{position: relative; padding: 4px 0 4px 19px;
		&:before{.pseudo(8px,8px); background: @lblue; border-radius: 100%; top: 14px; left: 0;}
	}
}
.first-title{margin-top: 0; padding-top: 0;}
.extra{font-size: 26px; line-height: 38px;}
.extra-image{margin-left: -100px!important; width: calc(~"100% - -200px")!important; display: block; max-width: 1190px!important; height: auto;}
.fancybox-overlay{z-index: 1000000!important;}
/*------- /helpers -------*/

/*------- selectors -------*/
/*
::-webkit-scrollbar { -webkit-appearance: none; width: 5px; }
::-webkit-scrollbar-thumb {
	background-color: @lightGray; border-radius: 5px;
	box-shadow: 0 0 1px rgba(255,255,255,.5);
}
*/
body{background: #fff; color: @textColor; .font(@fontSize, @lineHeight, @font);}
a{
	color: @linkColor; text-decoration: none; .transition();
	&:hover{text-decoration: none;}
}
ul, ol{margin: 0; padding: 0;}
h1, h2, h3, h4{
	font-weight: 300; padding-bottom: 13px;
	a, a:hover{text-decoration: none;}
}
h1{font-size: 64px; line-height: 68px; padding-bottom: 9px;}
h2{font-size: 44px; line-height: 53px; padding: 25px 0 13px;}
h3{font-size: 34px; line-height: 41px; padding: 25px 0 13px;}
h4{font-size: 24px; line-height: 29px; padding: 20px 0 8px;}
p{padding-bottom: 15px;}
body:not(.page-webshop-thank_you){
	table{
		display: block; width: auto; font-size: 16px; line-height: 20px;
		tbody{border: 1px solid @borderColor2; border-right: 0;}
		tr{
			border-bottom: 1px solid @borderColor2;
			&:last-child{border-bottom: 0;}	
		}
		td{padding: 5px 7px; border-right: 1px solid @borderColor2; width: auto; min-width: 115px;}
	}
}
/*------- /selectors -------*/

/*------- forms -------*/
label{padding: 0 0 4px 0; display: inline-block;}
input, textarea, select{-webkit-appearance: none; padding: 0 20px; border: 1px solid @borderColor2; font-size: 12px; height: 30px; font-family: @font; line-height: normal;}
input:disabled, textarea:disabled, input:disabled+label, .disabled{cursor: not-allowed !important; color: #ccc;}
input:hover, textarea:hover, select:hover, input:focus, textarea:focus, select:focus{border-color: @borderColor/1.3; outline: 0;}
input[type=submit], button{border: none; display: inline-block;}
input[type=checkbox], input[type=radio]{padding: 0; height: auto; border: none;}
textarea{height: 145px; padding: 20px 25px; line-height: 20px; background: @white; font-size: 14px; border-radius: 3px; box-shadow: 0 10px 30px 0 rgba(0,0,0,0.25); width: 100%;}
legend{
	font-size: 16px; line-height: 18px; font-weight: bold;
	a{text-decoration: none;}
}

select{
	display: block; width: 100%; height: 50px; line-height: normal; background: @white; border-radius: 3px; box-shadow: 0 10px 30px 0 rgba(0,0,0,0.25); font-size: 14px; background-image: url(images/icons/arrow.svg); background-repeat: no-repeat; background-size: 11px; background-position: 92% 21px; -moz-appearance:none; -webkit-appearance: none; appearance: none;
	&::-ms-expand{display: none;}
} 

input[type=checkbox], input[type=radio]{position: absolute; left: -9999px; display: inline;}
input[type=checkbox] + label, input[type=radio] + label{cursor: pointer; position: relative; padding: 4px 0 0 38px; min-height: 24px; line-height: 20px; font-size: 14px; text-align: left;}
input[type=checkbox] + label{font-size: 20px; line-height: 28px;}
input[type=radio] + label{padding-top: 5px;}

input[type=checkbox] + label:before{.pseudo(25px, 25px); text-indent: 2px; color: #fff; border: 1px solid @borderColor2; border-radius: 3px; left: 0; text-align: center; top: 0; .icon-check; font: 15px/23px @fonti; .transition(all);}
input[type=radio] + label:before{.pseudo(20px, 20px); border-radius: 50%; color: #fff; border: 1px solid @white; left: 0; text-align: center; top: 0; .transition(all);}
input[type=radio] + label:after{.pseudo(14px,14px); background: @textColor; border-radius: 50%; top: 4px; left: 4px; opacity: 0;}

input[type=checkbox]:checked + label:before{color: @blue;}
input[type=radio]:checked + label:before{background: @white;}
input[type=radio]:checked + label:after{opacity: 1;}


.form-inline{
	font-size: 0;
	p{position: relative;}
	label{display: inline-block;}
	label{width: 140px; font-size: @fontSize; text-align: right; padding-right: 15px;}
	input, input, select, textarea{width: 300px; display: inline-block;}
	textarea{padding: 20px;}
	.error, .field-note, .auth-links{margin-left: 140px; font-size: @fontSize;}
	.field-newsletter, button, .remember, .field-accept_terms, .field-show-password{margin-left: 140px;}
	.field-newsletter, .remember, .field-accept_terms{
		width: 260px;
		label{text-align: left; width: auto;}
	}
	.field-message label{vertical-align: top;}
	.field-optional{position: absolute; font-size: 10px; top: 0; right: 0;}
}

.form-label{
	font-size: 0;
	p, .field{position: relative; padding-bottom: 18px;}
	label{display: inline-block; font-size: 16px; line-height: normal; font-weight: 300; text-align: left;}
	input{
		height: 54px; width: 100%; display: block; line-height: normal; border-radius: 3px; font-size: 18px;
		&.field_error_input{background: url(images/icons/danger-red.svg) no-repeat; background-size: 23px; background-position: 95% 14px;}
	}
	input, select, textarea{
		width: 100%; display: block;
		&:focus{outline: none;}
	}
	input+label, textarea+label{font-size: 16px; position: absolute; pointer-events: none; left: 20px; top: 18px; padding-bottom: 0; .transition(all);}
	input:focus+label, .focus input+label, textarea:focus+label, .focus textarea+label {top: 6px; .scale(0.68); transform-origin: top left; opacity: .8;}
	input[type=checkbox], input[type=radio]{transition: none!important;}
	input[type=checkbox]:focus+label, input[type=radio]:focus+label{top: auto!important; .scale(1)!important; opacity: 1!important;}
	.focus, .ffl-floated{
		input{padding: 12px 20px 0;}
		textarea{padding-top: 25px;}
		input[type=checkbox]+label, input[type=radio]+label{top: auto!important; .scale(1)!important; opacity: 1!important;}
		input[type=checkbox]:checked+label, input[type=radio]:checked+label{top: auto!important; .scale(1)!important; opacity: 1!important;}
	}
	.error, .field-note, .auth-links{margin-left: 18px; font-size: 12px;}
	.error strong{font-weight: 500;}
	.field-newsletter, button, .remember, .field-accept_terms, .field-show-password{margin-left: 140px;}
	.field-newsletter, .remember, .field-accept_terms{
		width: 100%; margin: 7px 0 0;
		label{
			text-align: left; width: auto; position: relative; left: auto; top: auto; font-size: 14px; line-height: 18px; padding: 1px 0 0 33px; font-weight: 500;
			&:before{width: 20px; height: 20px; top: -1px; font-size: 12px; line-height: 20px;}
		} 
		a{text-decoration: underline;}
	}
	.field-optional{position: absolute; font-size: 10px; top: 0; right: 0;}
	input[type=checkbox] + label, input[type=radio] + label{pointer-events: inherit;}
	input[type=radio] + label{padding-top: 2px;}
	.field-company_oib input, .field-b_company_oib input{width: 100%!important;}
	.field-newsletter{padding-bottom: 0;}
	.field-accept_terms{
		.error{margin-left: 33px; padding-left: 25px; position: relative; background: url(images/icons/danger-red.svg) no-repeat; background-size: 15px; background-position: 0 6px;}
	}
}
.field-country, .field-b_country{
	label{display: none!important;}
}
/*------- /forms -------*/

/*------- tables -------*/
.table{
	width: 100%; border-spacing: 0; margin: 10px 0px 20px;
	th{font-weight: bold; font-size: 14px; text-align: left; padding: 6px 0; border-bottom: 1px solid @gray;}
	td{border-bottom: 1px solid @gray; padding: 6px 0;}
	&.stripe tbody tr:nth-child(even){background: #E9E9E9;}
}
.table-row{display: table; width: 100%;}
.table-col{display: table-cell;}
.wide-table{overflow-x: scroll; -webkit-overflow-scrolling: touch;}
/*------- /tables -------*/

/*------- info messages -------*/
.error{color: @errorColor; display: block; padding: 5px 0 0 0; font-size: 14px; line-height: 19px;}
.global-error, .global-success, .global-warning{font-size: 14px; margin: 0 0 15px 0; line-height: 21px; padding: 15px 15px 15px 55px; min-height: 50px; background: @errorColor url(images/icons/danger-white.svg) no-repeat 10px 9px; background-size: 33px auto; color: #fff; position: relative; border-radius: 3px;}
.global-success{background-color: @successColor; background-image: url(images/icons/check.svg); background-size: 20px auto; background-position: 20px center;}
.field_error_input, .field_error_input_radio{border-color: @errorColor; background: #fff url(images/icons/danger.svg) no-repeat right 11px center; background-size: 20px auto; padding-right: 50px;}
/*------- /info messages -------*/

/*------- loading -------*/
.form-loading{position: fixed; top: 0; left: 0; right: 0; bottom: 0; z-index: 999999; color: #fff; font-size: 16px;}
.form-loading span{position: absolute; padding: 50px 20px 20px 20px; width: 200px; left: 50%; margin-left: -100px; top: 40%; text-align: center; box-shadow: 0px 0px 30px #000; color: #000; background: #fff url(images/loader.svg) no-repeat center -16px;}
.form-loading:before{content: ""; top: 0; bottom: 0; left: 0; right: 0; position: absolute; background: #000; background: rgba(0, 0, 0, .5);}
/*------- /loading -------*/

/*------- old browser info -------*/
.browser-note{
	background: #FC3; border-bottom: 1px solid #F90; padding: 8px 15px; font-size: 14px; font-weight: bold; text-align: center; position: fixed; z-index: 9999; top: 0; left: 0; right: 0;
	a{color: #000; text-decoration: underline;}
	a:hover{color: #000;}
	img{margin: 10px 0;}
}
/*------- /old browser info -------*/

/*------- buttons -------*/
.btn, input[type=submit], button{position: relative; display: inline-block; vertical-align: middle; padding: 0 70px; height: 54px; font-size: 16px; line-height: 55px; color: #fff; background: @lblue; text-decoration: none; border-radius: 3px; text-decoration: none !important; font-weight: 500; .transition(all);}
.btn-blue{background: @blue;}
.btn-green{background: @green;}
.btn-special{
	padding: 0 30px 0 30px; height: 50px; line-height: 50px; color: @lblue; font-size: 16px; display: inline-block; vertical-align: middle; border: 1px solid #0090C4; border-radius: 3px; font-weight: bold; margin-right: 10px; margin-top: 15px; text-decoration: none!important; text-transform: none; .transition(all);
	span{
		position: relative; padding-left: 40px;
		&:before{position: absolute; .icon-download; font: 20px/20px @fonti; color: @lblue; left: 0px; top: -1px; .transition(all);}
	}
}
.btn-special-pdf{
	span{
		&:before{position: absolute; .icon-pdf; font: 25px/25px @fonti; color: @lblue; left: 0px; top: -3px; .transition(all);}
	}
}
.btn-border{display: inline-block; vertical-align: top; padding: 0 20px; font-size: 14px; color: @lblue; font-weight: bold; height: 50px; line-height: 50px; border: 1px solid #E0E2DB; border-radius: 3px; font-family: Arial, sans-serif;}
.btn-white{background: @white; border: 1px solid @lblue; color: @lblue;}
.btn-white2{background: @white; border: 1px solid @blue; color: @blue;}
/*------- /buttons -------*/

/*------- owl -------*/
.owl-carousel{
	.owl-stage-outer { position: relative;overflow: hidden; -webkit-transform: translate3d(0px, 0px, 0px); transform: translate3d(0px, 0px, 0px); }
	.owl-stage {position: relative;-ms-touch-action: pan-Y; touch-action: pan-Y; -moz-backface-visibility: hidden; backface-visibility: hidden;}
	.owl-item {position: relative;min-height: 1px;float: left;-webkit-backface-visibility: hidden; backface-visibility: hidden; -webkit-tap-highlight-color: transparent;-webkit-touch-callout: none;}
	.owl-nav.disabled{display: none;}
}
.slider{
	position: relative;
	.owl-nav{position: absolute; height: 40px; width: auto; top: 50%; margin-top: -20px; left: 0; right: 0;}
	.owl-prev, .owl-next{
		width: 40px; height: 40px; line-height: 40px; position: absolute; top: 0; right: 0; cursor: pointer; z-index: 1; font-size: 0;
		&:before{.pseudo(40px,40px); .icon-arrow2; font: 30px/40px @fonti; top: 0; left: 0; text-align: center; color: @textColor; .transition(all);}
		&.disabled{display: none;}
	}
	.owl-prev{
		left: 0; right: auto;
		&:before{transform: rotate(180deg);}
	}
	.owl-stage-outer{z-index: 1;}
}
.animated{-webkit-animation-duration : 1000 ms; animation-duration : 1000 ms; -webkit-animation-fill-mode : both; animation-fill-mode : both;}   
.owl-animated-out{z-index : 1;} 
.owl-animated-in{z-index : 0;}
.fadeOut{-webkit-animation-name : fadeOut; animation-name : fadeOut;}  
@-webkit-keyframes  fadeOut  {
  0%{opacity : 1;}  
  100%{opacity : 0;}  
}
@keyframes  fadeOut  {
  0%{opacity : 1;}  
  100%{opacity : 0;
  }  
}
.animated{animation-duration: .5s;animation-fill-mode: both;}
.animated.infinite{animation-iteration-count: infinite;}
.animated.hinge{animation-duration: 2s;}
.fadeIn{animation-name: fadeIn;}
@keyframes fadeOut {
  from {opacity: 1;}
  to {opacity: 0;}
}
@keyframes fadeIn {
  from {opacity: 0;}
  to {opacity: 1;}
}
@keyframes fadeIn {
  from {opacity: 0;}
  to {opacity: 1;}
}
.fadeOut{animation-name: fadeOut;}
/*------- /owl -------*/

/*------- header -------*/
.overflow{overflow: hidden;}
.mobile-nav-top{
	display: none;
	&.active{display: none;}
}
.mobile-top-menu{display: none;}
.wrapper{width: 1680px; position: relative; margin: auto;}
.header{
	.wrapper{height: 82px;}
}
.logo{position: absolute; display: block; width: 309px; height: 50px; top: 14px; left: 0; background: url(images/logo.png); }
.nav-top{
	font-size: 0; list-style: none; position: absolute; top: 31px; left: 338px;  margin: 0; padding: 0; z-index: 15;
	&>li{display: inline-block; position: relative; font-size: 14px; line-height: 18px;}
	a{display: block; color: #424656; text-decoration: none; padding: 0 7px; padding-bottom: 10px;}
	li.has-children{
		&>a span{padding-right: 11px; position: relative;
			&:before{position: absolute; .icon-arrow; font: 3px/3px @fonti; color: #A6ABBD; right: 0; top: 8px;}
		}
	}
	ul{
		background: @lblue; font-size: 14px; position: absolute; left: -5px; top: 24px; width: 150px; padding: 10px 0; margin: 0; list-style: none; display: none; opacity: 0; .transition(opacity);
		a{color: #fff; padding: 2px 15px;}
	}
	ul ul{top: 0; left: 100%;}
	li.active>ul, li:hover>ul{display: block; opacity: 1;}
	li.selected>a{color: #A6ABBD;}
}
.header-contact{
	font-size: 0; list-style: none; margin: 0; padding: 0; position: absolute; right: 354px; top: 33px; font-family: Arial; 
	li{display: inline-block; vertical-align: top; margin-right: 29px;}
	li:last-child{margin-right: 0;}
}
.item-tel, .item-mail{
	position: relative; display: block; padding-left: 28px; font-size: 14px; line-height: 17px; font-weight: bold; color: #424656; 
	&:before{position: absolute; .icon-tel; font: 16px/16px @fonti; color: @lblue; left: 0px; top: 1px;}
	a{color: #424656;}
}
.item-mail{
	&:before{position: absolute; .icon-email; font: 12px/12px @fonti; color: @lblue; left: 0px; top: 3px;}
}
.m-menu{display: none;}
/*------- /header -------*/

/*------- auth widget -------*/
.aw{position: absolute; right: 208px; top: 28px;}
.aw-login{
	position: relative; display: block; font-size: 14px; line-height: 25px; padding-left: 37px; color: @textColor; 
	&:before{position: absolute; .icon-user; font: 25px/25px @fonti; color: @lblue; left: 0px; top: 1px;}
}
/*------- /auth widget -------*/

/*------- cart widget -------*/
.ww{
	position: absolute; right: 0; top: 10px; opacity: 0.6; pointer-events: none;
	&.active{
		 pointer-events: auto; opacity: 1;
		.ww-items:after{opacity: 1;}
	}
}
.ww-items{
	display: block; width: 166px; height: 60px; background: @green; line-height: 60px; color: #fff!important; text-decoration: none; padding: 0 0 0 58px; font-size: 16px; font-weight: normal; border-radius: 3px; .transition(all);
	&:before{position: absolute; .icon-cart; top: 18px; left: 19px; font: 25px/25px @fonti; color: #fff;}
	&:after{position: absolute; .icon-arrow; font: 7px/7px @fonti; color: #fff; right: 21px; top: 26px; opacity: 0;}
}
.ww-title{display: block;}
.ww-counter{display: block; background: #526BA3; text-align: center; font-weight: bold; font-family: Arial, sans-serif; position: absolute; height: 19px; width: 19px; line-height: 15px; font-size: 11px; top: 10px; left: 25px; color: #fff; border-radius: 100%; border: 2px solid #fff; z-index: 1;}
.ww-preview{
	display: none; z-index: 100; position: absolute; top: 60px; background: #fff; width: 340px; box-shadow: 0px 0px 20px rgba(0, 0, 0, .2); right: 0; padding: 20px; font-size: 15px; border-radius: @borderRadius;
	&:before{.pseudo(10px, 10px); background: #fff; .rotate(45deg); right: 127px; top: -3px;}
	.btn{display: block; text-align: center;}
	.continue-shopping{display: block; width: 100%; text-align: center; font-size: 16px; line-height: 22px; padding-bottom: 10px;}
	.ww-totals{margin-bottom: 20px;}
}
@media screen and (max-width: 1200px) {
	.ww-preview{display: none!important;}
}
.ww-preview-table{
	width: 100%; font-size: 15px!important;
	tbody{display: block; width: 100%; border: none!important;}
	tr{padding-bottom: 5px; display: block;}
	td{padding-bottom: 0!important; border: none!important; min-width: inherit!important;}

}
.ww-preview-items{overflow: auto; max-height: 250px;}
.ww-image{
	width: 75px; text-align: center;
	a{display: block; width: 75px; height: auto; line-height: inherit;}
	img{display: inline-block; vertical-align: middle; max-width: 100%; height: auto;}
}
.ww-c-title{
	font-size: 16px; line-height: 20px; padding-bottom: 3px;
	a{text-decoration: none; color: @textColor;}
}
.ww-price{
	font-size: 16px; line-height: 20px;
	abbr{font-size: 13px;}
}
.ww-old-price{
	font-size: 13px; color: gray; display: block;
	span{text-decoration: line-through;}
}
.ww-discount-price{font-weight: bold; color: @red;}
.ww-lowest-price{font-size: 12px; line-height: 16px; margin-top: 3px;}
.ww-totals{margin-bottom: 13px; padding-top: 10px; font-size: 13px; line-height: 23px; border-top: 1px solid @borderColor2; text-align: right;}
.ww-total{font-weight: 500;}
.ww-value{display: inline-block; vertical-align: top; width: 115px;}
.ww-btn-view{margin-bottom: 10px;}
/*------- /cart widget -------*/

/*------- categories -------*/
.categories-cnt{background: @blue; position: relative; z-index: 10;}
.categories{
	font-size: 0; margin: 0; padding: 0; list-style: none; font-weight: bold; position: relative;
	&>li{
		display: inline-block; vertical-align: top; margin-right: 66px; position: relative;
		@media (max-width: @l){margin-right: 35px;}
		@media (max-width: @t){margin-right: 20px;}
		&>a{
			font-size: 24px; line-height: 84px;
			@media (max-width: @d){font-size: 20px; line-height: 62px;}
			@media (max-width: @t){font-size: 16px; line-height: 55px; font-weight: 500;}
		}
		&>a span{
			position: relative;
			&:after{ .pseudo(auto,4px); background: #fff; bottom: -10px; left: 0; right: 0; opacity: 0; .transition(all);}
		}
		&.active{
			&>a span:after{opacity: .2;}
		}
	}
	li.has-children{
		&>a span{
			padding-right: 22px;
			&:before{
				position: absolute; .icon-arrow; font: 7px/7px @fonti; color: #fff; right: 0; top: 14px; opacity: 0.3;
				@media (max-width: @d){top: 11px;}
				@media (max-width: @t){top: 8px;}
			}
			&:after{right: 22px;}
		}
		&.active{
			.subcategories-container{display: flex;}
		}
	}	
	a{
		color: #fff; font-size: 20px; line-height: 25px; display: block;
		@media (max-width: @d){font-size: 18px; line-height: 22px;}
		@media (max-width: @t){font-size: 14px; line-height: 20px;}
	}
	ul{
		padding: 12px 0 30px; margin: 0; list-style: none; width: 450px;
		@media (max-width: @d){width: 400px;}
		@media (max-width: @t){width: 300px;}
		@media (max-width: @tp){width: 250px;}
		a{
			color: #fff; padding: 7px 70px 7px 50px;
			@media (max-width: @t){padding: 5px 40px 5px 30px;}
		}
		li.has-children{
			&>a{
				position: relative;
				&:after{
					.pseudo(12px,12px); .icon-arrow; font: 7px/1 @fonti; color: #6c80a5; .rotate(-90deg); right: 45px; top: 13px; .transition(color);
					@media (max-width: @t){right: 20px; top: 9px;}
				}
			}
		}
		li.active{
			&>a{
				color: #01BBCF;
				&:after{color: #01BBCF;}
			}
		}
	}
	ul ul{display: none; }
}
.subcategories-container{
	background: @blue; position: absolute; left: -50px; top: 84px; display: none; box-shadow: 0 10px 10px 0 rgba(0, 0, 0, 0.5); border-top: 1px solid rgba(255,255,255,0.2);
	@media (max-width: @d){top: 62px;}
	@media (max-width: @t){top: 55px; left: -30px;}
	ul{border-left: 1px solid rgba(255,255,255,0.2);}
	.categories-level2{border-left: 0;}
}
.subcategories-col{display: flex;}
/*------- /categories -------*/

/*------- search widget -------*/
.sw{position: absolute; right: 0; top: 15px;}
.sw-form{
	width: 527px; position: relative;
	&:before{position: absolute; .icon-search; font: 16px/16px @fonti; color: #A6ABBD; left: 20px; top: 19px;}
}
.sw-input{height: 54px; width: 100%; font-size: 16px; line-height: normal; border-radius: 3px; padding: 0 85px 0 51px; .placeholder(#4A4A4A,#ccc);}
.sw-btn{width: 78px; height: 44px; line-height: 44px; font-size: 14px; color: #fff; font-weight: 500; border-radius: 3px; position: absolute; top: 5px; right: 5px; padding: 0; background: @lblue; .transition(all);}
/*------- /search widget -------*/

/*------- autocomplete -------*/
.ui-autocomplete{
	background: @white; box-shadow: 0 0 20px 0 rgba(0,0,0, 0.3); padding: 0; margin: 0;
	li{
		display: block; padding-left: 0; border-bottom: 1px solid @borderColor2;
		&:before{display: none;}
	}
	a{display: flex; width: 100%; padding: 9px 15px; font-size: 0; align-items: center;}
	.image{
		width: 80px; flex-grow: 0; margin-right: 15px; flex-shrink: 0; height: 80px; text-align: center; display: flex; align-items: center;
		img{display: block; width: auto; height: auto; max-width: 100%; margin: 0 auto;}
	}
	.autocomplete-cnt{flex-grow: 1; font-size: 18px; line-height: 26px; font-weight: 400; align-self: center;}
	.search-title{display: block; width: 100%; margin-bottom: 0; color: @textColor;}
	.search-price{display: block;}
}
.autocomplete-showall{
	padding: 5px!important;
	a{.btn; text-align: center;}
	.ui-corner-all{
		background: @lblue; color: @white; text-align: center; font-size: 18px; font-weight: 400; line-height: 44px; cursor: pointer;
		strong{font-weight: 400;}
	}
}
/*------- /autocomplete -------*/

/*------- main -------*/
.wrapper2{width: 990px; margin: auto;}
.main{padding: 70px 0 80px; display: flex; flex-direction: row-reverse;}
.page-homepage, .page-publish-index, .page-publish-detail, .page-shopping-cart, .page-search, .page-thankyou {
	.main{display: block; flex-direction: none;}
}
.main-content{
	width: 1310px; float: right; padding-left: 120px;
	h1, h2, h3, h4{
		color: @blue2;
		a{color: @blue2;}
	}
}
.sidebar{width: 370px; float: left; border-right: 1px solid @borderColor2;}
.cms-content{
	font-weight: 300; padding-top: 20px;
	a{text-decoration: underline;}
	img{margin: 15px 0; width: auto; height: auto; max-width: 100%;}
	ul{
		.list;
	}
	ol{margin: 0 0 20px 38px;}
	.btn{margin: 15px 0;}
}
.image-wrapper{
	img{margin-left: -100px; margin-bottom: 0; display: block; height: auto;}
}
.image-title{text-align: center; font-size: 14px; line-height: 22px; opacity: 0.4; padding: 15px 0;}
.blue2{color: @blue2};
.blue3{color: @blue3};
.blue4{color: @blue4};
.purple1{color: @purple1};
.purple2{color: @purple2};
.purple3{color: @purple3};
.green2{color: @green2};
.green3{color: @green3};
.yellow2{color: @yellow2};
.red2{color: @red2};
.orange2{color: @orange2};
.table{
	position: relative; display: block; width: auto!important;
	tbody{border-color: @textColor;}
	th{border-bottom: 0; border-right: 1px solid @textColor; font-weight: normal; color: @white; background: @blue3; padding: 10px 12px; text-align: center; font-size: 16px;}
	tr{
		border-bottom: 1px solid @textColor;
		&:nth-child(odd){
			td{background: #e1f3fb;}
		}
	}
	td{padding: 6px 12px; text-align: center; border-bottom: 0; border-right-color: @textColor;}
}
/*------- /main -------*/

/*------- sideabar -------*/
.cms-nav{
	list-style: none; margin: 0; padding: 0;
	&>li{
		margin-bottom: 20px;
		&.selected{
			&>a span:before{opacity: 0.2;}
		}
		&>a{
			font-size: 22px; line-height: 30px; font-weight: bold; color: @blue!important;
			span{
				position: relative;
				&:before{ .pseudo(auto,4px); background: #424656; left: 0; bottom: -5px; right: 0; opacity: 0; .transition(all);}
			}
		}
	}
	li.news{display: none;}
	a{
		display: block; color: #424656;
		&.active{color: @lblue;}
	}
	ul{
		font-size: 18px; line-height: 24px; list-style: none; margin: 0; padding: 0 0 0 15px;
		ul{font-size: 16px; line-height: 21px;}
	}
	li>ul>li{margin-top: 9px;}
	ul ul{
		margin-bottom: 15px;
		li{margin-top: 10px;}
	}
}
/*------- /sideabar -------*/

/*------- breadcrumbs -------*/
.bc{
	padding: 0 0 6px; font-size: 14px; line-height: 16px; color: #B3B8C7; 
	a{
		text-decoration: none; padding: 0 13px 0 0; margin: 0 6px 0 0; position: relative; color: #2C497E; 
		&:before{position: absolute; .icon-arrow; font: 4px/4px @fonti; color: #AAAFC0; right: 0; top: 7px; .rotate(-90deg);}
	}
}
/*------- /breadcrumbs -------*/ 

/*------- share -------*/
.share-cnt{margin-top: 30px;}
.share-standard{
	float: left;
	.item{display: block;}
}
.share{font-size: 0;}
.share-title{display: block; vertical-align: top; font-size: 14px; line-height: 20px; margin-bottom: 8px;}
.share-item{
	display: inline-block; vertical-align: top; width: 32px; height: 32px; background: @lblue; position: relative; border-radius: 3px; margin-right: 8px; cursor: pointer; .transition(all);
	&:before{position: absolute; .icon-facebook; font: 14px/14px @fonti; color: #fff; left: 12px; top: 9px;}
}
.ss_googleplus{
	&:before{.icon-google-share; font-size: 14px; line-height: 14px; left: 8px;}
}
.ss_email{
	&:before{.icon-email; font-size: 10px; line-height: 10px; left: 9px; top: 11px;}
}
.ss_whatsapp{
	&:before{.icon-whatsapp; left: 10px; font-size: 15px; line-height: 15px; top: 8px; left: 9px;}
}
.ss_viber{
	&:before{.icon-viber; font-size: 16px; line-height: 16px; left: 9px; top: 8px;}
}
.ss_copy{
	&:before{.icon-copy; left: 10px; top: 9px;}
}
/*------- /share -------*/

/*------- newsletter -------*/
.nw{
	background: @lblue; color: #fff;
	.wrapper{height: 195px;}
}
.nw-title{font-size: 54px; line-height: 66px; font-weight: 300; position: absolute; left: 0; top: 43px;}
.nw-subtitle{font-size: 20px; line-height: 28px; width: 486px; text-align: center; left: 486px; position: absolute; top: 50px;}
.nw-cnt{width: 460px; position: absolute; top: 50px; right: 0;}
.nw-form{
	font-size: 0; position: relative;
	&:before{position: absolute; .icon-mail-send; font: 15px/15px @fonti; color: #A6ABBD; left: 24px; top: 20px;}
}
.nw-input{border-radius: 3px; width: 100%; height: 54px; .placeholder(#424656,#ccc); padding: 0 120px 0 65px; font-size: 16px; color: #424656;}
.nw-button{height: 44px; width: 100px; border-radius: 3px; background: @blue; position: absolute; right: 5px; top: 5px; text-align: center; padding: 0; line-height: 46px;}
.nw-form-main{position: relative;}
.nw-error{font-size: 12px; line-height: 18px; padding-left: 65px; font-family: Arial, sans-serif; position: absolute; top: 100%; left: 0;}
.nw-success{font-size: 16px; line-height: 24px; text-align: center; padding-top: 6px;}

.nw-checkbox{
	position: relative; display: block; margin-top: 18px;
	input[type=checkbox]+label{
		font-size: 12px; line-height: 16px;
		&:before{width: 20px; height: 20px; color: transparent; font-size: 12px; line-height: 18px; top: 0;}
		span{max-height: 42px; overflow-y: auto; display: block;}
	}
	input[type=checkbox]:checked+label:before{background: #fff; color: @blue;}
	.nw-error{padding-top: 0; color: #fff; padding-left: 38px; top: calc(~"100% - 9px");}
}
/*------- /newsletter -------*/

/*------- footer -------*/
.footer-title{font-size: 24px; line-height: 29px; font-weight: bold; color: @blue; padding: 0 0 12px;}
.footer{padding: 136px 0 50px; font-size: 0;}
.footer-col{display: inline-block; vertical-align: top; width: 288px;}
.footer-col2{width: 320px;}
.footer-col3{width: 456px;}
.footer-col4{
	width: 392px; padding-top: 10px;
	p{padding: 0;}
	a, img{display: block; width: auto; height: auto; max-width: 220px; max-height: 84px;}
	.convatec{
		padding: 38px 0 0 10px;
		span{font-size: 12px; line-height: 24px; color: #9B9B9B; font-weight: 500; padding: 0 0 0 48px; display: block;}
	}
}
.footer-col5{
	width: 205px; text-align: center;
	.payway{margin-bottom: 25px;}
	a{display: block;}
	img{display: inline-block; vertical-align: top;}
	p{padding: 0;}
}
.nav-footer{
	font-size: 16px; line-height: 30px; list-style: none;
	li.selected a{color: @lblue;}
	a{color: #4A4A4A; display: block;}
}
.footer-contact{
	li{font-size: 16px; line-height: 30px;}
	.item-tel{
		&:before{top: 7px;}
	}
	.item-mail{
		&:before{top: 10px;}
	}
}
.social{
	font-size: 0; margin-top: 38px;
	a{
		width: 44px; height: 44px; display: inline-block; margin: 0 10px 0 0; border-radius: 100%; background: @lblue; position: relative; .transition(all);
		&:before{position: absolute; .icon-facebook; font: 18px/18px @fonti; color: #fff; left: 18px; top: 13px;}
	}
	.yt{
		&:before{.icon-youtube; font: 13px/13px @fonti; left: 14px; top: 16px;}
	}
	.ins{
		&:before{.icon-instagram; font: 20px/20px @fonti; left: 13px; top: 12px;}
	}	
	.gp{
		display: none!important;
		&:before{.icon-google; font: 16px/16px @fonti; left: 16px; top: 14px;}
	}	
	.tw{
		&:before{.icon-twitter2; font: 17px/17px @fonti; left: 14px; top: 14px;}
	}
	.in{
		display: none!important;
		&:before{.icon-linkedin; font: 18px/18px @fonti; left: 14px; top: 12px;}
	}				
}
.footer-bottom{padding-top: 128px; position: relative;}
.footer-login{
	display: inline-block; vertical-align: top; width: 607px;
	.btn-border{margin-right: 20px;}
}
.copy{display: inline-block; vertical-align: top; font-size: 14px; line-height: 24px; color: #A6ABBD; padding-top: 14px;}
.dev{
	position: absolute; right: 0; bottom: 12px; display: inline-block; font-size: 14px; line-height: 24px; color: #A6ABBD; 
	a{
		color: #A6ABBD;
		&:hover{color: #c5242a;}
	}
	a:first-child{
		padding-left: 36px; position: relative;
		&:before{position: absolute; .icon-marker; font: 26px/26px @fonti; color: @lblue; left: 0px; top: -4px;}
	}
}
.btn-footer{
	.transition(all);
	&:hover{background: @lblue; color: #fff; border-color: @lblue;}
}

.footer-exchange-rate{font-size: 16px; line-height: 20px; margin-top: 25px;}
/*------- /footer -------*/

/*------- homepage -------*/
.homepage-main{padding: 0; width: 100%; background: @white;}
.homepage-top-intro{position: relative; display: block;}
.homepage-slider{
	position: relative; display: block; width: 100%; height: auto;
	.owl-dots{position: absolute; width: auto; height: auto; bottom: 40px; left: 120px; font-size: 0; z-index: 2;}
	.owl-dot{
		width: 24px; height: 24px; background: transparent; border: 1px solid @white; border-radius: 50%; margin: 0 5px; display: inline-block; vertical-align: top; cursor: pointer;
		&:first-child{margin-left: 0;}
		&:last-child{margin-right: 0;}
		&.active{background: @white;}
	}
}
.hs-item{
	position: relative; display: block; 
	img{display: block; width: auto; height: auto; max-width: 100%;}
	&:before{.pseudo(auto,auto); left: 0; right: 0; bottom: 0; top: 0; background: rgba(44,73,126,.15);}
}
.homepage-intro{position: absolute; width: auto; left: 0; right: 0; top: 0; bottom: 0; padding: 35px 120px 109px; z-index: 2;}
.homepage-intro-left{color: @white; position: relative; float: left; width: 445px; padding-top: 225px;}
.hil-contact{
	display: block; width: 100%; text-align: left; font-size: 16px; line-height: 19px; font-weight: bold;
	a[href^=tel]{font-size: 94px; line-height: 100px; display: block; font-weight: bold; color: @white; padding-top: 5px;}
}
.hil-label{display: block; text-align: right; font-size: 54px; line-height: 60px; font-weight: 300;}
.homepage-intro-right{display: block; position: relative; float: right; width: 750px; height: auto;}
.hir-close-btn{display: none;}
.hir-note{
	display: block; position: relative; width: 100%; background: @white; border-radius: 3px; box-shadow: 0 10px 30px 0 rgba(0,0,0,0.25); padding: 20px 20px 20px 74px; margin-bottom: 30px;
	&:before{.pseudo(40px,40px); .icon-list; font: 40px/40px @fonti; color: @lblue; top: 22px; left: 20px; text-align: center;}
	&:after{.pseudo(12px,12px); background: @white; transform: rotate(45deg); bottom: -5px; left: 34px;}
}
.hir-note-title{font-size: 24px; line-height: 30px; font-weight: bold; color: @textColor; display: block; max-width: 57%;}
.hir-note-subtitle{display: block; font-size: 14px; line-height: 17px; font-weight: 500; color: @grayLight; max-width: 57%;}
.hir-btn{position: absolute; top: 20px; right: 20px; margin: 0;}
.hir-form-container{position: relative; display: block;}
.homepage-form{
	display: block; font-size: 0;
	.field{
		padding-bottom: 0; display: inline-block; vertical-align: top; width: calc(~"33.3333% - 20px"); margin: 0 30px 25px 0; line-height: normal; position: relative;
		&:nth-child(3n){margin-right: 0;}
		&:nth-child(7){width: calc(~"33.333% - 20px"); margin-right: 30px;}
		&:nth-child(8){width: calc(~"68% - 20px"); margin-right: 0;}
		label{display: table-cell; font-size: 14px; line-height: 17px; font-weight: 500; color: @white; padding-bottom: 7px; position: relative; padding-right: 30px;}
		input{
			background: @white; width: 100%; height: 50px; line-height: normal; padding: 0 20px; box-shadow: 0 10px 30px 0 rgba(0,0,0,0.25); border-radius: 3px; font-size: 14px; color: @textColor;
			&::placeholder{opacity: .5;}
		}
	}
	.error{
		font-size: 12px; line-height: 14px; color: @white; position: absolute; bottom: -18px; font-weight: 100;
		strong{font-weight: 500;}
	}
	.submit{width: calc(~"68% - 20px"); margin-right: 0; margin-bottom: 0; display: inline-block;}
}
.prescription-success{font-size: 40px; line-height: 46px; color: @white; background: none; padding-left: 0; font-weight: 300;}
.field-special{
	position: relative;
	label{
		&:after{.pseudo(14px,14px); .icon-info; font: 10px/14px @fonti; color: @black; background: @white; border-radius: 50%; text-align: center; top: 0; right: 0;}
	}
	&.active .field-special-tooltip{opacity: 1; visibility: visible;}
}
.has-tooltip{
	label{
		&:before{.pseudo(10px,10px); background: @white; transform: rotate(45deg); right: 2px; bottom: -5px; opacity: 0; .transition(all);}
	}
	&.active label:before{opacity: 1;}
}
.field-special-tooltip{
	position: absolute; top: 19px; padding: 5px 7px; background: @white; font-size: 14px; font-weight: 300; line-height: 17px; width: 140%; text-align: center; height: auto; border-radius: 3px; z-index: 1; margin-top: 5px; display: block; opacity: 0; visibility: hidden; box-shadow: 0 10px 30px 0 rgba(0,0,0,0.4); .transition(all);
	p{padding-bottom: 0; z-index: 1; position: relative;}
	img{display: block; width: auto; height: auto; max-width: 100%;}
}
.field-special-tooltip2{left: auto; right: 0;}
.field-health_insurance label{padding-bottom: 18px!important;}
.hei-container{display: inline-block; vertical-align: top; width: 50%;}
.field-insurance_provider{
	padding-bottom: 7px; margin-top: 20px;
	label{padding-right: 0!important;}
}
.m-req{display: none!important;}
.homepage-req{font-size: 11px; line-height: 13px; font-weight: 500; color: @white; display: block; margin-bottom: 0!important; padding: 23px 0; width: calc(~"33.333% - 20px"); margin-right: 30px; display: inline-block;}
.homepage-submit-btn{
	display: block; width: 100%; height: 60px; line-height: normal; font-size: 20px; box-shadow: 0 10px 30px 0 rgba(0,0,0,0.3);
	.special{display: none;}
}
.homepage-post-intro{position: relative; display: block; width: 100%; height: auto; background: @lblue; padding: 27px 0;}
.hpi-left, .hpi-right{
	display: inline-block; vertical-align: top; width: auto; margin-right: 125px; font-size: 16px; line-height: 18px; color: @white; position: relative; font-weight: 300; padding: 5px 0;
	p{padding-bottom: 0;}
	strong{display: block; font-size: 24px; line-height: 29px; font-weight: bold; padding-bottom: 5px;}
}
.hpi-left{
	padding-left: 80px;
	&:before{.pseudo(45px,62px); .icon-years; font: 62px/62px @fonti; color: @white; top: 0; left: 0;}
}
.hpi-right{
	padding-left: 75px;
	&:before{.pseudo(45px,62px); .icon-list; font: 62px/62px @fonti; color: @white; top: 0; left: 0;}
}
.hpi-info{position: absolute; width: 960px; height: auto; background: @white; border-radius: 3px 0 0 3px; padding: 57px 90px; top: -67px; right: 0; z-index: 1; background-image: url(images/pharmacy.png); background-size: 413px 368px; background-repeat: no-repeat; background-position: 100% -8px; box-shadow: 0 10px 20px 0 rgba(66,70,86,0.2);}
.hpi-info-title{
	font-size: 36px; line-height: 44px; font-weight: bold; margin-bottom: 30px;
	span{display: block; font-size: 18px; line-height: 23px; color: @grayLight;}
}
.hpi-info-content{position: relative; display: block; width: auto;}
.hpi-cnt{
	display: block; position: relative; padding-left: 30px; font-size: 22px; line-height: 28px; color: @textColor; font-weight: 300; padding-bottom: 15px;
	a{
		color: @textColor;
		&:not([href^=tel]):hover{text-decoration: underline; color: @blue;}
	}
}
.hpi-business-time{padding-bottom: 0;}
.hpi-address:before{.pseudo(20px,20px); .icon-pin; font: 20px/20px @fonti; color: @lblue; top: 4px; left: 0;}
.hpi-phone:before{.pseudo(20px,20px); .icon-tel; font: 20px/20px @fonti; color: @lblue; top: 4px; left: 0;}
.hpi-business-time:before{.pseudo(20px,20px); .icon-clock; font: 20px/20px @fonti; color: @lblue; top: 4px; left: 0;}
.hir-note-buttons{display: none;}
/*------- /homepage -------*/

/*------- homepage publish widget -------*/
.homepage-pw{position: relative; display: block; width: 100%; height: auto; margin-bottom: 0; margin-top: 90px;}
.hpw-title{font-size: 64px; line-height: 70px; font-weight: 300; display: block; padding-bottom: 10px;}
.hpw-subtitle{font-size: 18px; line-height: 24px; font-weight: bold; color: @grayLight;}
.hpw-container{position: relative; display: block; margin-top: 50px;}
.hpw-left{
	position: relative; float: left; width: 840px;
	.pp{
		display: block; width: 100%; height: auto; margin-bottom: 0;
		&:hover .pp-cnt{box-shadow: 0 10px 20px 0 rgba(66,70,86,0.4);}
	}
	.pp-image{
		display: block; position: relative; width: 100%; height: auto; border-radius: 3px;
		img{display: block; width: auto; height: auto; max-width: 100%; border-radius: 3px;}
	}
	.pp-cnt{width: 720px; padding: 47px 60px 56px; margin-top: -110px; box-shadow: 0 10px 20px 0 rgba(66,70,86,0.2);}
	.pp-headline{font-size: 18px; line-height: 24px;}
	.pp-title{font-size: 36px; line-height: 42px; overflow: hidden; word-wrap: normal; white-space: nowrap; text-overflow: ellipsis;}
	.pp-desc{
		font-size: 18px; line-height: 26px; display: -webkit-box; text-overflow: ellipsis; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden;
		p{padding-bottom: 0;}
	}
}
.hpw-right{
	position: relative; float: right; width: 750px;
	.pp{display: block; position: relative; width: 100%; margin-bottom: 29px; .transition(all);}
	.pp-image{
		display: inline-block; vertical-align: middle; width: 175px; height: auto; border-radius: 3px; margin-right: 30px; .transition(all);
		img{display: block; width: auto; height: auto; max-width: 100%; border-radius: 3px; .transition(all);}
	}
	.pp-cnt{display: inline-block; vertical-align: middle; width: 545px; padding: 0; margin: 0; box-shadow: none;}
	.pp-headline{font-size: 16px; line-height: 19px;}
	.pp-title{font-size: 28px; line-height: 34px; width: 100%; overflow: hidden; word-wrap: normal; white-space: nowrap; text-overflow: ellipsis;}
	.pp-desc{
		font-size: 16px; line-height: 22px; padding-top: 4px; display: -webkit-box; text-overflow: ellipsis; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden;
		p{padding-bottom: 0;}
	}
}
.hpw-btn{position: relative; float: right; margin-top: 40px; font-weight: 400;}
.mobile-hpw-container{display: none;}
/*------- /homepage publish widget -------*/

/*------- homepage catalog widget -------*/
.homepage-cw{position: relative; display: block; margin: 160px 0 140px;}
.hcw-container{display: flex;}
.hcw-col{
	position: relative; display: inline-block; vertical-align: top; width: calc(~"20% - 1px"); margin-right: 1px; background: @lblue; .transition(all);
	&.active{
		box-shadow: 0 8px 40px 0 rgba(82,107,163,0.5);
		.hcw-back{height: 100%; visibility: visible; opacity: 1;}
	}
	.btn{display: none;}
}
.hcw-image{
	display: block; width: 100%; height: auto; min-height: 277px;
	img{display: block; width: auto; height: auto; max-width: 100%;}
}
.hcw-cnt{display: block; padding: 35px;}
.hcw-cnt-title{
	font-size: 24px; line-height: 30px; color: @white; font-weight: bold; display: block;
	span{
		display: inline-block; position: relative; padding-bottom: 5px;
		&:after{.pseudo(100%,4px); background: rgba(255,255,255,.2); bottom: 0; left: 0;}
	}
}
.hcw-cnt-desc{font-size: 16px; line-height: 22px; color: @white; display: block; margin-top: 20px;}
.hcw-back{position: absolute; height: 0; top: 0; right: 0; bottom: auto; left: 0; background: @blue; padding: 35px; opacity: 0; visibility: hidden; .transition(all);}
.hcw-back-title{
	display: block; font-size: 24px; line-height: 30px; color: @white; font-weight: bold;
	a{
		color: #fff;
		&:hover{color: @lblue;}
	}
	span{
		display: inline-block; position: relative; padding-bottom: 5px;
		&:after{.pseudo(100%,4px); background: rgba(255,255,255,.2); bottom: 0; left: 0;}
	}
}
.hcw-back-list{
	list-style: none; margin: 20px 0 0; padding: 0;
	li{display: block; padding-bottom: 15px;}
	a{display: block; font-size: 20px; line-height: 24px; color: @white; font-weight: 300; .transition(all);}
}
/*------- /homepage catalog widget -------*/

/*------- homepage about -------*/
.homepage-about{
	background: @blue; position: relative; display: block; color: @white; padding: 120px 0 110px; background-image: url(images/watermark.svg); background-repeat: no-repeat; background-size: auto 100%; background-position: 0 100%; margin-bottom: 1px;
	.wrapper{display: flex; align-items: center;}
}
.ha-content{
	font-weight: 400; font-size: 18px; line-height: 24px;
	strong{font-weight: 600;}
	h2{font-weight: 100; padding-top: 0; padding-bottom: 23px;}
	.btn{margin-top: 21px;}
}
.ha-left{position: relative; float: left; width: 770px;}
.ha-right{position: relative; float: right; width: 570px; margin-right: 240px; margin-left: auto;}
.ha-right-list{
	list-style: none; margin: 0; padding: 0;
	li{
		display: block; padding-bottom: 40px;
		&:last-child{padding-bottom: 0;}
	}
	a[href^=tel]{color: @white;}
	strong{display: block; font-size: 24px; line-height: 29px; padding-bottom: 6px; font-weight: 600;}
}
.ha-link {
	display: block; position: relative; padding-left: 80px; color: @white; font-size: 16px; line-height: 18px; font-weight: 400;
	&:before{.pseudo(50px,50px); font: 50px/50px @fonti; color: @lblue; top: 0; left: 0; text-align: center;}
}
.ha-year:before{.icon-years; font-size: 55px;}
.ha-list:before{.icon-list;}
.ha-shop-support:before{.icon-telechat;}
.ha-patients-support:before{.icon-pharmacy;}
.ha-delivery:before{.icon-truck; font-size: 32px; line-height: 58px;}
.ha-payment:before{.icon-payment;}
.ha-images{width: auto; position: absolute; top: 0; right: -2px; border-left: 1px solid @white; height: auto; height: 100%;}
.ha-image{
	display: block; width: auto; height: calc(~"33.3333% - -0.5px"); border-bottom: 1px solid @white;
	img{display: block; width: auto; height: auto; height: 100%;}
}
/*------- /homepage about -------*/


/*------- publish index -------*/
.page-publish-index{
	.main{padding-top: 40px;}
	.bc{text-align: center;}
	h1{text-align: center; padding-top: 10px;}
}
.p-categories{
	font-size: 0; font-weight: bold; list-style: none; margin: 0; padding: 30px 0 0; text-align: center;
	li{display: inline-block; vertical-align: top;}
	li.selected{
		a{color: @lblue;}
	}
	a{font-size: 20px; line-height: 25px; padding: 0 28px; color: @blue; text-decoration: underline;}
}
.p-items{
	padding-top: 70px; display: flex; flex-wrap: wrap;
	.pp:nth-child(4n-2){margin-right: 0;}
}
.load-more-container{text-align: center;}
.m-p-categories{display: none;}
.m-p-categories-close{display: none;}
.p-desc{
	max-width: 1000px; padding: 20px 0 0 0; margin: auto;
	a{text-decoration: underline; color: @blue2!important;}
	a[href^=tel]{text-decoration: none;}
	@media (min-width: 1300px){
		a:hover{text-decoration: none;}
	}
}
/*------- /publish index -------*/

/*------- publish post -------*/
.pp{width: 390px; display: inline-block; vertical-align: top; position: relative; margin: 0 40px 85px 0;}
.pp-image{
	img{display: block; border-radius: 3px; width: auto; height: auto; max-width: 100%;}
}
.pp-cnt{background: #fff; font-size: 14px; line-height: 17px; padding: 27px 30px; border-radius: 3px; position: relative; z-index: 1; width: 350px; margin: -40px auto 0; box-shadow: 0 10px 20px 0 rgba(66,70,86,0.2); .transition(all);}
.pp-headline{color: #A6ABBD; font-family: Arial, sans-serif; padding-bottom: 2px; font-weight: bold;}
.pp-title{font-size: 20px; line-height: 24px; color: @blue; padding: 0; font-weight: normal; .transition(all);}
.pp-big{
	width: 820px; margin: 0 40px 50px 0;
	.pp-cnt{padding: 46px 60px; font-size: 18px; line-height: 26px; width: 700px; margin-top: -100px; box-shadow: 0 10px 20px 0 rgba(66,70,86,0.2);}
	.pp-title{font-size: 36px; line-height: 43px;}
}
.pp-desc{color: #424656; padding-top: 11px;}
.p-items{
	.pp{display: flex;flex-flow: column;}
	.pp-desc{display: none;}
	.pp-cnt{flex-grow: 1; display: flex; flex-flow: column; justify-content: center;}
	.pp-big {
		.pp-cnt{flex-grow: 1;}
		.pp-headline{line-height: 24px;}
		.pp-desc{
			display: block; display: -webkit-box; text-overflow: ellipsis; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden;
			p{padding-bottom: 0;}
		}
	}
}
/*------- /publish post -------*/

/*------- publish detail -------*/
.page-publish-detail{
	.bc{text-align: center;}
	.pd-headline{max-width: 1430px; margin: auto;}
	h1{text-align: center; padding-bottom: 36px; max-width: 880px; margin: auto;}
	.main{padding-top: 40px; padding-bottom: 0;}
	.cms-content{padding-top: 0;}
}
.pd-headline{text-align: center; font-size: 18px; line-height: 23px; color: #A6ABBD; font-weight: bold; padding: 24px 0 0;}
.pd-main-image{width: 1430px; margin: auto;}
.pd-content-cnt{
	width: 1190px; box-shadow: 0 10px 20px 0 rgba(66,70,86,0.2); border-radius: 3px; background: #fff; margin-top: -130px; padding: 67px 100px 70px; position: relative; z-index: 1;
	&.no-image{margin-top: 0;}
}
.pd-date{font-size: 16px; line-height: 18px; color: #A6ABBD; padding-bottom: 11px;}
.pd-related-products-title{font-size: 44px; line-height: 53px; text-align: center; padding: 110px 0 50px;}
.pd-thumb{
	display: block; margin: 20px 0 20px;
	img{display: block; width: 100%;}
}
.pd-other-items{
	.owl-stage{display: flex;}
	.owl-item{display: flex;}
	.pp{display: flex;flex-flow: column;}
	.pp-cnt{flex-grow: 1;}
	.pp:last-child{margin-right: 0;}
	.pp-desc{padding-top: 5px; display: -webkit-box; text-overflow: ellipsis; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden;}
}
.pd-other-title{font-size: 44px; line-height: 53px; padding: 40px 0; text-align: center;} 
/*------- /publish detail -------*/

/*------- catalog index -------*/
.catalog-index{
	.bc{padding-bottom: 17px;}
	h1{padding-bottom: 35px;}
	.btn-load-more{width: 100%;}
}
.c-categories{
	list-style: none; margin: 0; padding: 0 0 30px; margin-top: -15px;
	@media (max-width: @m){margin-top: 0; padding:  0 0 20px;}
	&>li{
		border-bottom: 1px solid @borderColor2;
		&.selected{
			&>a span:before{opacity: 0.2;}
		}
		&.active{
			&>a{
				border: 0; padding-bottom: 10px;
				@media (max-width: @m){padding-bottom: 0;}
			}
		}
		&.has-children{
			&>a{
				position: relative;
				&:after{
					.pseudo(20px,20px); position: relative; .icon-plus; font: 20px/1 @fonti; color: @lblue; margin-right: 23px; margin-left: auto;
					@media (max-width: @t){width: 15px; height: 15px; font-size: 15px;}
					@media (max-width: @m){margin-right: 0;}
				}	
			}
			&.active{
				&>a:after{.icon-minus;}
			}
		}
		&>a{
			font-size: 22px; line-height: 30px; padding: 12px 0 12px 0; font-weight: bold; color: @blue!important; display: flex; align-items: center;
			@media (max-width: @t){font-size: 16px;}
			@media (max-width: @m){padding: 10px 15px 10px 0;}
			span{
				position: relative;
				&:before{
					.pseudo(auto,4px); background: #424656; left: 0; bottom: -5px; right: 0; opacity: 0; .transition(all);
					@media (max-width: @m){display: none;}
				}
			}
		}
	}
	li.has-children.active ul{display: block;}

	li.selected>a{color: @lblue;}
	a{
		display: block; color: #424656; padding: 7px 15px 7px 0;
		@media (max-width: @t){padding: 5px 10px 5px 0;}
		@media (max-width: @m){padding: 6px 15px;}
	}
	ul{
		font-size: 18px; line-height: 24px; list-style: none; margin: 0; padding: 0 0 6px; display: none;
		@media (max-width: @t){font-size: 16px;}
		ul{font-size: 16px; line-height: 21px;}
		a{
			padding-left: 20px;
			@media (max-width: @t){padding-left: 10px;}
			@media (max-width: @m){padding-left: 15px;}
		}
	}
	ul ul a{
		font-size: 14px; line-height: 19px; padding-left: 40px;
		@media (max-width: @t){padding-left: 20px;}
		@media (max-width: @m){padding-left: 30px;}
	}
	ul ul ul a{
		padding-left: 60px;
		@media (max-width: @t){padding-left: 30px;}
		@media (max-width: @m){padding-left: 50px;}
	}
}
.page-search .c-categories{margin-top: 0;}
.c-desc{
	font-size: 26px; line-height: 38px; font-weight: 300; display: block; border-bottom: 1px solid @borderColor2; padding-bottom: 27px;
	p{padding-bottom: 0;}
	ul{.list;
		li:before{top: 21px;}
	}
	img{max-width: 100%; height: auto;}
}
.c-extra-desc{
	position: relative; display: block; margin-top: 20px;
	&.special{
		.ced-title{font-size: 28px; line-height: 34px; font-weight: bold; color: @blue;}
	}
}
.ced-container{
	display: block; position: relative; width: 100%; border-bottom: 1px solid @borderColor2; cursor: pointer; margin-bottom: 15px;
	&.active{
		.ced-title:after{.pseudo(20px,20px); .icon-minus; font: 20px/20px @fonti; color: @lblue; top: 5px; right: 0;}
		.ced-content{display: block;}
	}
}
.ced-title{
	position: relative; display: block; width: 100%; font-size: 34px; line-height: 40px; font-weight: 300; padding-bottom: 16px;
	&:after{.pseudo(20px,20px); .icon-plus; font: 20px/20px @fonti; color: @lblue; top: 5px; right: 0;}
}
.ced-content{
	font-size: 20px; line-height: 28px; font-weight: 300; padding-bottom: 20px; display: none;
	.list{margin: 0 0 15px;}
	img{max-width: 100%;}
}
.c-toolbar{
	margin-top: 85px; padding-bottom: 30px; border-bottom: 1px solid @borderColor2;
	&.no-dropdown{margin-top: 25px;}
}
.c-counter{font-size: 18px; line-height: 24px; font-weight: 300; position: relative; float: left; width: 300px; padding-top: 15px;}
.c-sort{
	position: relative; float: right; width: 300px;
	select{box-shadow: none; font-size: 18px; height: 54px; background-position: 92% 24px; font-weight: 300;}
}
.btn-load-more{background: @blue; width: 215px; display: block; text-align: center; margin: 0 auto; font-weight: 400;}
.c-load-more-container{
	.btn-load-more{margin-top: 25px;}
}
.load-more-loader{
	width: 200px; height: 54px; line-height: 54px; border: 1px solid @blue; background: @white; text-align: center; display: block; padding: 0; margin: 25px auto 0;
	span{display: inline-block; vertical-align: top; background: url(/media/images/loader.svg) left center no-repeat; background-size: 115px; padding: 0 60px;}
}
.mobile-filters{display: none;}
.c-empty{padding: 30px 0 0 0;}
/*------- /catalog index -------*/

/*------- filters -------*/
.cf{position: relative; float: left; width: 370px; padding-right: 0;}
.cf-main-title{display: block; position: relative; font-size: 24px; line-height: 36px; font-weight: bold; color: @blue; padding-bottom: 15px; margin-top: 52px; border-bottom: 1px solid @borderColor2;}
.cf-item{
	padding: 16px 0 0;
	&.active{
		.cf-title{
			border-bottom: 0; padding-bottom: 8px;
			&:after{.icon-minus;}
		}
		.cf-item-wrapper{display: block;}
	}
}
.cf-title{
	display: block; position: relative; font-size: 24px; line-height: 30px; padding-bottom: 12px; border-bottom: 1px solid @borderColor2; cursor: pointer; color: @blue; font-weight: 300;
	&:after{.pseudo(20px,20px); .icon-plus; font: 20px/20px @fonti; color: @lblue; top: 4px; right: 30px;}
}
.cf-item-category{
	&.active{
		.cf-title{border-bottom: 1px solid @borderColor2;}
	}
	.cf-counter{display: none;}
	.cf-row{
		padding-bottom: 8px;
		label{
			padding-left: 0!important;
			&:before, &:after{display: none;}
		}	
		input[type=checkbox]:checked + label{
			color: @black;
			&:before, &:after{display: none;}
		}
	}
}
.cf-row{
	padding-bottom: 17px;
	&:last-child{padding-bottom: 0;}
	input[type=checkbox]+label{font-size: 20px; line-height: 20px; display: block; width: 100%; padding-left: 40px; padding-top: 5px; padding-right: 35px; .transition(all);}
}
.cf-row-unavailable .cf-counter{display: none;}
.cf-item-wrapper{padding: 13px 0 27px; border-bottom: 1px solid @borderColor2; display: none;}
.cf-counter{float: right; color: @grayLight;}
.confirm-filters{padding: 9px 0 6px; display: block; width: 100%; text-align: center; font-size: 13px; text-transform: uppercase;}
.m-cf-top{display: none;}
.filter-category{
	border-bottom: 1px solid @borderColor2;
	&.active-filters{padding-top: 13px;}
	li.has-children{
		padding-bottom: 8px;
		ul{
			padding-left: 15px; padding-top: 15px;
			li{padding-bottom: 10px;}
			a{font-size: 16px;}
		}
	}
}
.filter-category-title{font-size: 22px; line-height: 36px; font-weight: bold; color: @blue; padding-bottom: 20px;}
.filters-category{
	list-style: none; margin: 0; padding: 0 0 0 20px;
	li{
		display: block; padding-bottom: 18px;
		&.selected{
			&>a{color: @lblue;}
		}
		/* &.has-children{
			a:after{display: block;}
		} */
	}
	a{
		display: block; font-size: 20px; line-height: 24px; font-weight: 300; color: @textColor; position: relative;
		//&:after{.pseudo(20px,20px); .icon-minus; font: 20px/20px @fonti; color: @lblue; top: 2px; right: 30px; display: none;}
	}
	span{
		position: relative; display: inline-block;
		&:after{.pseudo(100%,4px); background: @textColor; bottom: -2px; left: 0; opacity: 0; .transition(all);}
	}
	ul{padding-left: 10px;}
}
.m-filter-bottom{display: none;}
.m-close-filters{display: none;}
/*------- /filters -------*/

/*------- active filters -------*/
.m-cf-active{display: none;}
.cf-active{display: block; position: relative; width: 100%; padding-top: 22px; border-bottom: 1px solid @borderColor2; padding-bottom: 43px;}
.cf-active-filters-title{display: block; position: relative; font-size: 22px; line-height: 30px; font-weight: bold; color: @blue; padding-bottom: 20px;}
.cf-active-item{
	display: block; font-size: 20px; line-height: 24px; color: @textColor; padding-bottom: 20px; .transition(all);
	&:last-child{padding-bottom: 0;}
	span{
		position: relative; padding-left: 30px;
		&:before{.pseudo(14px,14px); .icon-close; font: 14px/14px @fonti; color: @red; top: 5px; left: 2px; .transition(all);}
	}
}
/*------- /active filters -------*/

/*------- catalog post -------*/
.cp{
	display: flex; width: 100%; height: auto; padding: 25px 0; background: @white; border-bottom: 1px solid @borderColor2; position: relative;
	&:after{.pseudo(auto,auto); top: -2px; right: -25px; bottom: -2px; left: -25px; background: @white; box-shadow: 0 0 20px 0 rgba(0,0,0,0.27); opacity: 0; visibility: hidden;  .transition(all);}
}
.cp-image{
	width: 200px; height: auto; display: inline-block; vertical-align: top; position: relative; z-index: 1;
	span{display: block;}
	img{width: auto; height: auto; display: block; max-width: 100%;}
}
.cp-badge{
	position: absolute; top: -10px; right: -20px; font-size: 14px; line-height: 17px; font-weight: bold; color: @white; text-transform: uppercase; border-radius: 3px; padding: 17px 12px;
	&.new{background: @lblue;}
}
.cp-badge-discount{background: @red;}
.cp-cnt{display: inline-block; vertical-align: top; width: 79%; padding-left: 60px; position: relative; z-index: 1;}
.cp-title{font-size: 30px; line-height: 36px; font-weight: 300; color: @textColor; display: block; padding: 0 0 10px;}
.cp-code{font-size: 16px; line-height: 18px; display: block; font-weight: 300; padding: 0 0 15px; color: @textColor; opacity: .5;}
.cp-short-description{
	font-size: 20px; line-height: 28px; font-weight: 300; color: @textColor;
	ul{.list; margin-left: 0;}
}
.cp-bottom-container{display: flex; align-items: center;}
.cp-price{position: relative; float: left; padding-top: 5px;}
.cp-price-label, .cp-old-price{display: block; position: relative; font-size: 14px; line-height: 17px; font-weight: 300; color: @textColor;}
.cp-old-price span{text-decoration: line-through;}
.cp-current-price, .cp-discount-price{display: block; font-size: 24px; line-height: 29px; font-weight: 500; color: @textColor;}
.cp-discount-price{color: @red;}
.cp-lowest-price, .cp-unit-price{
	font-size: 14px; line-height: 17px; color: #424656; font-weight: 300; padding-top: 3px;
	br{display: none;}
}
.cp-btn-details{position: relative; float: right; font-size: 16px; line-height: 19px; border: 1px solid @blue; color: @blue; border-radius: 3px; padding: 16px 81px; font-weight: bold; margin-left: auto; .transition(all);}
/*------- /catalog post -------*/

/*------- catalog detail -------*/
.cd-main{
	padding-top: 55px;
	.bc{padding-bottom: 30px;}
}
.cd-body{display: flex;}
.cd-main-content{
	width: 100%; margin: 0; padding: 0; border-left: 0; float: none;
	.wrapper2{.wrapper;}
	.homepage-submit-btn{
		.normal{display: none;}
		.special{display: block;}
	}
}
.cd-col1{position: relative; width: 765px; flex-grow: 0; flex-shrink: 0;}
.cd-col2{position: relative; width: 100%; flex-grow: 1; padding: 0 100px 0 60px;}
.cd-hero-slider{
	height: 0; overflow: hidden;
	&.owl-loaded{height: auto;}
}
.cd-thumbs{
	display: inline-block; vertical-align: top; width: 120px; height: auto; margin-right: 15px;
	.owl-dot{
		display: block; width: 100%; border: 1px solid transparent; margin-bottom: 5px; text-align: center; padding: 4px; cursor: pointer; .transition(all);
		img{width: 100%; height: auto; max-width: 100%; max-height: 100%; display: block; margin: 0 auto;}
		&.active{border-color: @blue;}
		&:last-child{margin-bottom: 0;}
	}
}
.cd-hero-image{
	width: 605px; height: auto; display: inline-block; vertical-align: top; text-align: center;
	span{display: block;}
	img{width: auto; height: auto; max-width: 100%; display: block; margin: 0 auto;}
	&.special{width: 100%;}
}
.cd-col2-top{display: flex; position: relative; border-bottom: 1px solid @borderColor2; padding-bottom: 25px; flex-flow: column;}
.cd-col2-top-left{position: relative; flex-grow: 1; padding-right: 10px;}
.cd-title{font-size: 26px; line-height: 32px; font-weight: bold; display: block; padding-bottom: 11px;}
.cd-code{font-size: 18px; line-height: 22px; font-weight: 300; display: block; color: @grayLight;}
.cd-col2-top-right{position: relative; width: auto; flex-shrink: 0; margin-top: 20px;}
.cd-badge-discount, .cd-variation-discount{display: inline-block; vertical-align: top; font-size: 14px; line-height: 17px; color: @white; font-weight: bold; background: @red; border-radius: 3px; padding: 17px 8px; margin-right: 20px;}
.cd-variation-price{display: inline-block; vertical-align: middle;}
.cd-price{display: inline-block; vertical-align: middle; width: auto;}
.cd-old-price, .cd-price-label{display: block; font-size: 16px; line-height: 18px; font-weight: 300; padding-bottom: 0;}
.cd-old-price span{text-decoration: line-through;}
.cd-current-price{
	font-size: 36px; line-height: 42px; font-weight: 500; color: @textColor; letter-spacing: -0.75px;
	&.red{color: @red;}
}
.cd-tax, .cd-unit-price{display: block; font-size: 14px; line-height: 17px; font-weight: 300; padding-top: 4px;}
.cd-unit-price{margin-top: 2px;}
.cd-lowest-price{font-size: 11px; line-height: 15px; font-weight: 300; color: #424656;}
.cd-col2-short-description{display: block; position: relative; width: 100%; padding-top: 20px;}
.cd-short-desc{
	display: block; position: relative;
	ul{
		.list; margin-left: 0; margin-bottom: 10px;
		li{font-weight: 300;}
	}

}
.related-other-colors{display: block; margin-top: 35px; width: 100%;}
.roc-title{font-size: 20px; line-height: 26px; font-weight: bold; display: block; padding-bottom: 15px;}
.roc-container{display: block; width: 100%;}
.roc-item{
	display: inline-block; vertical-align: top; width: 95px; height: 135px; padding: 2px; border: 3px solid transparent; margin: 0 4px 4px 0; .transition(all);
	img{display: block; width: auto; height: auto; max-width: 100%; max-height: 100%;}
	span{display: block;}
}
.related-other-size{display: block; margin-top: 35px; width: 100%;}
.empty-related-other-size{display: none;}
.ros-container{position: relative; display: flex; flex-wrap: wrap;}
.ros-item{
	position: relative; display: flex; align-items: center; justify-content: center; margin: 0 10px 10px 0; border: 1px solid @borderColor2; min-width: 50px; min-height: 50px; border-radius: @borderRadius; padding: 10px; font-size: 18px; line-height: 1.2; color: @textColor; font-weight: 800; .transition(border-color);
	@media (min-width: 1200px){
		&:hover{border-color: @lblue;}
	}
}
.cd-qty{
	padding-top: 0!important; display: flex!important; align-items: center!important; flex-wrap: wrap; width: 138px!important; margin-right: 17px;
	.wp-btn-qty{width: 42px;}
	.wp-unit{width: 100%; text-align: center;}
}
.cd-buy-container{margin-top: 35px; display: block; width: 100%;}
.cd-variations-title{font-size: 16px; line-height: 18px; display: inline-block; width: auto; margin-right: 45px;}
.size-info-link{
	display: inline-block; vertical-align: top; padding-top: 2px; font-size: 14px; line-height: 17px; font-weight: 500; color: @blue; .transition(all); 
	span{
		position: relative; display: inline-block; 
		&:after{.pseudo(100%,1px); background: @blue; bottom: 2px; left: 0; .transition(all);}	
	}
}
.cd-variations{
	width: 375px; display: block;
	select{box-shadow: none; font-weight: bold; font-size: 18px; padding: 0 25px; height: 54px; background-position: 92% 25px;}
}
.cd-btn-add{
	width: 375px; display: inline-block; vertical-align: top; margin-top: 40px; text-align: center; font-size: 16px; line-height: 52px; font-weight: 400; margin-right: 14px; font-weight: bold;
	span{
		display: inline-block; position: relative; padding-left: 40px;
		&:before{.pseudo(27px,27px); .icon-cart; font: 27px/27px @fonti; color: @white; top: 13px; left: 0;}
	}
}
.cd-btn-add2{margin-top: 0;}
.cd-buy-container .prescription-success{color: @textColor; font-size: 30px; line-height: 36px;}
.cd-order-prescription{display: inline-block; vertical-align: top; margin-top: 40px; width: 375px; text-align: center; font-weight: bold; line-height: 52px;}
.cd-order-prescription2{margin-top: 0;}
.cd-tabs{display: block; position: relative; margin-top: 40px; border-top: 1px solid @borderColor2;}
.cd-tab{
	border-bottom: 1px solid @borderColor2;
	&.active{
		.cd-tab-title:after{.icon-minus;}
		.cd-tab-desc{display: block;}
	}
	table{
		.table;
		tbody{border: 1px solid @textColor!important;}
		tr{
			border-bottom: 1px solid @textColor;
			&:nth-child(odd){
				td{background: #e1f3fb;}
			}
			&:first-child td{border-bottom: 0; border-right: 1px solid @textColor; font-weight: normal; color: @white; background: @blue3; padding: 10px 12px; text-align: center; font-size: 16px;}
		}
		td{padding: 6px 12px; text-align: center; border-bottom: 0; border-right-color: @textColor;}
	}
}
.cd-tab-title{
	display: block; font-size: 22px; line-height: 28px; font-weight: bold; color: @blue; position: relative; padding: 23px 0; cursor: pointer;
	&:after{.pseudo(20px,20px); .icon-plus; font: 20px/20px @fonti; color: @lblue; top: 26px; right: 0;}
}
.cd-tab-desc{
	display: none; padding-bottom: 30px; font-weight: 300;
	ul{.list; margin-top: 0; margin-bottom: 0; padding-bottom: 10px;}
	table{
		width: auto!important;
		tbody{border: none;}
	}
}
.cd-related-posts{position: relative; display: block; font-size: 36px; line-height: 42px; font-weight: 300; margin-top: 47px; margin-bottom: 30px;}
.cd-related-posts-title{margin-bottom: 30px;}
.cd-related-post{display: block; margin-bottom: 30px;}
.cd-related-post-image{
	display: inline-block; vertical-align: middle; width: 175px; height: auto; border-radius: 3px; margin-right: 30px;
	img{display: block; width: auto; height: auto; max-width: 100%; border-radius: 3px;}
}
.cd-related-post-cnt{display: inline-block; vertical-align: middle; width: 71%; padding: 0; margin: 0; font-size: 16px; line-height: 24px;}
.cd-related-post-headline{font-size: 16px; line-height: 22px;}
.cd-related-post-title{font-size: 28px; line-height: 34px; width: 100%; overflow: hidden; word-wrap: normal; white-space: nowrap; text-overflow: ellipsis;}
.cd-related-post-desc p{padding-bottom: 0;}
.cd-unavailable{
	position: relative; display: block; width: 100%; margin-top: 38px; margin-bottom: 55px;
	.error{
		font-weight: 300;
		strong{font-weight: 500;}
	}
}
.cd-unavailable-title{display: block; font-size: 24px; line-height: 32px; padding: 0 0 10px; color: @red!important; font-weight: 500;}
.cd-unavailable-subtitle{display: block; font-size: 18px; line-height: 24px; font-weight: 300; margin-bottom: 30px;}
.cd-unavailable-form{
	display: block;
	input{position: relative; float: left; width: 73%; height: 54px; line-height: normal; font-size: 16px; font-weight: 300; padding: 0 20px; border-radius: 3px;}
}
.btn-unavailable{position: relative; float: right; width: 25%; height: 54px; line-height: 54px; font-weight: 500; padding: 0; text-align: center;}
.success-message{font-size: 20px; line-height: 26px; width: 575px;}
.cd-buttons-container{
	position: relative; display: flex;
	a{padding: 0;}
}
.cd-prescription{
	position: absolute; background: @white; z-index: 1; top: 115px; width: auto; left: -30px; right: -30px; box-shadow: 0 0 20px 0 rgba(0,0,0,0.35); padding: 35px;
	&:before{.pseudo(15px,15px); background: @white; top: -7px; right: 220px; transform: rotate(45deg);}
	&.active{display: block!important;}
	.homepage-form{
		.field{
			label{color: @textColor;}
			input, textarea, select{box-shadow: none;}
			&:nth-child(8){width: calc(~"68% - 21px");}
		}
		.submit{width: calc(~"68% - 21px");}
		.error{color: @red;}
	}
	.homepage-submit-btn{box-shadow: none;}
	input[type=radio]+label:before{border-color: @textColor;}
	input[type=radio]:checked + label:before{background: @white;}
	.field-special label:after{border: 1px solid @textColor;}
	.homepage-req{color: @textColor;}
}
.cd-prescription2{top: 70px;}
.cd-prescription-close{
	position: absolute; width: 20px; height: 20px; top: 10px; right: 10px;
	&:after{.pseudo(100%,100%); .icon-close2; font: 15px/20px @fonti; color: @red; text-align: center; top: 0; left: 0;}
}
.size-guide-title{font-size: 26px; line-height: 36px; font-weight: 600; display: block; padding-bottom: 15px;}
.sizeinfo-content table{width: auto!important;}
.fancybox-skin{background: @white!important;}
/*------- /catalog detail -------*/

/*------- catalog detail related -------*/
.cd-related{display: block; position: relative; text-align: center; margin-top: 150px;}
.cd-related2{margin-top: 90px;}
.cd-related-title{display: block; font-size: 44px; line-height: 50px; font-weight: 300;}
.cd-related-content{
	position: relative; display: block; margin-top: 40px;
	.owl-nav{height: 50px; margin-top: 0; top: 275px;}
	.owl-next, .owl-prev{
		width: 50px; height: 50px; line-height: 50px;
		&:before{font-size: 50px; line-height: 50px; color: @grayLight2;}
	}
	.owl-next{right: -60px;}
	.owl-prev{left: -60px;}
}
.cd-related-item{display: block; padding: 20px; position: relative; border: 1px solid transparent; .transition(all);}
.cd-related-item-image{
	display: block; width: 100%; height: auto; min-height: 530px;
	span{display: block;}
	img{display: block; width: auto; height: auto; max-width: 100%;}
	.cp-badge{padding: 19px 7px; top: 0; right: 5px;}
}
.cd-related-item-cnt{padding: 20px 0 0; color: @textColor;}
.cd-related-item-title{font-size: 20px; line-height: 28px; font-weight: 300; display: block; padding-bottom: 11px;}
.cd-related-item-price{display: block;}
.cd-related-item-label, .cd-related-item-old{display: block; font-size: 14px; line-height: 17px; font-weight: 300; padding-bottom: 2px;}
.cd-related-item-old span{text-decoration: line-through;}
.cd-related-item-current{
	display: block; font-size: 24px; line-height: 30px; font-weight: 500;
	&.red{color: @red;}
}
.cp-price-inline{display: flex; justify-content: center;}
.cp-inline-left{text-align: right;}
.cp-inline-right{text-align: left;}
.cd-related-item-old ins{margin: 0 4px 0 10px;}
.cd-related-item-current.red ins{margin: 0 0 0 9px;}
.cd-related-lowest-price{font-size: 14px; line-height: 17px; font-weight: 300; color: #424656; margin-top: 2px;}
.cd-related-btn{display: block; width: 100%; text-align: center; margin-top: 18px; opacity: 0; visibility: hidden; .transition(all);}
/*------- /catalog detail related -------*/

/*------- add to cart modal -------*/
.product-message-modal{
	position: fixed; bottom: 0; top: 0; right: 0; background: rgba(0,0,0,.7); display: none; z-index: -40; left: -99999px;
	&.active{display: block; z-index: 9999; left: 0;}
}
.modal-box{
	display: block; width: 805px; background: #fff; position: fixed; top: 235px; left: 0; right: 0; margin: auto; z-index: 9999; padding: 50px; box-shadow: 0px 10px 30px rgba(0, 0, 0, .3); display: grid; grid-template-columns: 170px auto; grid-template-rows: auto auto;
	.message{
		font-weight: bold; font-size: 24px; line-height: 30px; color: @textColor; padding: 2px 0 16px 50px; position: relative; font-weight: 300;
		&:before{background: @green; border-radius: 200px; width: 32px; height: 32px; text-align: center; position: absolute; .icon-check; font: 16px/34px @fonti; color: #fff; left: 0; top: 0;}
		&.product_message_response_error{
			color: @red;
			&:before{background: @red; .icon-close; font-size: 16px;}
		}
	}
	.title{font-size: 20px; font-weight: 300; line-height: 24px; padding-bottom: 8px;}
	.close-button{
		z-index: 10; position: relative; display: block; width: 20px; height: 20px; position: absolute; top: 25px; right: 25px;
		&:before{.pseudo(20px,20px); .icon-close; font: 20px/20px @fonti; color: @textColor; top: 0; left: 0; .transition(color);}
		&:hover:before{color: @red;}
	}
	.image{float: left; width: 150px; height: 220px; line-height: 220px; text-align: center; grid-column: ~"1 / 2"; grid-row: ~"1 / 3";}
	img{max-height: 220px; width: auto; height: auto; max-width: 150px; display: inline-block; vertical-align: middle;}
	.desc{float: right; width: 100%; position: relative;}
	.content{padding-left: 50px; width: auto; position: relative; float: left; width: 390px;}
	.modal-price{
		position: absolute; right: 0; float: right; width: auto; color: @red; font-size: 28px; line-height: 32px; font-weight: 500; text-align: right;
		.cd-price-label,.cd-tax{display: none!important;}
		.cd-old-price{
			font-size: 14px; line-height: 17px; color: @textColor;
			ins{display: none;}
			span{display: block;}
		}
		.cd-current-price{
			font-size: 28px; line-height: 32px;
			ins{display: none;}
			span{display: block;}
		}
		.cd-lowest-price{font-size: 14px; line-height: 17px;}
	}
	.modal-old-price{display: block; font-size: 14px; line-height: 17px; font-weight: 300; text-decoration: line-through; color: @textColor;}
}
.modal-code, .modal-attributes{font-size: 14px; line-height: 16px; font-weight: 300; padding-bottom: 2px;}
.modal-code{color: @grayLight;}
.modal-continue-shopping{
	float: left; position: relative; font-size: 16px; line-height: 19px; font-weight: 500; color: @blue; padding-left: 15px;
	&:before{.pseudo(10px,10px); .icon-arrow2; font: 10px/10px @fonti; color: @blue; top: 4px; left: 0; transform: rotate(180deg);}
}
.modal-view-cart{display: inline-block; vertical-align: top; width: 225px; height: 54px; line-height: 52px; text-align: center; background: @green; border-radius: 3px; color: @white; margin-left: 0; font-size: 16px; .transition(all); font-weight: bold;}
.modal-finish-shopping{display: none;}
.modal-continue-shopping{float: left; margin-top: 18px; font-size: 16px; .transition(all); margin-left: auto;}
.modal-buttons{padding: 10px 0 0 50px; width: 100%; display: flex;}
/*------- /add to cart modal -------*/  

/*------- fancybox quick -------*/
.quick-cnt{
	h1{font-size: 46px; line-height: 52px; color: @blue; padding-bottom: 15px;}
}
/*------- /fancybox quick -------*/

/*------- faq -------*/
.page-faq{
	h1{padding-bottom: 37px;}
	.wrapper2{width: 950px;}
	.main{padding-bottom: 115px;}
	.main-content{padding-left: 75px;}
	.cms-content{padding-top: 0;}
}
.faq-group-title{
	display: block; font-size: 40px; line-height: 48px; font-weight: bold; padding-bottom: 29px; margin-top: 18px;
	&:first-child{margin-top: 0;}
}
.fp{
	display: block; position: relative; cursor: pointer; padding-bottom: 30px;
	&.active{
		.fp-title span:after{.icon-minus;}
		.fp-cnt{display: block;}
	}
}
.fp-title{
	font-size: 26px; line-height: 32px; color: @blue; font-weight: 300; display: block; padding: 0;
	span{
		position: relative; padding-left: 45px;
		&:after{.pseudo(20px,20px); .icon-plus; font: 20px/20px @fonti; color: @lblue; top: 6px; left: 0;}	
	}
}
.fp-cnt{padding-left: 45px; padding-top: 10px; display: none;}
/*------- /faq -------*/

/*------- contact -------*/
.page-contact{
	.main-content{padding-left: 75px;}
	.wrapper2{width: 100%;}
	h2{font-size: 28px; line-height: 34px; padding-top: 0; font-weight: bold;}
	p{font-weight: 300;}
	.share-cnt{margin-top: 25px;}
}
.contact-left{position: relative; float: left; width: 460px;}
.contact-right{
	position: relative; float: right; width: 495px; margin-right: 80px;
	a{color: @textColor; text-decoration: underline;}
	a[href^=tel]{text-decoration: none;}
	img{width: auto; height: auto; max-width: 100%; margin: 15px 0 0;}
	.facebook, .youtube{margin-top: 10px;}
}
.contact-left, .contact-right{
	a[href^=mailto]{
		color: @textColor; text-decoration: underline; transition: color .3s;
		&:hover{color: @lblue;}
	}
}
.phone, .mail{
	position: relative; display: block; font-size: 20px; line-height: 28px; font-weight: bold; padding-left: 35px;
	&:before{.pseudo(18px,18px); .icon-phone; font: 18px/18px @fonti; color: @lblue; top: 6px; left: 0; text-decoration: none!important;}
}
.mail{
	text-decoration: underline; color: @textColor;
	&:before{.icon-email; font-size: 14px;}
}
.facebook, .youtube{font-size: 0; display: inline-block; vertical-align: top; width: 44px; height: 44px; border-radius: 50%; margin-right: 5px; background: @lblue; position: relative; text-decoration: none!important; .transition(all);}
.facebook{
	&:after{.pseudo(100%,100%); .icon-facebook; color: @white; font: 20px/44px @fonti; top: 0; left: 0; text-align: center;}
}
.youtube{
	background: @lblue;
	&:after{.pseudo(100%,100%); .icon-youtube; color: @white; font: 15px/44px @fonti; top: 0; left: 0; text-align: center;}
}
.map{display: block; position: relative; width: 100%; height: 550px; margin-top: 30px;}
.gm-style-iw{width: 100% !important;}
.infoBox{
	width: 340px; border-radius: 0; position: relative; margin: 0; background: #fff; box-shadow: 0 0 10px 0 rgba(0,0,0,0.5); font-size: 15px; line-height: 18px; padding: 30px 45px 20px 30px;
	&>strong{display: block; padding: 8px 0 8px 13px; font-size: 14px; display: none;}
	&>br{display: none;}
	img{position: absolute !important; top: 10px; right: 10px; margin: 0 !important;}
	.title{font-weight: 600; padding-bottom: 10px; display: block;}
	.address, .contact, .business-hour{display: block; width: 100%; padding: 0 0 10px;}
	.business-hour{
		padding-bottom: 10px; padding-right: 65px; line-height: 20px;
		strong{display: block;}
	}
	.contact{
		p{padding-bottom: 10px;}
		a{color: @textColor;}
		a[href^=mailto]{
			color: @textColor; text-decoration: underline; transition: color .3s;
			&:hover{color: @lblue;}
		}
	}
}
/*------- /contact -------*/

/*------- shopping cart -------*/
.w-title{display: block; text-align: center; font-size: 64px; line-height: 78px; font-weight: 300;}
.w-counter{
	font-size: 35px; padding-left: 10px; display: none;
	&.active{display: inline-block;}
}
.page-shopping-cart{
	.main{padding: 50px 0 90px;}
}
.w-title{padding-bottom: 28px;}
.cart-container{position: relative; display: flex; width: 1192px; margin: 0 auto;}
.cart-left{position: relative; flex-grow: 1; padding-right: 80px;;}
.cart-right{position: relative; width: 335px; flex-grow: 0; flex-shrink: 0; padding-top: 15px;}
.wp{
	display: flex; position: relative; width: 100%; border-bottom: 1px solid @borderColor2; padding: 20px 0;
	&:last-child{border-bottom: 0;}
}
.wp-image{
	width: 100px; flex-grow: 0; flex-shrink: 0; height: auto; display: inline-block; vertical-align: top;
	img{width: auto; height: auto; display: block; max-width: 100%;}
}
.wp-btn-delete-mobile{display: none;}
.wp-cnt{display: inline-block; vertical-align: top; flex-grow: 1; padding: 0 20px;}
.wp-title{
	padding: 0; display: block; font-size: 20px; line-height: 24px; font-weight: 300; color: @textColor; padding-bottom: 5px;
	a{color: @textColor;}
}
.wp-code{display: block; font-size: 14px; line-height: 17px; color: @grayLight; padding-bottom: 5px;}
.wp-variations{display: block; font-size: 14px; line-height: 17px; color: @textColor; padding-bottom: 5px;}
.wp-qty{display: inline-block;vertical-align: middle; width: 142px; padding-top: 32px; flex-shrink: 0; position: relative;}
.wp-btn-qty{display: inline-block; vertical-align: middle; width: 44px; height: 44px; background: @textColor; position: relative; font-size: 0;}
.wp-btn-dec{
	border-radius: 3px 0 0 3px;
	&:after{.pseudo(100%,100%); .icon-minus; font: 20px/44px @fonti; color: @white; text-align: center; border-radius: 3px 0 0 3px;}
}
.wp-btn-inc{
	border-radius: 0 3px 3px 0;
	&:after{.pseudo(100%,100%); .icon-plus; font: 20px/44px @fonti; color: @white; text-align: center; border-radius: 3px 0 0 3px;}
}
.wp-input-qty{display: inline-block; vertical-align: middle; width: 54px; height: 54px; line-height: normal; border-radius: 3px; font-size: 20px; padding: 0 5px; text-align: center;}
.wp-unit{display: block; font-size: 14px; line-height: 16px; text-align: center; color: @grayLight; margin-top: 6px;}
.wp-message{display: block; position: absolute; font-size: 14px; line-height: 16px; background: @white; text-align: center; margin: 0 -15px; left: 0; width: calc(~"100% - -30px"); bottom: -35px; min-height: 20px; overflow: hidden; z-index: 100;}
.wp-total{position: relative; float: right; text-align: right; width: 170px; padding-top: 21px; flex-shrink: 0;}
.wp-price-old{
	display: block; font-size: 14px; line-height: 17px; color: @textColor;
	span{text-decoration: line-through;}
}
.wp-price-current{display: block; font-size: 28px; line-height: 34px; font-weight: 600;} /* padding-top: 12px; */
.wp-price-discount{display: block; font-size: 28px; line-height: 34px; color: @red; font-weight: 600; padding-top: 0;}
.wp-lowest-price-outer{height: 38px; position: relative; display: block;}
.wp-lowest-price{font-size: 14px; line-height: 19px; position: absolute; right: 0; width: 190px;}
.wp-qty-count{display: block; font-size: 14px; line-height: 17px;}
.cart-total-title{font-size: 24px; line-height: 30px; display: block; font-weight: 600; padding-bottom: 17px;}
.cart-totals{
	font-size: 15px; line-height: 24px; padding-bottom: 10px; font-weight: 300;
	&>div{padding-bottom: 5px;}
}
.w-totals-label{text-align: left; display: inline-block; width: 56%;}
.w-totals-value{text-align: right; display: inline-block; width: 42%;}
.cart-finish-shopping{width: 100%; display: block; text-align: center; margin-top: 5px;}
.empty-cart{display: block; width: 700px; margin: 0 auto; text-align: center;}
.wp-btn-delete{
	display: block; font-size: 12px; line-height: 14px; padding-left: 15px; position: relative; color: @red; position: relative; margin-top: 15px;
	&:before{.pseudo(10px,10px); .icon-close; font: 10px/10px @fonti; color: @red; top: 2px; left: 0;}
}
.wp-btn-delete-mobile{position: relative; bottom: 0; margin-bottom: 20px 0 0 25px; display: none;}
.wp-cnt-right{width: auto; flex-grow: 1; display: flex;}
/*------- /shopping cart -------*/

/*------- auth -------*/
.auth-main{
	display: block; width: 1130px; margin: 0 auto; padding-top: 52px; 
	h1{text-align: center; font-size: 48px; line-height: 56px; padding-bottom: 25px;}
}
.auth-menu{
	text-align: center; list-style: none; margin: 0; padding: 0 0 50px;
	li{
		display: inline-block; padding-left: 0;
		&:before{display: none;}
		&.selected a{color: @lblue;}
	}
	a{
		color: @textColor; font-size: 18px; line-height: 24px; font-weight: 300; padding: 0 25px; .transition(all);
		&.active{color: @lblue;}
	}
}
.a-intro{display: flex; position: relative; width: 100%; font-size: 18px; line-height: 26px;}
.a-intro-left{position: relative; float: left; width: 595px; border-right: 1px solid @borderColor2; padding-right: 80px;}
.a-intro-user{
	position: relative; float: right; width: 538px; padding-left: 80px;
	p{padding-bottom: 0;}
}
.a-intro-title{font-size: 24px; line-height: 30px; font-weight: 300; display: block; padding: 20px 0 18px;}
.a-menu{
	margin: 0 0 0 20px;
	a{text-decoration: underline; font-weight: bold;}
}
.btn-auth-edit{font-size: 16px; font-weight: 400; padding: 0; width: 225px; margin-top: 15px; text-align: center;}
.auth-box.orders-container{margin-top: 80px;}
.a-section-title{
	font-size: 36px; line-height: 44px; font-weight: 300; padding: 0 0 30px;
	a{color: @textColor;}
}
.a-edit-subtitle{font-size: 22px; line-height: 26px; margin: 10px 0 15px;}
/*------- /auth -------*/

/*------- auth orders -------*/
.orders-container{position: relative; display: block; width: 100%;}
.order-details{display: none;}
.order-row{
	display: block; border-top: 1px solid @borderColor2; border-bottom: 1px solid @borderColor2; margin-top: -1px;
	&.active{
		.order-details{display: block;}
		.table-order{padding-bottom: 10px;}
	}
	&:last-of-type{border-bottom: 0;}
}
.table-order{font-size: 18px; line-height: 26px; font-weight: 300; padding: 18px 0; display: block; color: @textColor;}
.orders{	
	.table-col{display: inline-block;}
}
.col-order-num{width: 325px; padding-right: 15px;}
.col-order-status{width: 235px; padding-right: 15px;}
.col-order-total{width: 260px; text-align: right; padding-left: 15px;}
.col-order-btns{position: relative; float: right; width: auto;}
.btn-order-details{
	font-size: 16px;
	span{
		display: block; position: relative; padding-right: 40px;
		&:after{.pseudo(20px,20px); font: 20px/20px @fonti; color: @lblue; top: 2px; right: 0;}	
	}
	.btn-active{
		display: none;
		&:after{.icon-minus;}
	}
	.btn-inactive{
		display: block;
		&:after{.icon-plus;}
	}
	&.active{
		.btn-active{display: block;}
		.btn-inactive{display: none;}
	}
}
.wp-details{
	border-bottom: 0;
	.wp-total{padding-top: 35px;}
	.wp-price-old{
		ins{display: none;}
		span{display: block;}
	}
	.wp-price-current{
		ins{display: none;}
		span{display: block;}
	}
}
.w-table-details{padding-right: 300px;}
.wp-details-sum{
	display: block; text-align: right; font-size: 26px; line-height: 26px; font-weight: 500; margin: 25px 0 35px;
	span:first-child{position: relative; border-top: 1px solid @borderColor2; padding-left: 125px; padding-top: 15px;}
}
/*------- /auth orders -------*/

/*------- auth form -------*/
.auth-form{
	width: 400px; margin: 0 auto;
	.submit{
		margin-top: 15px;
		button{display: block; width: 100%; text-align: center; margin-left: 0; font-weight: bold;}
	}
}
/*------- /auth form -------*/

/*------- auth login -------*/
.page-auth-login{
	.bc{display: none;}
}
.pal-main{
	display: block; width: 1032px; margin: 0 auto; padding-top: 52px;
	h1{display: block; text-align: center; padding-bottom: 65px;}
}
.pal-main-content{
	display: flex;
	h2{
		display: block; font-size: 28px; line-height: 34px; font-weight: 300; padding-bottom: 15px; padding-top: 0;
		&.special{padding-top: 25px;}
	}
	.a-have-account{display: block; padding-top: 25px; font-weight: 300;}
	.btn{font-weight: bold;}
	.form-label{
		.submit{
			width: auto; float: left; margin: 0;
			button{margin-left: 0; font-weight: bold;}
		}
		.auth-links{
			float: right; width: 200px; text-align: center; margin-left: 0;
			a{display: block; color: @textColor; font-size: 14px; line-height: 18px; padding-top: 10px;}
			span{display: block; color: @lblue; text-decoration: underline; font-weight: 500;}
		}
	}
}
.pal-left{position: relative; float: left; width: 478px; padding-right: 80px; border-right: 1px solid @borderColor2;}
.pal-right{
	position: relative; float: right; width: 550px; padding-left: 80px; font-size: 18px; line-height: 24px; font-weight: 300;
	.list{
		margin-left: 0;
		li:before{top: 12px;}
	}
}
.ls-left, .ls-right{
	p{padding-bottom: 0;}
}
.ls-left{position: relative; float: left; width: auto;}
.ls-right{
	position: relative; float: left; width: 200px; font-size: 14px; line-height: 18px; text-align: center; padding-top: 10px; 
	a{color: @textColor;}
	span{display: block; color: @lblue; text-decoration: underline; font-weight: 500;}
}
.form-label{
	.field-healthy_worker{
		padding-bottom: 0;
		&>label{
			position: relative; top: auto; right: auto; bottom: auto; left: auto; padding-top: 0; font-size: 14px; padding-left: 33px; font-weight: 500;
			&:before{width: 20px; height: 20px; top: 3px; font-size: 12px; line-height: 20px;}
		}
	}
}
.healthy_fields{margin-top: 10px;}
.pal-right .list{margin-left: 16px;}

/*------- /auth login -------*/

/*------- forgotten password -------*/
.forgotten-main{
	width: 700px; margin: 0 auto; display: block;
	h1{padding-bottom: 28px; text-align: center;}
	.auth-links{
		position: relative; text-align: center; width: 100%; padding-top: 10px; margin-left: 0;
		a{text-decoration: underline; display: block; font-size: 14px; line-height: 18px;}
	}
	.submit{margin-top: 0!important;}
}
/*------- /forgotten password -------*/

/*------- checkout login -------*/
.checkout-login-main{
	width: 1090px; display: block; margin: 0 auto; font-size: 18px; line-height: 26px; font-weight: 300; padding: 51px 0 80px;
	h1{text-align: center; padding-bottom: 60px;}
	h2{font-size: 28px; line-height: 44px; display: block; font-weight: 300; padding-bottom: 12px; padding-top: 0;}
}
.wc-container{
	position: relative; display: block; width: 100%; display: flex;
	.wc-cnt{padding-bottom: 5px;}
	
}
.wc-col1{position: relative; float: left; width: 50%; border-right: 1px solid @borderColor2; padding-right: 80px; padding-left: 28px;}
.wc-col2{position: relative; float: right; width: 50%; padding-left: 80px; padding-right: 28px;}
.wc-container .form-label{
	.field-email label, .field-password label{padding: 0; text-align: left;}
}
.wc-guest-form, .wc-login-form{max-width: 400px;}
.wc-login-form{
	.remember{margin-top: 2px; padding-bottom: 15px;}
}
.btn-wc-guest{margin-left: 0!important; width: 100%; display: block; font-weight: bold;}
.wc-cnt{padding-bottom: 15px;}
.btn-wc-login{margin-left: 0!important; position: relative; float: left; width: auto;}
.wc-auth-links{
	position: relative; float: left; width: 180px; text-align: center; padding-top: 10px;
	a{display: block; font-size: 14px; line-height: 18px; color: @textColor;}
	span{color: @lblue; text-decoration: underline; display: block; font-weight: 500;}
}
/*------- /checkout login -------*/

/*------- checkout -------*/
.header-checkout{
	position: relative; display: block; width: 100%; height: 87px; border-bottom: 1px solid @borderColor2;
	.logo{left: 120px; width: 280px; height: 45px; background-size: cover; top: 20px;} 
}
.hc-steps{
	position: relative; display: block; width: 1000px; margin: 0 auto; font-size: 32px; line-height: 44px; font-weight: 300; padding-top: 20px;
	.step{
		position: relative; opacity: .5;
		a{color: @textColor;}
		&.current-step{opacity: 1;}
	}
	.step2{float: left;}
	.step3{float: right; text-align: right;}
}
.hc-label{font-size: 18px; line-height: 24px; font-weight: 500; position: absolute; top: 32px; width: 200px; left: 50%; margin-left: -100px; text-align: center;}
.checkout-main{
	width: 995px; position: relative; margin: 0 auto; padding-top: 48px;
	.wc-col1{padding-left: 0; width: 50%; padding-right: 95px;}
	.wc-col2{padding-right: 0; width: 50%; padding-left: 95px;}
	select{box-shadow: none!important;}
}
.wc-subtitle{font-size: 28px; line-height: 34px; font-weight: 300; display: block; padding-top: 0; padding-bottom: 20px;}
.step2{
	.field-zipcode{display: inline-block; vertical-align: top; width: 38%; margin-right: 10px!important;}
	.field-city{display: inline-block; vertical-align: top; width: 59%;}
	.field-message{display: none!important;}
	.field-phone .field_error_input{background: none;}
}
.checkout-form{
	input{font-size: 16px;}
	select{height: 54px; background-position: 92% 24px; font-size: 16px;}
	textarea{height: 120px;}
	.wc-col1{
		border-right: 0;
		&:after{.pseudo(1px, auto); background: @borderColor2; right: 0; top: -48px; bottom: -80px;}
	}
	input[type=checkbox]+label{font-size: 18px;line-height: 24px;}
	input[type=radio]+label:before{width: 18px; height: 18px; top: 2px;}
	input[type=radio]+label:after{width: 12px; height: 12px; top: 6px;}
	input[type=radio]+label{padding-left: 30px; line-height: 24px;}
}
.phone-tooltip{display: block; position: relative; font-size: 14px; font-weight: 300; line-height: 24px; opacity: .5; position: absolute; right: 20px; top: 15px;}
.field-shipping{
	position: relative; display: block; margin-top: 40px;
	&>label{font-size: 28px; line-height: 34px; display: block; padding-bottom: 22px; width: 100%; text-align: left; padding-right: 0;}
	span{
		position: relative; display: block; margin-bottom: 20px;
		label{
			position: relative; top: auto; left: auto; display: block; width: 100%; font-size: 18px;
			&:before{border-color: @borderColor2;}
		}
		input{height: 25px; padding: 0!important;}
		span{margin-bottom: 0;}
		div{font-size: 14px; line-height: 18px; font-weight: 100; opacity: .5; padding-left: 40px;}
		select{margin-top: 10px; box-shadow: none;}
	}
}
.field-message{
	padding-bottom: 0!important;
	textarea{width: 100%; box-shadow: none;}
}
.wc-step2-col2{
	.field{
		label{display: block; width: 100%; padding-top: 0;}
		input[type=checkbox]:checked + label:after{opacity: 1;}
	}
	.label-b_same_as_shipping, .label-b_r1{position: relative!important; left: 0; top: 0; padding-top: 2px;}
}
.btn-checkout{margin-left: 0!important; width: 100%; font-weight: bold; margin-top: 12px;}
.checkout-finish{display: block; position: relative; width: 100%; text-align: center; font-size: 15px; line-height: 21px; color: @grayLight; margin-top: 15px; font-weight: bold;}
.checkout-main-payment{width: 995px;}
.wc-step3-col1, .wc-step3-col2{width: 50%!important;}
.field-payment, .section-shipping{
	&>label{display: none!important;}
	label{
		width: 100%; display: block; font-size: 18px!important; padding-top: 0px!important;
		&:before{border-color: @borderColor!important;}
	}
	span{
		position: relative; display: block; margin-bottom: 16px;
		div{display: block; font-size: 14px; line-height: 22px; color: @textColor; padding: 5px 0 0 30px;}
	}
}
.cart-small{
	position: relative; display: block; margin-top: 40px;
	.w-cart-title{font-size: 28px; line-height: 34px; font-weight: 300; position: relative; float: left; padding: 0;}
	.counter{font-size: 20px;}
	.wp{padding: 10px 0;}
	.wp:last-child{border-bottom: 1px solid @borderColor2; padding-bottom: 20px;}
	.wp-title{font-size: 18px; line-height: 22px; padding-bottom: 8px;}
	.wp-cnt{
		width: 76%; padding-bottom: 56px;
		&.no-image{padding-bottom: 60px;}
	}
	.wp-lowest-pb{
		.wp-cnt{padding-bottom: 80px;}
	}
	.wp-lowest-price{position: relative; right: auto; width: auto;}
	.wp-total{position: absolute; float: none; bottom: 20px; right: 0; width: auto;}
	.wp-price-discount,.wp-price-current{font-size: 20px; line-height: 25px;}
	.w-totals-cnt{display: block; position: relative; margin-top: 24px; /* padding-left: 115px; */}
	.w-totals-title{font-size: 28px; line-height: 34px; display: block; font-weight: 300; padding-bottom: 10px;}
	.cart-total .w-totals-label{text-transform: uppercase;}
	.cart-totals{padding-bottom: 0; font-size: 18px;}
	.wp-price-old{
		&>span:last-child{text-decoration: none;}
	}
}
.w-btn-change{font-size: 16px; line-height: 22px; font-weight: 300; color: @lblue; text-decoration: underline; position: relative; float: left; padding-top: 6px;}
.shipping-row{
	display: block; margin-bottom: 16px;
	&>span{display: none!important;}
	div{
		display: block; font-size: 14px; line-height: 18px; color: @textColor; padding: 5px 0 0 30px;
		span{margin-bottom: 0; margin: 2px 0 3px; font-size: 14px; line-height: 18px; display: inline-block;}
	}
	select{box-shadow: none; width: 100%;}
}
.checkout-last{
	display: block; position: relative; margin-top: 25px;
	textarea{box-shadow: none;}
	label{font-size: 16px!important; line-height: 22px; width: auto;}
}
.webshop-accept-terms{
	input + label{
		display: block; width: 100%; font-size: 16px; padding-left: 32px; font-weight: 500; padding-top: 5px;
		&:before{width: 18px; height: 18px; top: 5px; font-size: 12px; line-height: 21px;}
	}
	a{text-decoration: underline;}
	.error{margin-left: 12px; padding-left: 25px; position: relative; background: url(images/icons/danger-red.svg) no-repeat; background-size: 15px; background-position: 0 6px;}
}
.btn-finish{margin-left: 0!important; text-align: center; width: 100%; margin-top: 20px; font-weight: bold;}
.wc-step3-col2{
	/* width: auto!important; */ font-size: 16px; line-height: 22px;
	.error{
		font-size: 14px;
		strong{font-weight: 500;}
	}
	.section{display: block;}
}
.section-bill-address{
	display: block; font-size: 16px; line-height: 20px; margin-bottom: 15px;
	strong{font-weight: 500; font-size: 18px; line-height: 24px;}
}
/*------- /checkout -------*/

/*------- checkout footer -------*/
.checkout-footer{
	border-top: 1px solid @borderColor2; padding: 61px 0 41px;
	.dev{bottom: 0;}
	.copy{padding-top: 0;}
	.footer-contact{
		.item-mail a{text-decoration: underline;}
	}
}
.cf-col{display: inline-block; vertical-align: top; width: 33.3333%}
.cf-col2{
	text-align: center;
	img{float: unset!important;}
	p{padding-bottom: 0; line-height: 15px;}
}
.cf-col3{
	text-align: right;
	p:last-child{padding-right: 45px;}
}

.wc-exchange-rate{font-size: 16px; line-height: 20px; margin-top: 25px;}
/*------- /checkout footer -------*/

/*------- thankyou -------*/
.thankyou-main{
	width: 890px; margin: 0 auto;
	h1{text-align: center;}
}
.checkout-cnt{position: relative; display: block; margin-top: 30px; text-align: center;}
.invoice-container{
	padding: 20px 30px 10px; border: 1px solid @borderColor2; border-radius: 3px; margin-top: 25px;
	.btn{font-weight: 400;}
}
.thank-you-wrapper{margin-top: 35px;}
.thank-you-login{
	width: 400px; display: block; position: relative; margin-bottom: 25px;
	.field-show-password{
		margin-left: 0;
		label{position: relative; left: 0;}
	}
	.btn{margin-left: 0; width: 100%;}
}
.thank-you-content{position: relative; display: block; margin-bottom: 35px; font-size: 18px; line-height: 24px;}
.thank-you-safe{font-size: 16px; line-height: 22px;}
.ty-payment-transfer-cnt{
	margin-top: 50px;
	.mail-payment-transfer{margin: auto;}
	.btn-print{margin-top: 20px;}
}
/*------- /thankyou -------*/

/*------- search -------*/
.page-search{
	h1{
		font-size: 24px; line-height: 30px; font-weight: 300; text-align: center; padding-bottom: 35px;
		span{display: block; font-size: 48px; line-height: 56px;}
	}
	.bc{display: none;}
	.wrapper2{width: 1065px; margin-left: 100px;}
}
.page-header-cnt{display: flex; width: 100%; border-top: 1px solid @borderColor2; border-bottom: 1px solid @borderColor2; padding: 10px 0;}
.s-nav{
	flex-grow: 1; list-style: none; margin: 0; padding: 12px 0 0; text-align: center;
	&.special{padding-bottom: 14px;}
	li{
		display: inline-block; vertical-align: middle; width: auto;
		&:before{display: none;}
		&.selected a{color: @lblue;}
	}
	a{display: block; width: auto; padding: 0 25px; color: @textColor; font-size: 18px; line-height: 24px; font-weight: bold; .transition(all);}
}
.search-main-content{
	width: 100%; float: none; padding: 0; border: 0;
	.wrapper2{width: 100%; margin: auto;}
	.s-nav{padding-top: 0;}
	.page-header-cnt{padding: 20px 0 25px; margin-bottom: 60px;}
	.pp{
		width: calc(~"25% - 35px");
		&:nth-child(3n){margin-right: 40px;}
		&:nth-child(4n){margin-right: 0;}
	}
}
.s-item{
	display: block; width: 990px; margin: 0 auto 58px; font-size: 20px; line-height: 28px; font-weight: 300; display: block;
	.btn{margin-top: 11px;}
}
.s-item-title{
	font-size: 36px; line-height: 44px; padding: 0 0 17px;
	a{color: @textColor;}
}
.s-no-results{text-align: center;}
/*------- /search -------*/

/*------- prescription -------*/
.prescription-form-container{
	display: block; width: 450px; margin-top: 25px;
	.global-error{margin-left: 0;}
	.field-health_insurance{
		span{display: block; width: 100%;}
		label{
			position: relative; display: block; width: 100%; padding-bottom: 15px; left: 0; top: 0;
			&:before{border-color: @borderColor2;}
		}
	}
	.field-insurance_provider{
		margin-top: 0;
		select{box-shadow: none;}
		label{display: block; width: 100%; padding-bottom: 15px;}
	}
	.prescription-btn{margin-left: 0; display: block; width: 100%; margin-top: 25px;}
}
/*------- /prescription -------*/

/*------- tellfriend -------*/
.tellfriend{position: relative; display: block; width: 80%; margin: 0 auto;}
.btn-tellfriend{margin-left: 0!important; float: left;}
.btn-tellfriend-close{position: relative; float: right; width: auto; text-align: right; font-size: 18px; line-height: 24px; color: @red; padding-top: 15px;}
/*------- /tellfriend -------*/

/*------- Hover -------*/
@media screen and (min-width: 1200px) {
	/*------- 1200 buttons hover -------*/
	.btn, input[type=submit], button{
		&:hover{color: #fff; background: @lblue/1.2; text-decoration: none;}
	}
	.btn-blue:hover{background: @blue/1.2;}
	.btn-green:hover{background: @green/1.2;} 
	.btn-special:hover{
		background: @lblue; color: #fff;
		span:before{color: #fff;} 
	}
	.btn-white:hover{background: @lblue;}
	.btn-white2:hover{background: @blue;}
	/*------- /1200 buttons hover -------*/

	/*------- 1200 nav top hover -------*/
	.nav-top{
		li:hover>a{color: @lblue;} 
		ul li:hover>a{color: @blue;}
	}
	.item-tel, .item-mail{
		a:hover{color: @lblue;}
		a[href^=tel]:hover{color: @textColor;}
	}
	/*------- /1200 nav top hover -------*/
	.m-menu:hover{background: @lblue;}
	/*------- 1200 header hover -------*/
	
	/*------- /1200 header hover -------*/

	/*------- 1200 auth widget hover -------*/
	.aw-login:hover{color: @lblue;}
	/*------- /1200 auth widget hover -------*/

	/*------- 1200 cart widget hover -------*/
	.ww-items:hover{background: @green/1.2;}
	/*------- /1200 cart widget hover -------*/
	
	/*------- 1200 search widget hover -------*/
	.sw-btn:hover{background: @lblue/1.2;}
	/*------- /1200 search widget hover -------*/

	/*------- 1200 sidebar hover -------*/
	.cms-nav{
		&>li{
			&:hover{
				&>a span:before{opacity: 0.2;}
			}
		}
		a{
			&:hover{color: @lblue;}
		}
	}
	/*------- /1200 sidebar hover -------*/

	/*------- 1200 breadcrumbs hover -------*/
	.bc a:hover{color: @lblue;}
	/*------- /1200 breadcrumbs hover -------*/

	/*------- 1200 share hover -------*/
	.share-item:hover{
		background: @lblue/1.2; 
	}
	/*------- /1200 share hover -------*/
	
	/*------- 1200 newsletter hover -------*/
	.nw-button:hover{background: @blue/1.2;}
	/*------- /1200 newsletter hover -------*/

	/*------- 1200 footer hover -------*/
	.nav-footer li:hover{
		a{color: @lblue;}
	}
	.social{
		a:hover{background: #526BA3;}
		.yt:hover{background: #FF0000;}
		.ins:hover{background: #d62f78;}
		.gp:hover{background: #dd4a38;}
		.tw:hover{background: #1da1f2;}
		.in:hover{background: #0276b4;}
	}
	/*------- /1200 footer hover -------*/

	/*------- 1200 homepage hover -------*/
	.hpw-right{
		.pp:hover{
			.pp-cnt{box-shadow: none;}
			.pp-image{
				border-radius: 3px 3px 0 0; box-shadow: 0 22px 20px -16px rgba(66,70,86,0.2);
				img{border-radius: 3px 3px 0 0;}
			}
			.pp-title{color: @lblue;}
		}
	}
	/*------- /1200 homepage hover -------*/

	/*------- 1200 homepage catalog widget hover -------*/
	.hcw-back-list a:hover{color: @lblue;}
	/*------- /1200 homepage catalog widget hover -------*/

	/*------- 1200 publish index hover -------*/
	.p-categories{
		li:hover a{color: @lblue;}
	}
	/*------- /1200 publish index hover -------*/

	/*------- 1200 publish index hover -------*/
	.p-categories{
		li:hover a{color: @lblue;}
	}
	/*------- /1200 publish index hover -------*/

	/*------- 1200 publish post hover -------*/
	.pp:hover{
		.pp-cnt{box-shadow: 0 10px 20px 0 rgba(66,70,86,0.3);}
	}
	/*------- /1200 publish post hover -------*/

	/*------- 1200 filters hover -------*/
	.filters-category{
		a:hover span:after{opacity: .2;}
	}
	/*------- /1200 filters hover -------*/

	/*------- 1200 active filters hover -------*/
	.cf-active-item:hover{
		color: @textColor*1.2;
		span:before{color: @red*1.2;}
	}
	/*------- /1200 active filters hover -------*/

	/*------- 1200 catalog post hover -------*/
	.cp:hover{
		z-index: 1;
		&:after{opacity: 1; visibility: visible;}
		.cp-btn-details{color: @white; background: @blue;}
	}
	/*------- /1200 catalog post hover -------*/

	/*------- 1200 catalog detail hover -------*/
	.cd-thumbs{
		.owl-dot:hover{border-color: @lblue;}
	}
	.roc-item:hover{border-color: @lblue;}
	.size-info-link:hover{
		color: @lblue;
		span:after{background: @lblue;}
	}
	/*------- /1200 catalog detail hover -------*/

	/*------- 1200 catalog detail related hover -------*/
	.cd-related-content{
		.owl-next, .owl-prev{
			&:hover{
				&:before{color: @blue;}
			}
		}
	}
	.cd-related-item:hover{
		border-color: @borderColor2;
		.cd-related-btn{opacity: 1; visibility: visible;}
	}
	/*------- /1200 catalog detail related hover -------*/

	/*------- 1200 add to cart container hover -------*/
	.modal-view-cart:hover{background: @green; color: @white;}
	.modal-finish-shopping:hover{background: @green/1.2;}
	.modal-continue-shopping:hover{color: @blue*1.2;}
	/*------- /1200 add to cart container hover -------*/

	/*------- 1200 contact hover -------*/
	.facebook:hover{background: @fblue;}
	.youtube:hover{background: @yred;}
	/*------- /1200 contact hover -------*/

	/*------- 1200 auth hover -------*/
	.auth-menu a:hover{color: @lblue;}
	.a-menu a:hover{text-decoration: none;}
	/*------- /1200 auth hover -------*/

	/*------- 1200 search hover -------*/
	.s-nav a:hover{color: @lblue;}
	.s-item-title a:hover{color: @lblue;}
	/*------- /1200 search hover -------*/

}
/*------- /Hover -------*/

/*------- Responsive -------*/

@media screen and (max-width: 1700px) {
	.extra-image{margin-left: -20px!important; width: calc(~"100% - -40px")!important;}

	/*------- 1700 header -------*/
	.header, .categories-cnt{
		.wrapper{width: 100%; padding: 0 30px;}
	}
	.logo{width: 245px; height: 40px; top: 21px; left: 30px; background-size: contain; background-repeat: no-repeat;}
	.header-contact{
		right: 365px; top: 20px;
		li{display: block; padding-bottom: 5px;}
	}
	.m-menu{
		display: block; position: absolute; width: 60px; height: 60px; background: @blue; border-radius: 3px; top: 10px; right: 30px; .transition(all);
		&:after{.pseudo(100%,100%); .icon-menu; font: 33px/60px @fonti; color: @white; text-align: center; color: @white; top: 0; left: 0; .transition(all);}
		&.active{background: @lblue;
			&:after{.icon-close; font-size: 24px;}
			&:before{.pseudo(10px,10px); background: @white; transform: rotate(45deg); bottom: -5px; left: 50%; margin-left: -5px;}
		}
	}
	.nav-top{
		left: auto; right: 30px; top: 70px; width: 265px; background: @white; padding: 30px 30px 25px; box-shadow: 0 18px 20px rgba(0,0,0, 0.2); display: none;
		&.active{display: block;}
		li{
			display: block; width: 100%; font-size: 18px; line-height: 24px; padding-bottom: 5px;
			&.has-children>a span:before{font-size: 4px; top: 10px;}
		}
		a{display: block; padding: 0;}
		ul{
			background: none; position: relative; left: auto; top: auto; width: 100%; padding: 5px 0; display: block; opacity: 1;
			li{font-size: 16px; padding-bottom: 0;}
			a{color: @lblue;}
		}
	}
	/*------- /1700 header -------*/

	/*------- 1700 cart widget -------*/
	.ww{right: 100px;}
	.ww-items{
		width: 145px; padding-left: 50px;
		&:before{left: 11px;}
		&:after{top: 27px; right: 13px;}
	}
	.ww-preview:before{right: 114px;}
	/*------- /1700 cart widget -------*/

	/*------- 1700 auth widget -------*/
	.aw{right: 257px;}
	.aw-login{padding-left: 30px;}
	/*------- /1700 auth widget -------*/

	/*------- 1700 search widget -------*/
	.sw{
		top: 0; right: 30px;
		&.active{
			top: 4px;
			.sw-form{
				display: block;
				&:before{display: none;}
			}
			.sw-toggle{
				width: 54px; height: 54px; left: 0; top: 0; top: 0px; z-index: 1;
				&:after{.icon-close; color: @red; line-height: 54px; font-size: 15px;}	
			}
		}
	}
	.sw-toggle{
		width: 60px; height: 60px; position: absolute; top: 1px; right: 0; background: none;
		&:after{.pseudo(100%,100%); .icon-search; font: 20px/60px @fonti; color: @white; text-align: center; top: 0; left: 0;}
	}
	.sw-form{width: 448px; display: none;}
	/*------- /1700 search widget -------*/

	/*------- 1700 autocomplete -------*/
	.ui-autocomplete .autocomplete-cnt{width: 359px;}
	/*------- /1700 autocomplete -------*/

	/*------- 1700 homepage -------*/
	.homepage-main{width: 100%!important;}
	.homepage-intro{
		padding: 30px 30px 0; width: 100%; margin: 0 auto;
		&.active{width: 100%; z-index: 3;}
	}
	.homepage-intro-left{padding-top: 150px; width: auto;}
	.hir-note{margin-bottom: 15px;}
	.hir-btn{top: 10px;}
	.homepage-form .field{margin: 0 20px 20px 0; width: calc(~"33.3333% - 14px");}
	.hpi-info{top: -55px; width: 655px; padding: 30px 50px;}
	.homepage-post-intro{
		padding: 20px 0;
		.wrapper{width: 100%; padding: 0 30px;}
	}
	.hpi-left{padding-left: 65px; margin-right: 55px;}
	.hpi-cnt{font-size: 18px; padding-bottom: 10px;}
	.homepage-slider{
		.owl-dots{left: 76px;}
	}
	.hil-contact{
		font-size: 14px; line-height: 17px; font-weight: 500;
		a[href^=tel]{font-size: 64px; line-height: 70px; padding-top: 0;}
	}
	.hil-label{font-size: 28px; line-height: 34px;}
	.hir-note-buttons{
		display: block; width: auto; position: relative; float: right; margin-right: 70px; padding-top: 155px;
		&.active{display: none;}
		a{margin-bottom: 15px; float: none; width: 250px; display: block; padding: 0; text-align: center; position: relative; left: auto; right: auto; top: auto; bottom: auto;}
	}
	.hir-note{
		display: none;
		&.active{
			display: block;
			.btn{display: none;}
		}
	}
	.homepage-form{
		display: none;
		&.active{display: block;}
		.submit, .field:nth-child(8){width: calc(~"68% - 25px");}
		.field{
			input, select{box-shadow: none;}
		}
	}
	.homepage-intro-right.active{
		position: absolute; top: 0; right: 0; left: 0; bottom: auto; width: auto; height: auto; background: @blue; padding: 45px 122px; z-index: 3; box-shadow: 0 10px 10px 0 rgba(0,0,0,0.5);
		.hir-close-btn{
			display: block; position: absolute; top: 25px; right: 50px; width: 20px; height: 20px;
			span{display: none;}
			&:after{.pseudo(20px,20px); .icon-close; font: 20px/20px @fonti; color: @white; top: 0; left: 0; text-align: center;}
		}
	}
	.categories-cnt{
		&.active{display: none;}
	}
	.field-special-tooltip{
		width: 100%;
		&:before{right: auto; left: 20px;}
	}
	.homepage-submit-btn{background: @turquoise; box-shadow: none;}
	.hpi-left, .hpi-right{
		font-size: 14px; line-height: 18px; padding-left: 45px; margin-right: 45px;
		&:before{width: 30px; height: 40px; font-size: 40px; line-height: 40px; top: 8px;}
		strong{font-size: 18px; line-height: 24px; padding-bottom: 3px;}
	}
	/*------- /1700 homepage -------*/

	/*------- 1700 homepage publish widget -------*/
	.homepage-pw{
		margin-top: 30px;
		.wrapper{width: 100%; padding: 0 30px;}
	}
	.hpw-title{font-size: 55px; line-height: 60px; width: 50%;}
	.hpw-subtitle{width: 50%;}
	.hpw-container{margin-top: 35px;}
	.hpw-left{
		width: 720px;
		.pp-cnt{width: 90%; padding: 30px 45px 40px; margin-top: -100px;}
	}
	.hpw-right{
		width: 550px;
		.pp-image{margin-right: 20px;}
		.pp-cnt{width: 355px;}
	}
	/*------- /1700 homepage publish widget -------*/

	/*------- 1700 homepage catalog widget -------*/
	.homepage-cw{
		margin: 70px 0 90px;
		.wrapper{width: 100%; padding: 0 30px;}
	}
	.hcw-image{min-height: inherit;}
	/*------- /1700 homepage catalog widget -------*/

	/*------- 1700 homepage about -------*/
	.homepage-about{
		padding: 70px 0 0;
		.wrapper{width: 100%; padding: 0 30px 1px;}
	}
	.ha-content h2{font-size: 36px; line-height: 40px;}
	.ha-left{width: 610px;}
	.ha-right{width: 372px; margin-right: 278px;}
	.ha-right-list{
		strong{font-size: 21px;}
		li{padding-bottom: 25px;}
	}
	.ha-image img{width: 310px;}
	/*------- /1700 homepage about -------*/

	/*------- 1700 newsletter -------*/
	.nw{
		.wrapper{width: 100%; padding: 0 30px;}
	}
	.nw-title{left: 30px;}
	.nw-subtitle{left: 305px;}
	.nw-cnt{right: 30px;}
	/*------- /1700 newsletter -------*/

	/*------- 1700 footer -------*/
	.footer{
		padding-top: 65px;
		.wrapper{width: 100%; padding: 0 30px;}
	}
	.footer-col{width: 20%;}
	.social{
		margin-top: 15px; /* text-align: center; */
		a{
			margin-bottom: 3px; margin-right: 3px; width: 40px; height: 40px;
			&:before{font-size: 17px; line-height: 14px; left: 16px; top: 13px;}
		}
		.yt:before{left: 11px; top: 13px; font-size: 13px; line-height: 13px;}
		.ins:before{font-size: 18px; left: 10px; top: 9px;}
		.gp:before{left: 11px; top: 12px;}
		.tw:before{left: 12px; top: 12px;}
		.in:before{top: 9px; left: 11px;}
	}
	.footer-bottom{padding-top: 65px;}
	/*------- /1700 footer -------*/

	/*------- 1700 main -------*/
	.wrapper{width: 1290px;}
	.main-content{width: 71%; padding-left: 80px;}
	.wrapper2{width: 100%;}
	/*------- /1700 main -------*/

	/*------- 1700 publish post -------*/
	.pp{width: calc(~"25% - 30px");}
	.pp-cnt{width: 90%;}
	.pp-big{
		width: calc(~"50% - 20px");
		.pp-cnt{width: 90%;}
	}
	/*------- /1700 publish post -------*/

	/*------- 1700 publish detail -------*/
	.pd-main-image{
		width: 100%;
		img{width: auto; height: auto; display: block; max-width: 100%;}
	}
	.pd-content-cnt{width: 1190px;}
	.pd-other-title{padding-top: 60px;}
	.pd-other-items .pp{width: 100%;}
	.cd-prescription{
		.homepage-form{
			display: block;
			.field{
				&:nth-child(8){width: calc(~"68% - 25px");}
			}
			.submit{width: calc(~"68% - 25px");}
		}
		.cd-prescription-close{width: 20px;}
	}
	/*------- /1700 publish detail -------*/

	/*------- 1700 catalog detail related -------*/
	.cd-related-item-image{min-height: 200px;}
	/*------- /1700 catalog detail related -------*/

	/*------- 1700 catalog post -------*/
	.cp-lowest-price{
		br{display: initial;}
	}
	/*------- /1700 catalog post -------*/

	/*------- 1700 catalog detail -------*/
	.cd-main{width: auto; padding-left: 30px; padding-right: 30px;}
	.cd-main-content{
		width: 100%; padding-left: 0;
		.wrapper2{width: 100%;}
	}
	.cd-col1{width: 650px;}
	.cd-col2{padding-right: 0;}
	.cd-buttons-container{
		a{width: 48%;}
	}
	.cd-thumbs{width: 100px;}
	.cd-hero-image{width: 515px;}
	.cd-related-post-cnt{width: 65%;}
	.cd-related{margin-top: 80px;}
	.cd-related-content{
		.owl-prev{left: -33px;}
		.owl-next{right: -43px;}
	}

	.cd-current-price{font-size: 30px;}
	/*------- /1700 catalog detail -------*/

	/*------- 1700 contact -------*/
	.contact-left{width: 455px;}
	.contact-right{margin-right: 0; width: 370px;}
	/*------- /1700 contact -------*/

	/*------- 1700 faq -------*/
	.page-faq .wrapper2{width: 100%;}
	/*------- /1700 faq -------*/

	/*------- 1700 checkout -------*/
	.header-checkout .logo{left: 30px; width: 245px; background-size: contain; height: 40px; top: 23px;}
	.hc-steps{width: 785px; font-size: 30px;}
	/* .wc-container{
		form{margin: auto;}
	} */
	//.cart-small .w-totals-cnt{padding-left: 115px;}
	.checkout-footer{
		.dev{right: 30px;}
	}
	.checkout-login-main{width: 1090px;}
	.checkout-main{width: 995px;}
	.checkout-main-payment{width: 995px;}
	/*------- /1700 checkout -------*/

	/*------- 1700 search -------*/
	.search-main-content{width: 100%; padding-left: 0;}
	/*------- /1700 search -------*/

	/*------- 1700 auth login -------*/
	.pal-main-content{width: 1050px; margin: auto;}
	/*------- /1700 auth login -------*/
}

@media screen and (max-width: 1400px) {
	/*------- 1400 homepage -------*/
	.homepage-slider .owl-dots{left: 39px;}
	/*------- /1400 homepage -------*/

	/*------- 1400 footer -------*/
	.footer-col3{width:  22%; text-align: left;}
	.social{text-align: left;}
	.footer-col4{width: 18%;}
	/*------- /1400 footer -------*/
}

@media screen and (max-width: 1350px) {
	/*------- 1350 selectors -------*/
	body{font-size: 18px; line-height: 26px;}

	h1{font-size: 48px; line-height: 54px;}
	h2{font-size: 34px; line-height: 40px;}
	h3{font-size: 24px; line-height: 30px;}
	h4{font-size: 20px; line-height: 26px;}

	.extra-image{margin-left: 0!important; width: 100%!important;}
	/*------- /1350 selectors -------*/

	/*------- 1350 forms -------*/
	input[type=checkbox]+label{
		padding: 4px 0 0 30px;
		&:before{width: 20px; height: 20px; text-indent: 0; font-size: 12px; line-height: 20px;}
	}
	/*------- /1350 forms -------*/

	/*------- 1350 header -------*/
	.header-contact{
		top: 33px; right: 244px;
		li{display: inline-block; padding-bottom: 0; font-size: 16px; line-height: 18px;}
	}
	.nav-top li.has-children>a span:before{top: 11px;}
	/*------- / 1350 header -------*/

	/*------- 1350 cart widget -------*/
	.ww-items{
		width: 60px; padding-left: 0;
		&:before{left: 15px; top: 20px;}
		&:after{display: none;}
	}
	.ww-c-title{display: none;}
	.ww-preview:before{right: 25px;}
	/*------- /1350 cart widget -------*/

	/*------- 1350 auth widget -------*/
	.aw{width: 60px; height: 60px; font-size: 0; right: 170px; top: 10px;}
	.aw-login{
		font-size: 0; padding: 0; width: 100%; height: 100%;
		&:before{width: 100%; height: 100%; line-height: 60px; text-align: center; top: 0;}
	}
	/*------- /1350 auth widget -------*/

	/*------- 1350 search widget -------*/
	.sw.active{
		top: 0; right: 0; left: 0; bottom: 0; width: auto; border: 0; border-radius: 0; border-top: 1px solid @borderColor2; border-bottom: 1px solid @borderColor2;
		.sw-form{width: 100%; display: block; height: 100%;}
		.sw-toggle{
			height: 60px; 
			&:after{line-height: 60px;}
		}
	}
	.sw-input{height: 100%; border: none; border-radius: 0;}
	.sw-btn{top: 8px;}
	/*------- /1350 search widget -------*/

	/*------- 1350 homepage -------*/
	.homepage-intro-left{padding-top: 90px;}
	.homepage-intro-right{width: auto;}
	.hir-note-buttons{
		padding-top: 90px;
		.hir-btn{border-color: @white;}
	}
	.hpi-info{width: 475px; padding: 25px 30px; background-size: 65% 110%; background-position: 287px -9px; top: -80px;}
	.hpi-info-title{font-size: 28px; line-height: 36px; margin-bottom: 25px;}
	.hpi-cnt{
		font-size: 16px; line-height: 22px; padding-left: 27px; padding-bottom: 20px;
		&:before{top: 0;}
	}
	/*------- /1350 homepage -------*/

	/*------- 1350 homepage publish widget -------*/
	.homepage-pw{margin-top: 57px;}
	.hpw-title{font-size: 48px; line-height: 54px;}
	.hpw-subtitle{font-size: 16px; line-height: 19px;}
	.hpw-container{display: flex;}
	.hpw-left{
		width: 520px; flex-shrink: 0; flex-grow: 0;
		.pp-cnt{width: 100%; margin-top: 0; border-radius: 0; padding: 15px 0; box-shadow: none!important;}
		.pp-headline{font-size: 16px; line-height: 19px;}
		.pp-title{font-size: 24px; line-height: 30px;}
		.pp-desc{font-size: 14px; line-height: 19px; padding-top: 7px;}
	}
	.hpw-right{
		width: 100%; flex-grow: 1;
		.pp{margin-bottom: 15px; display: flex; align-items: center;}
		.pp-image{width: 135px; margin-right: 20px; flex-grow: 0; flex-shrink: 0;}
		.pp-cnt{flex-grow: 1; width: auto;}
		.pp-desc{display: none;}
		.pp-headline{font-size: 14px; line-height: 19px;}
		.pp-title{font-size: 18px; line-height: 22px; overflow: none; white-space: inherit; text-overflow: none;}
	}
	.hpw-btn{margin-top: 15px; padding: 0 35px;}
	/*------- /1350 homepage publish widget -------*/

	/*------- 1350 homepage catalog widget -------*/
	.homepage-cw{margin: 50px 0;}
	.hcw-container{display: block;}
	.hcw-col{
		display: flex; width: 100%; margin-bottom: 20px;
		.btn{display: block; width: 180px; padding: 0; text-align: center; height: 47px; line-height: 47px; position: absolute; bottom: 35px; left: 40px;}
	}
	.hcw-image{width: 335px; height: 277px; display: inline-block; vertical-align: top;}
	.hcw-cnt{display: inline-block; vertical-align: top; width: 63%; padding: 35px 40px; position: relative;}
	.hcw-cnt-title{font-size: 28px; line-height: 32px;}
	.hcw-cnt-desc{font-size: 20px; line-height: 24px;}
	/*------- /1350 homepage catalog widget -------*/

	/*------- 1350 homepage about -------*/
	.ha-left{width: 530px;}
	.ha-content{
		font-size: 14px; line-height: 20px;
		.btn{margin-top: 0;}
	}
	.ha-images{position: relative; display: block; width: 100%; height: auto; text-align: center; border-left: 0; margin-top: 55px; right: auto;}
	.ha-image{height: auto; display: inline-block; border-bottom: 0;}
	.ha-right-list{
		li{padding-bottom: 35px;}
		strong{font-size: 18px; line-height: 22px; padding-bottom: 4px;}
	}
	.ha-link{
		font-size: 14px; line-height: 16px; padding-left: 60px;
		&:before{font-size: 35px; width: 35px; height: 35px; line-height: 35px; top: 2px;}
	}
	.ha-delivery:before{font-size: 32px; line-height: 51px;}
	.ha-right{width: 360px; margin-right: 0;}
	.ha-images{margin-top: 25px; border-top: 1px solid @white; font-size: 0; height: 256px;}
	.ha-image{
		width: 33.33333%; height: 255px; border-right: 1px solid @white;
		&:last-child{border-right: 0;}
		img{display: block; width: 100%; height: 100%;}
	}
	/*------- /1350 homepage about -------*/
	
	/*------- 1350 newsletter -------*/
	.nw{
		.wrapper{height: 134px;}
	}
	.nw-title{display: none;}
	.nw-subtitle{text-align: left; left: 30px; font-size: 16px; line-height: 20px; width: 390px; font-weight: 100; top: 48px;}
	.nw-cnt{top: 40px;}
	/*------- /1350 newsletter -------*/

	/*------- 1350 footer -------*/
	.footer-col{width: auto;}
	.footer-col1{width: 223px;}
	.footer-col2{width: 203px;}
	.footer-col3{width: 325px;}
	.footer-col4{
		width: 162px; padding-top: 0;
		.convatec{padding-top: 22px;}
		a,img{max-width: 100%;}
	}
	.footer-col5{
		width: 162px; margin-top: 25px;
		img{width: auto; height: auto; max-width: 100%; display: block; margin: auto;}
		.payway{margin-bottom: 15px;}
	}
	.footer-login{width: 751px; margin-top: 25px; padding-top: 22px;}
	.footer-bottom{width: 725px; position: relative; float: left; margin-top: -32px; padding-top: 0;}
	.copy{padding-top: 0;}
	.dev{position: relative; float: right; bottom: auto;}
	.social{
		text-align: left; margin-top: 20px;
		a{margin-right: 10px; margin-bottom: 0;}
	}
	/*------- /1350 footer -------*/

	/*------- 1350 main -------*/
	.main{width: 100%; padding: 30px 30px 60px;}
	.page-homepage .main{padding: 0;}
	.sidebar{width: 220px; padding-right: 10px;}
	.cms-nav{
		&>li{
			margin-bottom: 10px;
			&>a{font-size: 16px; line-height: 22px;}
			ul>li{margin-top: 7px;}
		}
		ul{
			font-size: 16px; line-height: 20px;
			ul{font-size: 14px; line-height: 19px;}
		}
	}
	.main-content{width: 75.9%; padding-left: 30px;}
	.cms-content{
		padding-top: 15px;
		ul{
			margin-left: 0;
			li{
				padding: 2px 0 2px 15px;
				&:before{top: 11px;}
			}
		}
	}
	/*------- /1350 main -------*/

	/*------- 1350 contact -------*/
	.page-contact{
		h2{font-size: 24px; line-height: 30px;}
		.main-content{font-size: 16px; line-height: 22px; padding-left: 30px;}
		p{padding-bottom: 10px;}
	}
	.contact-left{width: 300px;}
	.contact-right{width: 300px;}
	.map{height: 300px;}
	.infoBox{
		width: 260px; padding: 15px 20px; font-size: 13px; line-height: 16px;
		a{text-decoration: underline;}
		a[href^=tel]{text-decoration: none;}
	}
	/*------- /1350 contact -------*/

	/*------- 1350 faq -------*/
	.page-faq{
		h1{padding-bottom: 25px; padding-top: 10px;}
		.main-content{padding-left: 30px;}
		.main{padding-bottom: 60px;}
	}
	.faq-group-title{font-size: 36px; line-height: 42px; padding-bottom: 15px; margin-top: 10px;}
	.fp{padding-bottom: 15px;}
	.fp-title{
		font-size: 24px; line-height: 30px;
		span{display: block; padding-left: 35px;}
	}
	.fp-cnt{padding-left: 35px; padding-top: 5px;}
	/*------- /1350 faq -------*/

	/*------- 1350 publish index -------*/
	.page-publish-index{
		.main{padding-top: 25px; padding-bottom: 40px;}
		h1{padding-top: 15px;}
	}
	.p-categories{
		padding-top: 21px;
		a{font-size: 18px; line-height: 24px; padding: 0 20px;}
	}
	.p-items{
		padding-top: 22px;
		.pp-cnt{justify-content: flex-start;}
	}
	.p-desc{padding: 20px 100px 0; max-width: 100%;}
	/*------- /1350 publish index -------*/

	/*------- 1350 publish post -------*/
	.pp{
		width: calc(~"25% - 12px"); margin: 0 15px 40px 0;
		&:nth-child(4n-2){margin-right: 0;}
	}
	.pp-cnt{display: block; margin-top: 0; padding: 12px 0 0; width: 100%; border-radius: 0; box-shadow: none;}
	.pp-headline{font-size: 12px; line-height: 16px; padding-bottom: 4px;}
	.pp-title{font-size: 16px; line-height: 20px;}
	.pp-desc{font-size: 14px; line-height: 20px; padding-top: 4px;}
	.pp-big{
		width: calc(~"50% - 15px"); margin: 0 30px 30px 0;
		.pp-cnt{display: block; width: 100%; padding: 16px 0 4px; border-radius: 0; margin-top: 0; box-shadow: none;}
		.pp-title{font-size: 24px; line-height: 30px;}
		.pp-headline{font-size: 16px; line-height: 19px; padding-bottom: 4px;}
		.pp-desc{font-size: 14px; line-height: 20px; padding-top: 4px;}
	}
	/*------- /1350 publish post -------*/

	/*------- 1350 publish detail -------*/
	.page-publish-detail{
		.main{padding-top: 26px;}
		h1{padding-bottom: 30px;}
		.image-wrapper img{width: auto; margin: 7px 0; max-width: 100%;}
	}
	.pd-headline{padding-bottom: 6px;}
	.pd-content-cnt{width: auto; margin: -100px 25px 0; padding: 50px;}
	.pd-date{padding-bottom: 16px;}
	.extra{font-size: 22px; line-height: 30px; padding-bottom: 10px;}
	.image-title{padding: 5px 0;}
	.pd-other-title{font-size: 35px; line-height: 40px; padding-top: 40px; padding-bottom: 28px;}
	.pd-other-items{
		margin-bottom: 40px;
		.pp-desc{display: none;}
		.pp{width: 100%; margin: 0;}
	}
	.pd-other{margin-top: 30px;}
	/*------- /1350 publish detail -------*/

	/*------- 1350 catalog detail related -------*/
	.cd-related{margin-top: 42px; padding: 0 15px;}
	.cd-related-title{font-size: 35px; line-height: 40px;}
	.cd-related-item{padding: 5px; border: 0;}
	.cd-related-item-image{
		height: 305px; line-height: 305px;
		span{height: 100%;}
		img{max-height: 100%; display: inline-block; vertical-align: middle;}
		.cp-badge{right: 0; height: auto;}
	}
	.cd-related-item-cnt{padding: 10px 0 0;}
	.cd-related-item-title{padding-bottom: 7px; font-size: 14px; line-height: 20px;}
	.cd-related-item-label, .cd-related-item-old{font-size: 12px; line-height: 16px;}
	.cd-related-item-current{font-size: 18px; line-height: 23px;}
	.cd-related-lowest-price{font-size: 12px; line-height: 16px;}
	.cd-related-item-old ins{margin: 0 2px 0 7px;}
	.cd-related-item-current.red ins{margin: 0 0 0 7px;}
	.cd-related-content{
		.owl-nav{top: 125px; height: 35px;}
		.owl-prev, .owl-next{
			width: 35px; height: 35px;
			&:before{width: 35px; height: 35px; font-size: 35px; line-height: 35px;}
		}
		.owl-next{right: -30px;}
		.owl-prev{left: -30px;}
	}
	.cd-related-btn{display: none;}
	/*------- /1350 catalog detail related -------*/

	/*------- 1350 catalog index -------*/
	.page-catalog-index .main-content{width: calc(~"100% - 200px");}
	.catalog-index{
		.sidebar{padding-right: 0;}
	}
	/*------- /1350 catalog index -------*/

	/*------- 1350 catalog post -------*/
	.cp{padding: 30px 0;}
	.cp-title{font-size: 22px; line-height: 30px;}
	.cp-code{font-size: 14px; line-height: 16px; padding-bottom: 10px;}
	.cp-short-description{
		font-size: 18px; line-height: 26px; 
		ul li{
			padding-top: 0;
			&:before{top: 10px;}
		}
	}
	.cp-image{width: 160px;}
	.cp-cnt{padding-left: 50px;}
	.cp-current-price, .cp-discount-price{font-size: 20px; line-height: 26px;}
	.cp-btn-details{padding: 12px 29px;}
	.cp-price{padding-top: 0;}
	.cp-lowest-price{
		br{display: none;}
	}
	.cp-badge{padding: 13px 6px;}
	.c-desc{
		font-size: 18px; line-height: 26px; padding-bottom: 5px;
		ul{
			margin-left: 0;
			li{
				padding-top: 0; padding-left: 15px;
				&:before{top: 10px;}
			}
		}
	}
	.c-extra-desc.special .ced-title{font-weight: 300; font-size: 24px; line-height: 30px;}
	.c-toolbar{margin-top: 25px; padding-bottom: 25px;}
	.c-sort{width: 200px;}
	.ced-content{font-size: 18px; line-height: 26px;}
	.ced-container{margin-bottom: 12px;}
	.ced-title{font-size: 28px; line-height: 34px; padding-bottom: 12px;}
	/*------- /1350 catalog post -------*/

	/*------- 1350 filters -------*/
	.cf{width: 100%;}
	.filter-category-title{font-size: 16px; line-height: 22px; padding-bottom: 10px;}
	.filters-category{
		padding-left: 15px;
		li{padding-bottom: 10px;}
		a{font-size: 14px; line-height: 18px;}
	}
	.cf-main-title{margin-top: 35px; font-size: 16px; line-height: 22px; padding-bottom: 10px;}
	.cf-title{
		font-size: 16px; line-height: 22px;
		&:after{width: 15px; height: 15px; font-size: 15px; line-height: 15px; top: 4px;}
	}
	.cf-item-wrapper{padding: 10px 0 25px;}
	.cf-row{
		padding-bottom: 10px;
		input[type=checkbox]+label{font-size: 12px; line-height: 16px;}
	}
	.cf-counter{color: @textColor;}
	/*------- /1350 filters -------*/

	/*------- 1350 active filters -------*/
	.cf-active{padding-top: 0; padding-bottom: 20px;}
	.cf-active-item{
		font-size: 14px; line-height: 18px; padding-bottom: 15px;
		span{padding-left: 20px;
			&:before{font-size: 10px; top: 3px;}
		}
	}
	/*------- /1350 active filters -------*/

	/*------- 1350 catalog detail -------*/
	.cd-main{
		padding: 25px 30px 40px;
		.bc{padding-bottom: 25px;}
	}
	.cd-main-content{padding-left: 0; width: 100%;}
	.cd-thumbs{
		width: 66px; margin-right: 5px;
		.owl-dot{margin-bottom: 3px;}
	}
	.success-message{width: 100%;}
	.cd-hero-image{width: 355px;}
	.cd-price{margin-bottom: 30px;}
	.cd-col1{width: 430px;}
	.cd-col2{padding-left: 40px;}
	.cd-related-posts{font-size: 24px; line-height: 30px; margin-top: 31px; margin-bottom: 20px;}
	.cd-related-post-image{width: 130px; margin-right: 20px;}
	.cd-related-post-cnt{display: inline-block; width: 61%; padding-top: 0;}
	.cd-related-post-desc{display: none;}
	.cd-related-post-title{overflow: visible; word-wrap: inherit; white-space: inherit; text-overflow: inherit;}
	.cd-related-post{margin-bottom: 20px;}
	.cd-col2-top-left{display: block; float: none; margin-bottom: 20px;}
	.cd-title{font-size: 24px; line-height: 29px; padding-bottom: 9px;}
	.cd-code{font-size: 14px; line-height: 18px;}
	.cd-col2-top-right{float: none; display: block; margin-top: 0;}
	.cd-old-price, .cd-price-label{font-size: 14px; line-height: 16px;}
	.cd-current-price{font-size: 26px; line-height: 32px;}
	.cd-tax{font-size: 12px; line-height: 14px;}
	.cd-badge-discount, .cd-variation-discount{margin-right: 10px;}
	.cd-col2-top{padding-bottom: 0;}
	.cd-short-desc{
		font-size: 16px; line-height: 22px;
		ul li:before{top: 11px;}
	}
	.cd-col2-short-description{
		.btn-special{margin-top: 0; width: 47%; text-align: center;}
	}
	.related-other-colors{margin-top: 25px;}
	.roc-title{font-size: 14px; line-height: 20px; padding-bottom: 10px; font-weight: 500;}
	.roc-item{padding: 0; border: none;}
	.cd-buy-container{margin-top: 25px;}
	.cd-variations{width: 100%; display: block;}
	.size-info-link{float: right; padding-top: 0;}
	.cd-buttons-container a{width: 47.9%; padding: 0;}
	.cd-tab-title{
		font-size: 20px; line-height: 26px; padding: 19px 0;
		&:after{top: 22px;}
	}
	.cd-tab-desc{padding-bottom: 10px;}
	.cd-related2{margin-top: 45px;}
	.cd-unavailable-title{font-size: 20px; line-height: 26px;}
	.cd-unavailable-subtitle{font-size: 14px; line-height: 18px; margin-bottom: 20px;}
	.cd-unavailable-form input{width: 58%;}
	.btn-unavailable{width: 39%;}
	.cd-unavailable{margin-bottom: 0;}
	.cd-prescription{
		z-index: 100; left: 0; right: 0;
		&:before{right: 107px;}
		form{display: block;}
		.cd-prescription-close{
			width: 20px;
			&:after{font-size: 10px;}
		}
		.homepage-form{
			.error{font-size: 10px; bottom: -15px;}
			.field{
				width: calc(~"50% - 12px"); margin: 0 24px 15px 0; 
				&:nth-child(3n){margin-right: 24px ;}
				&:nth-child(2n){margin-right: 0;}
				input{height: 45px;}
				input[type=radio]+label{padding-top: 3px; padding-left: 28px; padding-right: 20px;}
			}
			textarea{height: 115px;}
			//.field-special label:after{display: none;}
			.field-special2{
				width: 100%; display: block; margin-right: 0; font-size: 0;
				div{
					display: inline-block; vertical-align: top; margin-top: 0; width: calc(~"50% - 12px"); margin-right: 24px;
					&:nth-child(2n){margin-right: 0;}
					div{display: block; width: 100%; margin-right: 0;}
				}
			}
			.field-health_insurance{
				&>label{padding-right: 0!important;}
			}
			.field-message, .submit, .homepage-req{display: block!important; width: 100%!important; margin-right: 0;}
			.homepage-req{padding: 15px 0 0; color: @textColor; opacity: .5; display: none!important; text-align: center;}
			.m-req{display: block!important;}
			.submit{padding-bottom: 0;}
			.homepage-submit-btn{background: @blue; font-size: 16px; height: 42px; line-height: 42px;}
		}

	}
	.cd-order-prescription.active{background: @blue; color: @white;}
	.cd-short-desc ul{margin-bottom: 20px;}
	.cd-related-posts-title{margin-bottom: 10px;}
	.cd-btn-add, .cd-order-prescription{font-size: 14px;}
	.table-wrapper{
		overflow: inherit; position: relative;
		table{overflow: auto;}
	}
	.wide-table{
		padding-top: 40px;
		&:before{ .pseudo(30px,28px); background: url(images/arrow.gif) no-repeat; background-size: cover; top: 0; right: 22px;}
	}

	.cd-current-price{font-size: 22px;}
	.cd-qty{
		.wp-input-qty{width: 52px; height: 52px;}
	}
	/*------- /1350 catalog detail -------*/

	/*------- 1350 cart -------*/
	.page-shopping-cart{
		.main{padding: 32px 0 50px;}
	}
	.cart-container{width: 100%; padding: 0 30px;}
	.w-title{font-size: 42px; line-height: 48px; padding: 0 30px;}
	.w-counter{font-size: 28px;}
	.cart-left{width: 590px;}
	.cart-right{width: 300px;}
	.wp-image{width: 80px;}
	.wp-title{font-size: 16px; line-height: 20px;}
	.wp-qty{width: 110px;}
	.wp-btn-qty{
		width: 35px; height: 35px;
		&:after{font-size: 16px; line-height: 35px;}	
	}
	.wp-input-qty{width: 40px; height: 40px;}
	.wp-total{width: 140px;}
	.wp-price-current, .wp-price-discount{font-size: 24px; line-height: 30px;}
	.wp-price-old, .wp-qty-count{font-size: 12px; line-height: 15px;}
	.wp-lowest-price-outer{height: 32px;}
	.wp-lowest-price{font-size: 12px; line-height: 16px; width: 165px;}
	.cart-total-title{font-size: 20px; line-height: 24px;}
	.cart-totals{font-size: 14px; line-height: 22px;}
	.wp-message{font-size: 12px; bottom: -30px;}

	.w-totals-label{width: 47%;}
	.w-totals-value{width: 51%;}
	/*------- /1350 cart -------*/

	/*------- 1350 add to cart -------*/
	.modal-box{top: 90px;}
	/*------- /1350 add to cart -------*/

	/*------- 1350 checkout -------*/
	.header-checkout .logo{width: 177px; height: 30px; top: 30px; left: 55px;}
	.checkout-main{
		padding: 30px 45px 60px;
		.wc-col1{
			padding-right: 50px;
			&:after{top: -30px; bottom: -60px;}
		}
		.wc-col2{padding-left: 50px;}
	}
	.checkout-form{
		width: 100%;
		input[type=checkbox]+label{font-size: 16px;}
	}
	.wc-subtitle{font-size: 24px; line-height: 28px;}
	.hc-label{font-size: 14px;}
	.hc-steps{font-size: 24px; line-height: 28px; padding-top: 30px; width: 660px; float: right; margin-right: 55px;}
	.wc-col1{padding-right: 50px;}
	.wc-col2{padding-left: 50px;}
	.field-shipping{
		span label{font-size: 16px;}
		&>label{font-size: 24px; line-height: 28px;}
	}
	.phone-tooltip{font-size: 12px; line-height: 14px; top: 21px;}
	.checkout-footer{padding: 45px 0 35px;}
	.footer-title{font-size: 20px; line-height: 26px;}
	.cf-col2 img{display: block; width: auto; height: auto; max-width: 150px; margin: auto;}
	.cf-col3 p{padding-bottom: 0;}
	//.wc-step3-col1{width: 460px!important;}
	.field-payment, .section-shipping{
		label{font-size: 16px!important;}
	}
	.cart-small{
		.w-cart-title{font-size: 24px; line-height: 28px; float: none; display: block; padding-bottom: 0;}
		.w-totals-cnt{padding-left: 0;}
		.cart-totals{font-size: 18px; line-height: 24px;}
		.wp-cnt{padding-bottom: 45px;}
		.wp-lowest-price{font-size: 12px; line-height: 16px;}
	}
	.w-btn-change{font-size: 14px;}
	.w-table-small{
		.wp-title{font-size: 18px; padding-bottom: 10px;}
	}
	.checkout-last textarea{height: 120px;}
	.section-bill-address{
		font-size: 14px; line-height: 18px;
		strong{font-size: 16px; line-height: 22px;}
	}
	.checkout-footer{
		.dev, .copy{display: inline-block; width: auto; font-size: 12px; float: none;}
		.dev{margin-top: 36px; position: absolute;}
	}
	.convatec span{font-size: 10px; color: @textColor; opacity: .5;}
	.field-payment label, .section-shipping label{padding-top: 1px!important;}
	.wp-code, .wp-variations{padding-right: 100px;}
	.webshop-accept-terms input[type=checkbox]+label{padding-top: 2px;}
	.w-btn-change{float: none; display: block;}
	/*------- /1350 checkout -------*/

	/*------- 1350 checkout login -------*/
	.checkout-login-main{
		font-size: 16px; line-height: 22px;
		h1{font-size: 40px; line-height: 48px; padding-bottom: 28px;}
		h2{font-size: 24px; line-height: 30px; padding-bottom: 20px;}
	}
	.wc-auth-links{width: 170px;}
	/*------- /1350 checkout login -------*/

	/*------- 1350 auth -------*/
	.auth-main{
		padding-bottom: 50px;
		h1{font-size: 36px; line-height: 42px; padding-bottom: 20px;}
	}
	.auth-menu{padding-bottom: 22px;}
	.a-intro-left{padding-right: 30px;}
	.a-intro-user{padding-left: 30px;}
	.a-section-title{padding-bottom: 20px;}
	.auth-box.orders-container{margin-top: 50px;}
	.auth-form{width: 100%;}
	.auth-edit-profile-form, .auth-change-password-form{width: 400px;}
	/*------- /1350 auth -------*/

	/*------- 1350 auth orders -------*/
	.col-order-num{width: 283px;}
	.col-order-status{width: 185px;}
	.col-order-total{width: 255px;}
	.w-table-details{
		padding-right: 265px;
		.wp-image{width: 100px;}
		.wp-title{font-size: 18px;}
		.wp-cnt{width: 420px;}
		.wp-price-old, .wp-qty-count{font-size: 14px; line-height: 18px;}
		.wp-price-current, .wp-price-discount{font-size: 28px; line-height: 32px;}
		.wp-total{width: 160px;}
	}
	/*------- /1350 auth orders -------*/	

	/*------- 1350 auth login -------*/
	.pal-main{
		padding-bottom: 50px;
		h1{font-size: 40px; line-height: 48px; padding-bottom: 28px;}
	}
	.pal-main-content{
		width: 100%; padding: 0 15px;
		h2{font-size: 24px; line-height: 28px;}
		.form-label{
			.submit{width: 200px; padding-bottom: 0;}
			.auth-links{width: 190px; padding-bottom: 0;}
		}
		.a-have-account{padding-top: 30px;}
	}
	.pal-left{padding-right: 50px; width: 50%;}
	.pal-right{
		padding-left: 50px; width: 50%; font-size: 16px; line-height: 22px;
		ul{
			margin-top: 0;
			li:before{top: 10px;}
		}
	}
	.page-signup{
		.pal-main-content h2{padding-bottom: 20px;}
	}
	.form-label .field-healthy_worker>label{font-weight: 500;}
	/*------- /1350 auth login -------*/

	/*------- 1350 forgooten password -------*/
	.auth-forgotten-password-form{width: 400px;}
	/*------- /1350 forgooten password -------*/

	/*------- 1350 search -------*/
	.page-search{
		.page-header{margin-bottom: 70px;}
		.main{padding-top: 25px;}
		.main-content{width: 72.6%;}
		h1{
			font-size: 18px; line-height: 24px;
			span{font-size: 36px; line-height: 42px; padding-top: 13px;}
		}
		.page-header-cnt{
			border: none; position: relative; padding: 5px 0;
			&:before{.pseudo(auto,1px); background: @borderColor2; top: 0; left: -30px; right: -30px;}
			&:after{.pseudo(auto,1px); background: @borderColor2; bottom: 0; left: -30px; right: -30px;}
		}
		.c-sort{
			width: 210px;
			select{height: 50px;}
		}
		.c-counter{width: 210px;}
		.wrapper2{margin-left: 0; width: 100%;}
		.sidebar{width: 250px;}
	}
	.s-nav{
		li.selected a{color: @textColor; opacity: .5; text-decoration: none;}
		a{padding: 0 10px; color: @lblue; text-decoration: underline;}
	}
	.search-main-content{width: 100%!important; padding-left: 0;}
	.page-search-publish, .page-search-cms{
		.page-header{margin-bottom: 0;}
		.page-header-cnt{margin-bottom: 30px; padding: 16px 0 19px;}
	}
	.search-main-content{
		.pp{
			width: calc(~"25% - 13px");
			&:nth-child(4n-2){margin-right: 15px;}
			&:nth-child(3n){margin-right: 15px; width: calc(~"25% - 13px");}
			&:nth-child(4n){margin-right: 0;}
		}
		.pp-desc{display: none;}
	}
	.search{font-size: 0;}
	.s-item{
		width: 100%; font-size: 18px; line-height: 26px; margin-bottom: 50px;
		.btn{width: 235px; text-align: center;}
	}
	.s-item-title{
		font-size: 28px; line-height: 34px;
		a{color: @lblue; text-decoration: underline;}
	}
	.s-toolbar{min-height: 65px;}
	/*------- /1350 search -------*/
}

@media screen and (max-width: 1100px) {
	.cp-lowest-price{
		br{display: initial;}
	}
	.cd-buttons-container{display: flex; flex-wrap: wrap;}
	.cd-btn-add{flex-grow: 1; flex-shrink: 1; margin-right: 0;}
	.cd-order-prescription{width: 100%!important; margin-top: 10px;}
}

@media screen and (max-width: 990px) {
	/*------- 990 selectors -------*/
	body{font-size: 16px; line-height: 24px;}

	.wrapper{width: 100%; padding: 0 20px;}

	h1{font-size: 36px; line-height: 44px;}
	h2{font-size: 32px; line-height: 38px;}
	h3{font-size: 26px; line-height: 32px;}
	h4{font-size: 18px; line-height: 26px;}
	/*------- /990 selectors -------*/

	/*------- 990 forms -------*/
	input[type=checkbox]+label, input[type=radio]+label{padding: 3px 0 0 32px;}
	.form-label{
		textarea+label{top: 10px;}
	}
	/*------- /990 forms -------*/

	/*------- 990 buttons -------*/
	.btn{height: auto; line-height: 22px; padding: 14px 15px;}
	/*------- /990 buttons -------*/

	/*------- 990 header -------*/
	.header{
		.wrapper{height: 60px; padding: 0 20px;}
	}
	.logo{width: 200px; height: 30px; top: 14px; left: 20px;}
	.m-menu{
		width: 50px; height: 50px; top: 5px; right: 20px;
		&:after{font-size: 25px; line-height: 52px;}
		&.active:after{font-size: 20px;}
	}
	.nav-top{
		top: 55px; right: 20px; padding: 20px 25px 15px; width: 215px;
		li{
			font-size: 14px; line-height: 20px;
			&.has-children>a span:before{top: 9px;}
		}
		ul li{font-size: 14px;}
	}
	.header-contact{
		top: 21px; right: 205px;
		li{font-size: 14px; line-height: 16px; margin-right: 25px;}
	}
	/*------- /990 header -------*/

	/*------- 990 categories -------*/
	.categories-cnt{
		.wrapper{padding: 0 20px;}
	}
	/*------- /990 categories -------*/

	/*------- 990 cart widget -------*/
	.ww{right: 80px; top: 5px;}
	.ww-items{
		width: 50px; height: 50px;
		&:before{top: 15px; left: 10px;}
	}
	.ww-counter{top: 8px; left: 23px;}
	/*------- /990 cart widget -------*/

	/*------- 990 auth widget -------*/
	.aw{width: 50px; height: 50px; top: 5px; right: 140px;}
	.aw-login:before{line-height: 50px;}
	/*------- /990 auth widget -------*/

	/*------- 990 search widget -------*/
	.sw{
		right: 20px;
		&.active{
			.sw-toggle{
				height: 54px;
				&:after{line-height: 55px;}
			}
			.sw-btn{top: 5px;}
		}
		input::placeholder{opacity: 1;}
	}
	.sw-toggle{
		width: 55px; height: 55px; top: 0;
		&:after{line-height: 55px;}
	}
	.sw-btn{
		background: @blue; width: 108px; padding-left: 14px; text-transform: uppercase;
		&:before{.pseudo(16px,16px); .icon-search; font: 16px/16px @fonti; color: @white; top: 13px; left: 20px;}
	}
	/*------- /990 search widget -------*/

	/*------- 990 autocomplete -------*/
	.ui-autocomplete{
		.autocomplete-cnt{width: 75%; padding-left: 15px;}
		.image{
			line-height: 80px;
			img{display: inline-block; vertical-align: middle;}
		}
	}
	/*------- /990 autocomplete -------*/

	/*------- 990 newsletter -------*/
	.nw{
		.wrapper{height: 104px;}
	}
	.nw-subtitle{font-size: 14px; line-height: 20px; bottom: 34px; left: 20px; width: 340px; top: auto;}
	.nw-cnt{top: auto; bottom: 25px; right: 20px; width: 350px;}
	.nw-error{padding-left: 0; position: absolute; font-size: 10px; line-height: 16px;}
	.nw-success{font-size: 14px; line-height: 20px; padding: 0 0 8px;}
	/*------- /990 newsletter -------*/

	/*------- 990 main -------*/
	.main{padding: 20px 20px 50px;}
	.sidebar{width: 195px;}
	.cms-nav{
		&>li{
			margin-bottom: 5px;
			&>a{font-size: 14px; line-height: 20px; font-weight: 500;}
		}
		ul{
			padding-left: 10px;
			li{font-size: 14px; line-height: 20px;}
			ul>li{margin-top: 5px; font-size: 12px; line-height: 17px;}
		}
	}
	.main-content{padding-left: 20px; width: 72%;}
	.cms-content{
		padding-top: 5px;
		ul li:before{top: 10px;}
	}
	.image-title{padding: 0;}
	/*------- /990 main -------*/

	/*------- 990 footer -------*/
	.footer{
		padding: 30px 0 20px;
		.wrapper{padding: 0 20px;}
	}
	.footer-title{font-size: 18px; line-height: 24px; padding-bottom: 6px;}
	.nav-footer{font-size: 14px; line-height: 26px;}
	.footer-col1{width: 210px; padding-bottom: 17px;}
	.footer-col2{width: 248px; padding-bottom: 17px;}
	.footer-col3{width: 250px; padding-bottom: 17px;}
	.social{
		a{
			margin-right: 8px; width: 33px; height: 33px;
			&:before{font-size: 15px; top: 10px; left: 13px;}
		}
		.yt:before{left: 8px; top: 10px;}
		.ins:before{font-size: 16px; left: 8.5px; top: 6px;}
		.gp:before{left: 9px; top: 8px;}
		.tw:before{left: 9px; top: 9px; font-size: 14px;}
		.in:before{top: 6px; left: 9px; font-size: 14px;}
	}
	.footer-login{
		width: 469px; margin-top: 0; padding-top: 9px; margin-bottom: 9px;
		.btn-border{padding: 0 16px; margin-right: 10px;}
	}
	.footer-col4{
		width: 239px; font-size: 0; 
		p{display: inline-block; vertical-align: top;}
		a{max-width: 110px;}
		.convatec{
			padding-top: 0;
			span{padding-left: 0; width: 100%; text-align: right;}
		}
	}
	.footer-col5{
		position: relative; float: right; margin-top: 0; width: 50%; font-size: 0; text-align: right; display: flex; justify-content: flex-end;
		p{
			display: inline-block; vertical-align: bottom; align-self: flex-end;
			&:first-child{order: 2; margin-left: 20px;}
		}
		.payway{margin-bottom: 0; width: 180px;}
	}
	.footer-bottom{position: relative; float: left; width: 50%; margin-top: 0; padding-top: 32px;}
	.copy{display: block; float: none;}
	.dev{float: none; display: block; margin-bottom: 12px;}

	.footer-exchange-rate{font-size: 14px; line-height: 18px; margin-top: 15px;}
	/*------- /990 footer -------*/

	/*------- 990 breadcrumbs -------*/
	.bc{font-size: 12px; line-height: 14px; padding-bottom: 20px;}
	/*------- /990 breadcrumbs -------*/
	
	/*------- 990 homepage -------*/
	.homepage-post-intro .wrapper{padding: 0 20px;}
	.homepage-intro-left{padding-top: 95px;}
	.hil-contact a[href^=tel]{font-size: 54px; line-height: 60px;}
	.hir-note-buttons{
		padding-top: 30px; margin-right: 40px;
		a{width: 230px; padding: 14px 0;}
	}
	.homepage-slider{
		.owl-dots{line-height: inherit; bottom: 15px;}
		.owl-dot{width: 17px; height: 17px; margin: 0 2px;}
	}
	.hpi-info{width: 370px; background-size: 62% 110%; padding: 20px 30px; background-position: 231px -9px;}
	.hpi-info-title{
		font-size: 24px; line-height: 30px; margin-bottom: 20px;
		span{font-size: 16px; line-height: 20px;}
	}
	.hpi-cnt{padding-bottom: 10px;}
	.hpi-phone:before{font-size: 16px;}
	.hpi-business-time:before{font-size: 16px;}
	.hpi-left, .hpi-right{
		font-size: 12px; line-height: 16px; padding-left: 30px; margin-right: 25px; padding-top: 0; padding-bottom: 0;
		strong{font-size: 14px; line-height: 20px; padding-bottom: 0; font-weight: 500;}
		&:before{width: 17px; height: 24px; font-size: 24px; line-height: 24px; left: 0; top: 5px;}
	}
	.homepage-intro-right.active{
		padding: 20px;
		.hir-close-btn{
			width: auto; z-index: 10; top: 53px; right: 40px;
			&:after{display: none;}
			span{
				display: block; position: relative; font-size: 16px; line-height: 18px; color: @textColor; font-weight: 300; padding-right: 30px;
				&:after{.pseudo(15px,15px); .icon-close; font: 15px/15px @fonti; color: @textColor; top: 0; right: 0;}
			}
		}
	}
	.hir-note-title{font-size: 20px; line-height: 26px;}
	.hir-note{
		padding-left: 65px;
		&:before{width: 28px; height: 40px; font-size: 37px; line-height: 40px; top: 21px;}
	}
	.homepage-form{
		.field label{padding-right: 26px;}
		.field:nth-child(8), .submit{width: calc(~"68% - 20px");}
	}
	.homepage-form .error{font-size: 12px;}
	/*------- /990 homepage -------*/

	/*------- 990 homepage catalog widget -------*/
	.homepage-cw{
		margin-bottom: 20px;
		.wrapper{padding: 0 20px;}
	}
	.hcw-cnt{width: 52%; padding: 25px;}
	.hcw-cnt-title{font-size: 24px; line-height: 28px;}
	.hcw-cnt-desc{font-size: 16px; line-height: 20px;}
	.hcw-col{
		margin-bottom: 15px;
		.btn{left: 25px; bottom: 25px;}
	}
	/*------- /990 homepage catalog widget -------*/

	/*------- 990 homepage about -------*/
	.homepage-about{
		padding-top: 33px;
		.wrapper{padding: 0 20px;}
	}
	.ha-content{
		h2{font-size: 33px; line-height: 39px; padding-bottom: 13px;}
		.btn{width: 100%; text-align: center;}
	}
	.ha-left{width: 375px;}
	.ha-right{
		width: 310px; padding-top: 53px;
		strong{font-size: 16px; line-height: 20px; padding-bottom: 0;}
	}
	.ha-link{
		padding-left: 50px;
		&:before{font-size: 33px; width: 28px; height: 40px; line-height: 40px;}
	}
	.ha-delivery:before{font-size: 24px;}
	.ha-right-list li{padding-bottom: 30px;}
	.ha-images{height: auto; line-height: 0;}
	.ha-image{height: auto;}
	/*------- /990 homepage about -------*/

	/*------- 990 contact -------*/
	.page-contact{
		.main-content{padding-left: 20px;}
		h2{font-size: 20px; line-height: 26px;}
	}
	.contact-left, .contact-right{
		float: none; width: 100%; display: block;
		a{color: @textColor; text-decoration: underline!important;}
	}
	.contact-right{ 
		.facebook, .youtube{margin-top: 0;}
		img{margin-bottom: 15px;}
	}
	.contact-left{border-bottom: 1px solid @borderColor2; padding-bottom: 20px; margin-bottom: 30px;}
	.mail, .phone{font-size: 18px; line-height: 26px; padding-left: 30px;}
	.mail{margin-bottom: 10px;}
	/*------- /990 contact -------*/

	/*------- 990 faq -------*/
	.page-faq{
		h1{padding-bottom: 15px;}
		.main-content{padding-left: 20px;}
	}
	.faq-group-title{font-size: 24px; line-height: 32px; padding-bottom: 10px;}
	.fp-title{
		font-size: 18px; line-height: 26px;
		span:after{top: 3px; font-size: 18px;}
	}
	.fp{padding-bottom: 10px;}
	/*------- /990 faq -------*/

	/*------- 990 publish index -------*/
	.page-publish-index{
		h1{padding-top: 0;}
		.main{padding-top: 20px; padding-bottom: 30px;}
	}
	.p-categories{
		padding-top: 9px;
		a{font-size: 14px; line-height: 20px; padding: 0 8px; font-weight: 500;}
	}
	.p-desc{
		padding: 20px 0;
		a[href^=tel]{text-decoration: underline;}

	}
	/*------- /990 publish index -------*/

	/*------- 990 publish post -------*/
	.p-items{
		padding-top: 20px;
	}	
	.pp:not(.pp-big){
		width: calc(~"33.3333% - 10px");
		&:nth-child(3n-1){margin-right: 0;}
		&:nth-child(4n-2){margin-right: 15px;}
	}
	.pp-big{
		width: calc(~"50% - 9px"); margin: 0 17px 17px 0;
		.pp-cnt{padding-top: 10px;}
		&:last-of-type{margin-right: 0!important;}
	}
	/*------- /990 publish post -------*/

	/*------- 990 publish detail -------*/
	.page-publish-detail{
		.main{padding-top: 20px;}
		h1{padding-bottom: 20px;}
		.cd-related2{margin-top: 0;}
	}
	.pd-headline{padding-top: 0; font-size: 14px; line-height: 18px; font-weight: 500;}
	.pd-date{font-size: 14px; line-height: 16px;}
	.extra{font-size: 18px; line-height: 28px;}
	.pd-other-title{font-size: 24px; line-height: 30px; padding-bottom: 20px;}
	.pd-other{margin-top: 0;}
	/*------- /990 publish detail -------*/

	/*------- 990 catalog index -------*/
	.catalog-index{
		.sidebar{width: 200px;}
	}
	.catalog-index h1{padding-bottom: 20px;}
	.c-toolbar{
		padding-bottom: 15px;
		&.no-dropdown{margin-top: 15px;}
	}
	.c-counter{width: 250px; font-size: 16px; padding-top: 10px;}
	.c-sort{
		width: 175px;
		select{height: 45px; font-size: 16px; background-position: 92% 19px;}
	}
	.ced-title{font-size: 24px; line-height: 30px;}
	.ced-content{
		font-size: 16px; line-height: 22px;
		.list li:before{top: 10px;}
	}
	/*------- /990 catalog index -------*/
	
	/*------- 990 homepage publish widget -------*/
	.homepage-pw{
		.wrapper{padding: 0 20px;}
	}
	.hpw-title{font-size: 42px; line-height: 50px;}
	.hpw-subtitle{font-size: 14px; line-height: 16px; font-weight: 500;}
	.hpw-left{
		width: 370px;
		.pp{width: 100%;}
		.pp-cnt{padding: 0 10px 0 0;}
	}
	.hpw-right{
		width: 340px; padding-left: 5px;
		.pp{width: 100%;}
		.pp-image{margin-right: 10px; vertical-align: top;}
		.pp-cnt{width: 185px; vertical-align: top;}
		.pp-title{font-size: 16px; line-height: 20px;}
	}
	.hpw-btn{width: 335px; text-align: center;}
	/*------- /990 homepage publish widget -------*/

	/*------- 990 filter -------*/
	.cf{padding-right: 10px;}
	.cf-item{padding-top: 10px;}
	.cf-title{
		padding-bottom: 7px;
		&:after{right: 15px; top: 3px;}
	}
	.cf-active-filters-title{font-size: 16px; line-height: 22px; padding-bottom: 15px;}
	.cf-active-item{
		font-size: 12px; line-height: 20px; padding-bottom: 5px;
		span{
			padding-left: 20px;
			&:before{width: 10px; height: 10px; font-size: 10px; line-height: 8px; top: 4px;}
		}
	}
	.cf-active{padding-bottom: 15px;}
	.cf-row{
		input[type=checkbox]+label{padding: 4px 21px 0 30px;}
	}
	/*------- /990 filter -------*/	

	/*------- 990 catalog post -------*/
	.cp-badge{font-size: 10px; line-height: 12px; padding: 14px 7px;}
	.cp-image{width: 135px;}
	.cp-title{font-size: 18px; line-height: 26px;}
	.cp-code{font-size: 12px; line-height: 14px; padding-bottom: 3px;}
	.cp-short-description{
		font-size: 14px; line-height: 20px;
		ul li:before{top: 7px;}
	}
	.cp-cnt{padding-left: 35px;}
	.cp-btn-details{font-size: 14px; font-weight: 500; font-size: 12px; padding: 9px 33px;}
	.cp-old-price, .cp-price-label{font-size: 12px; line-height: 16px;}
	.cp-current-price, .cp-discount-price{font-size: 18px; line-height: 24px;}
	.cp-lowest-price{font-size: 12px; line-height: 16px;}
	.c-desc{
		font-size: 16px; line-height: 24px;
		ul li:before{top: 8px;}
		h2{font-size: 26px; line-height: 30px; font-weight: 300;}
	}
	.cp-image img{max-height: 190px;}
	/*------- /990 catalog post -------*/

	/*------- 990 catalog detail -------*/
	.cd-main{
		.bc{padding-bottom: 20px;}
	}
	.cd-main-content{width: 100%; padding-left: 0;}
	.cd-col1{width: 360px;}
	.cd-thumbs{
		width: 56px; margin-right: 5px;
		.owl-dot{margin-bottom: 3px; padding: 1px;}
	}
	.cd-hero-slider{
		visibility: hidden;
		&.owl-loaded{visibility: visible;}
	}
	.cd-hero-image{width: 299px;}
	.cd-related-posts{font-size: 18px; line-height: 24px; margin: 20px 0 10px;}
	.cd-related-post{font-size: 0;}
	.cd-related-post-image{margin-right: 10px; vertical-align: top;}
	.cd-related-post-cnt{padding-top: 0; width: 61%; vertical-align: top;}
	.cd-related-post-headline{font-size: 14px; line-height: 18px;}
	.cd-title{font-size: 18px; line-height: 24px; padding-bottom: 4px;}
	.cd-price{margin-bottom: 5px;}
	.cd-current-price{font-size: 24px; line-height: 32px;}
	.cd-badge-discount, .cd-variation-discount{font-size: 12px; line-height: 14px; font-weight: 500; padding: 13px 5px;}
	.cd-lowest-price{font-size: 10px; line-height: 14px;}
	.cd-col2-top{padding-bottom: 20px;}
	.cd-col2-top-left{margin-bottom: 17px;}
	.cd-buttons-container{}
	.cd-short-desc{
		font-size: 14px; line-height: 18px;
		ul{
			margin-bottom: 15px;
			li{
				padding-left: 15px;
				&:before{top: 9px;}
			}
		}
	}
	.cd-col2-short-description{
		padding-top: 10px;
		.btn-special{width: 100%; height: 45px; line-height: 45px;}
	}
	.cd-buttons-container a{display: block; width: 100%; margin-top: 20px;}
	.cd-btn-add{
		width: auto!important; text-align: center; margin-right: 0; height: 50px; line-height: 50px; margin-top: 0!important;
		span:before{top: 11px;}
	}
	.cd-qty{
		width: 96px!important; margin-right: 8px; padding-top: 5px;
		.wp-btn-qty{margin-top: 0!important; width: 28px; height: 28px;}
		.wp-input-qty{width: 40px; height: 40px;}
		.wp-unit{font-size: 10px; line-height: 12px; margin-top: 1px;}
	}
	.cd-order-prescription{height: 50px; line-height: 48px;}
	.cd-tabs{margin-top: 20px;}
	.roc-item{width: calc(~"20% - 5px"); height: auto;}
	.cd-tab-title{
		font-size: 18px; line-height: 24px; padding: 15px 0;
		&:after{width: 15px; height: 15px; font-size: 15px; line-height: 15px; top: 19px;}
	}
	.cd-tab-desc{
		ul{
			margin-left: 0;
			li{
				padding-top: 0;
				&:before{top: 9px;}
			}
		}
	}
	.related-other-colors{margin-top: 20px;}
	.cd-variations-title{margin-right: 0;}
	.cd-related-title{font-size: 24px; line-height: 28px;}
	.cd-variations select{height: 45px; font-size: 14px; font-weight: 500; background-position: 92% 19px;}
	.cd-related{margin-top: 45px;}
	.cd-related2{margin-top: 45px;}
	.cd-related-content{margin-top: 20px;}
	.cd-related-item-image .cp-badge{padding: 15px 7px;}
	.cd-related-item-image{height: 220px; line-height: 220px;}
	.cd-related-item-current{font-size: 16px;}
	.cd-related-content{
		.owl-next, .owl-prev{
			width: 30px; height: 30px;
			&:before{font-size: 30px; line-height: 30px;}
		}
		.owl-next{right: -15px;}
		.owl-prev{left: -15px;}
	}
	.size-info-link{padding-top: 1px;}
	.cd-unavailable{margin-top: 20px;}
	.cd-unavailable-title{font-size: 18px; line-height: 24px; padding-bottom: 5px;}
	.cd-unavailable-subtitle{margin-bottom: 15px;}
	.cd-unavailable-form{
		input{display: block; width: 100%; float: none;}
		.error{font-size: 12px; line-height: 16px;}
	}
	.btn-unavailable{display: block; width: 100%; margin-top: 20px; padding: 16px 15px; float: none; font-weight: 300;}
	.cd-prescription{
		top: 140px; left: -140px;
		&:before{right: 158px;}
		.cd-prescription-close{margin-top: 0;}
		.homepage-form .homepage-submit-btn{background: @lblue; line-height: inherit; height: auto;}
	}
	.cd-variations-title{font-size: 14px; line-height: 16px;}
	.size-info-link{font-size: 12px; line-height: 15px;}
	.cd-prescription .cd-prescription-close:after{font-size: 15px; .icon-close;}
	.cd-unavailable-form{
		.succes-message{font-size: 16px; line-height: 22px; width: 100%;}
	}

	.cd-current-price{font-size: 20px;}
	/*------- /990 catalog detail -------*/

	/*------- /990 catalog detail related -------*/
	.cd-related-lowest-price{font-size: 10px; line-height: 14px;}
	/*------- /990 catalog detail related -------*/

	/*------- 990 share -------*/
	.share-cnt{margin-top: 25px;}
	.share-standard{margin-top: 2px;}
	.share-item{margin-right: 6px;}
	/*------- /990 share -------*/

	/*------- 990 add to cart modal -------*/
	.modal-box{
		width: auto; margin: 0 20px; top: 80px; padding: 40px 35px 35px;
		.desc{width: 100%;}
		.title{font-size: 18px; line-height: 22px;}
		.content{width: 350px;}
		.modal-price{
			font-size: 24px; line-height: 30px;
			.cd-old-price{
				font-size: 14px; line-height: 17px; color: @textColor;
				/* ins{display: initial;}
				span{display: initial;} */
			}
			.cd-current-price{
				font-size: 24px; line-height: 30px;
				/* ins{display: initial;}
				span{display: initial;} */
			}
		}
	}
	.modal-buttons{width: 100%;}
	.modal-view-cart{ width: 195px; height: 50px; line-height: 50px;}
	.modal-finish-shopping{width: 195px; height: 50px; line-height: 50px;}
	.modal-continue-shopping{margin-top: 16px;}
	/*------- /990 add to cart modal -------*/

	/*------- 990 cart -------*/
	.w-title{
		font-size: 34px; line-height: 40px; padding-bottom: 25px;
		span{font-size: 20px;}
	}
	.cart-container{padding: 0 20px; display: block;}
	.cart-left{display: block; float: none; width: 100%; padding: 0;}
	.cart-right{margin-left: auto;}
	.wp-price-old, .wp-qty-count{font-size: 14px; line-height: 16px;}
	.wp:last-child{border-bottom: 1px solid @borderColor2;}
	.empty-cart h2{font-size: 20px; line-height: 26px;}
	.wp-image{width: 103px;}
	.cart-right{padding-top: 30px;}
	.wp-total{width: 190px;}
	.wp-lowest-price-outer{height: 38px;}
	.wp-lowest-price{font-size: 14px; line-height: 19px; width: 190px;}
	/*------- /990 cart -------*/

	/*------- 990 checkout -------*/
	.header-checkout .logo{width: 165px; height: 27px; top: 29px; left: 20px;}
	.hc-steps{padding-top: 29px; width: 530px; margin-right: 20px; font-size: 20px; line-height: 24px;}
	.hc-label{line-height: 18px; width: 160px; margin-left: -80px;}
	.checkout-main{
		padding-top: 30px;
		.wc-col1{
			padding-right: 20px;
			&:after{top: -30px; bottom: -50px;}
		}
		.wc-col2{padding-left: 20px;}
	}
	.wc-col1{padding-right: 20px; width: 50%!important;}
	.wc-col2{padding-left: 20px; width: 50%!important;}
	.wc-subtitle{font-size: 20px; line-height: 26px; padding-bottom: 18px;}
	.step2{
		.field-zipcode, .field-city{display: block; width: 100%; margin-right: 0!important;}
	}
	.field-shipping{
		margin-top: 35px; padding-bottom: 0!important;
		&>label{font-size: 20px; line-height: 26px; padding-bottom: 18px;}
		span div{padding-left: 32px;}
	}
	.wc-step2-col2{
		input[type=checkbox]+label:before{top: 2px;}
	}
	.checkout-finish{font-size: 13px; line-height: 19px;}
	.field-payment, .section-shipping{
		span div{padding-left: 32px;}
	}
	.cart-small .w-cart-title{font-size: 20px; line-height: 26px; padding-bottom: 0; display: block; width: 100%;}
	.w-btn-change{display: block; width: 100%; text-align: left; float: none;}
	.w-table-small{
		margin-top: 10px;
		.wp{padding: 0 0 20px; border-bottom: 0;}
		.wp-image{width: 100px;}
		.wp-title{font-size: 16px; padding-bottom: 7px;}
		.wp-code, .wp-variations{padding-bottom: 3px;}
	}
	.cart-small{
		.wp-cnt{width: 69%; padding: 0 5px 0 20px; padding-bottom: 70px;}
		.wp-price-discount,.wp-price-current{font-size: 20px; line-height: 26px;}
		.cart-totals{font-size: 16px; line-height: 22px;}
		.wp-lowest-pb{
			.wp-cnt{padding-bottom: 95px;}
		}
		.wp-lowest-price{font-size: 14px; line-height: 19px;}
	}
	.shipping-row div{
		padding-left: 32px;
		.error{margin-left: 0;}
	}
	.webshop-accept-terms input+label{
		font-size: 14px; font-weight: 500; line-height: inherit;
		&:before{top: 2px;}
	}
	.footer-title{font-size: 20px;}
	.cf-col2{
		p{padding-bottom: 0;}
		img{max-width: 100px;}
	}
	.cf-col3{
		text-align: right;
		img{max-width: 150px; width: auto; height: auto; display: inline-block; text-align: right;}
		p:last-child{padding-right: 0; margin-top: 0;}
	}
	.checkout-footer .dev{margin-top: 13px;}

	.wc-exchange-rate{margin-top: 15px;}
	/*------- /990 checkout -------*/

	/*------- 990 checkout login -------*/
	.checkout-login-main{
		padding-top: 30px;
		h1{font-size: 36px; line-height: 42px; text-align: center;}
		h2{font-size: 20px; line-height: 26px; padding-bottom: 13px; font-weight: 500;}
		.wc-col1{padding-left: 0;}
		.wc-col2{padding-right: 0;}
		.remember{
			margin-top: 0;
			label{font-weight: 300;}
		}
	}
	.btn-wc-login{display: block; width: 100%; text-align: center;}
	.wc-auth-links{
		width: 100%; display: block; margin-left: 0!important;
		span{display: inline; font-weight: 500;}
	}
	/*------- /990 checkout login -------*/

	/*------- 990 auth login -------*/
	.pal-main{
		padding-top: 30px;
		h1{font-size: 36px; line-height: 42px; text-align: left;}
	}
	.pal-main-content{
		padding: 0;
		h2{font-size: 19px; line-height: 26px; padding-bottom: 17px; font-weight: 500;}
		.remember{margin-top: 0;}
		.form-label{
			.auth-links, .submit{width: 100%; float: none;}
			.auth-links span{font-weight: 500;}
		}
	}
	.pal-left{
		padding-right: 20px;
		button{height: auto; line-height: inherit; padding: 14px 15px;}
	}
	.pal-right{padding-left: 20px;}
	.btn-login2{width: 100%; display: block; text-align: center;}
	.ls-left, .ls-right{
		float: none; width: 100%; display: block;
		.btn{display: block; text-align: center;}
	}
	.pal-main-content{
		.login-buttons{
			display: flex;
			.submit{margin-right: 5px;}
			.auth-links{
				margin-left: 5px;
				a{font-size: 12px;}
			}
		}
	}
	/*------- /990 auth login -------*/

	/*------- 990 auth -------*/
	.auth-main{padding-top: 30px;}
	.auth-menu{
		a{font-size: 16px; line-height: 22px; padding: 0 17px;}
	}
	.a-intro-title{font-size: 20px; line-height: 26px;}
	.a-intro{font-size: 15px; line-height: 22px;}
	.a-intro-left{padding-right: 20px;}
	.a-intro-user{padding-left: 20px;}
	.a-section-title{font-size: 24px; line-height: 30px; font-weight: bold;}
	.auth-box.orders-container{margin-top: 63px;}
	.a-menu li:before{top: 10px;}
	.a-edit-subtitle{font-size: 16px; margin: 5px 0 10px;}
	/*------- /990 auth -------*/

	/*------- 990 orders -------*/
	.table-order{font-size: 16px; line-height: 22px; padding: 20px 0;}
	.col-order-num{width: 207px;}
	.col-order-status{width: 152px;}
	.col-order-total{width: 240px;}
	.btn-order-details{
		font-size: 14px; color: @lblue;
		span{
			padding-right: 25px;
			&:after{width: 15px; height: 15px; font-size: 15px; line-height: 15px; top: 4px;}
		}
	}
	.w-table-details{
		padding-right: 199px;
		.wp-image{width: 80px;}
		.wp-cnt{width: 305px;}
		.wp-title{font-size: 16px;}
		.wp-price-current, .wp-price-discount{font-size: 24px; line-height: 30px;}
		.wp-total{width: 190px;}
	}
	.wp-details-sum{font-size: 24px; line-height: 24px;}
	/*------- /990 orders -------*/

	/*------- 990 search -------*/
	.page-search{
		.main{padding-top: 30px;}
		h1{
			padding-bottom: 30px;
			span{padding-top: 0;}
		}
		.c-sort{
			width: 155px;
			select{height: 40px; line-height: normal; font-size: 14px; background-position: 92% 16px;}
		}
		.c-counter{width: 155px; font-size: 13px;}
		.page-header{margin-bottom: 50px;}
		.page-header-cnt{
			padding: 10px 0;
			&:before, &:after{left: 0; right: 0;}
		}
		.sidebar{width: 220px;}
		.main-content{width: 68.9%;}
		.search-main-content{padding-left: 0;}
	}
	.page-search-cms, .page-search-publish{
		.page-header{margin-bottom: 30px;}
		.page-header-cnt{padding: 17px 0;}
	}
	.s-nav{
		padding-top: 7px;
		li.selected a{text-decoration: none; color: @textColor; opacity: .5;}
		a{font-size: 16px; line-height: 22px; padding: 0 8px; color: @lblue; text-decoration: underline;}
	}
	.search-main-content{
		.pp{
			width: calc(~"33.33333% - 14px"); margin: 0 20px 35px 0;
			&:nth-child(4n){margin-right: 20px;}	
			&:nth-child(4n-2){margin-right: 20px;}
			&:nth-child(3n){width: calc(~"33.33333% - 14px"); margin-right: 0;}
		}
	}
	.page-search-cms{
		.search{padding: 0 10px;}
	}
	.s-item-title a{color: @lblue; text-decoration: underline;}
	/*------- /990 search -------*/
}

@media screen and (max-width: 760px) {
	/*------- 760 selectors -------*/
	body{
		font-size: 14px; line-height: 20px;
		&.active{overflow: hidden;}
	}

	h1{font-size: 28px; line-height: 34px; font-weight: 600;}
	h2{font-size: 24px; line-height: 30px;}
	h3{font-size: 20px; line-height: 26px;}
	h4{font-size: 16px; line-height: 20px;}
	h2,h3,h4{padding: 15px 0 10px;}
	p{padding-bottom: 10px;}
	strong{font-weight: 500;}

	table{font-size: 14px; line-height: 18px;}

	.quick-cnt h1{font-size: 30px; line-height: 36px;}
	/*------- /760 selectors -------*/

	/*------- 760 buttons -------*/
	.btn{display: block; width: 100%; text-align: center;}
	.btn-special span{padding-left: 35px;}
	/*------- /760 buttons -------*/

	/*------- 760 breadcrumb -------*/
	.bc{
		padding-bottom: 12px;
		a{padding-right: 11px; margin-right: 3px;}
	}
	/*------- /760 breadcrumb -------*/

	/*------- 760 forms -------*/
	.form-label{
		input+label, textarea+label{font-size: 14px;}
		p, .field{padding-bottom: 15px;}
		.field-accept_terms, .field-newsletter, .remember{
			label:before{width: 18px; height: 18px; font-size: 9px; line-height: 18px; top: 0;}
		}
		input{font-size: 16px;}
		input[type=radio]+label{padding-top: 0; padding-left: 28px;}
		.field-newsletter input[type=checkbox]+label{padding-top: 2px;}
	}
	input[type=radio]+label{
		&:before{width: 16px; height: 16px;}
		&:after{width: 10px; height: 10px;}
	}
	/*------- /760 forms -------*/

	/*------- 760 header -------*/
	.header{
		border-bottom: 1px solid @borderColor2;
		.wrapper{height: 56px; padding: 0 15px;}
		&.active{position: fixed; left: 0; right: 0; background: @white; z-index: 5;}
	}
	.logo{width: 165px; height: 26px; left: 15px; top: 17px;}
	.m-menu{
		width: 35px; height: 35px; right: 15px; top: 11px;
		&:after{font-size: 20px; line-height: 35px;}
		&.active{
			&:after{font-size: 16px;}
			&:before{display: none;}
		}
	}
	.header-contact{display: none;}
	.mobile-top-menu{
		display: none; position: fixed; top: 52px; right: 0; bottom: 0; left: 0; background: @white; z-index: 10; overflow: auto; padding-top: 7px; margin-top: -3px;
		&:before{.pseudo(8px,8px); background: @white; top: 2px; right: 28px; transform: rotate(45deg); border-left: 1px solid @borderColor2; border-top: 1px solid @borderColor2; z-index: 1;}
		&.active{display: block;}
	}
	.mobile-top-menu-bottom{
		position: relative; bottom: 0; right: 0; left: 0; padding: 15px; background: @white;
		a{display: block; width: 100%; text-align: center; margin-bottom: 10px;}
		.social{
			margin-top: 25px; text-align: center;
			a{
				width: 40px; height: 40px; display: inline-block; margin-right: 6px; margin-bottom: 0;
				&:last-child{margin-right: 0;}
			}
		}
		.item-tel{margin-top: 20px;}
		.item-mail a{margin-bottom: 8px;}
		.item-tel, .item-mail{ 
			padding-left: 0;
			&:before{display: none;}
			a{font-size: 18px; line-height: 24px; color: @lblue; font-weight: 600; text-decoration: underline;}
		} 
	}
	.social{
		a:before{font-size: 17px; top: 13px; left: 15px;}
		.yt:before{font-size: 14px; top: 14px; left: 11px;}
		.ins:before{font-size: 18px; top: 9px; left: 11px;}
		.gp:before{font-size: 18px; top: 11px; left: 11px;}
		.tw:before{font-size: 16px; top: 11px; left: 8px;}
		.in:before{font-size: 16px; top: 9px; left: 12px;}
	} 
	/*------- /760 header -------*/

	/*------- 760 categories -------*/
	.categories-cnt{
		margin-bottom: 20px;
		.wrapper{padding: 0;}
	}
	/*------- /760 categories -------*/

	/*------- 760 navigation -------*/
	.nav-top{
		display: none;
		&.active{display: none;}
	}
	.mobile-top-menu-list{
		display: block; padding: 0; margin: 0; list-style: none;
		li{
			display: block; padding-bottom: 9px;
			//&.selected a{color: @lblue;}
			&.has-children>a:after{.pseudo(12px,12px); .icon-plus; font: 12px/12px @fonti; color: @lblue; top: 5px; right: 15px;}
		}
		a{color: @textColor; padding: 0 15px; font-size: 18px; line-height: 26px; display: block; font-weight: 600; position: relative;}
		&>li.has-children.active{
			&>a:after{.icon-minus;}
			&>ul{display: block;}
		}
		ul{
			padding-top: 6px; display: none;
			li{
				padding-bottom: 10px;
				&.has-children{
					&.active{
						ul{display: block;}
						&>a:after{.icon-minus;}
					}
					&>a:after{top: 4px;}
				}
			}
			a{font-size: 14px; line-height: 20px; font-weight: 500;}
			ul{
				li{padding-bottom: 7px; padding-left: 20px;}
				a{font-size: 14px; line-height: 14px; font-weight: 300;}
			}
		}
	}
	.active-nav{
		overflow: hidden; width: 100%;
		.categories-cnt{display: block!important;}
		/*.header{position: fixed; top: 0; left: 0; right: 0;}*/
		.nw, .footer, .homepage-top-intro, .homepage-post-intro, .homepage-pw, .homepage-cw, .homepage-about{display: none;}
	}
	.active-hir{
		overflow: hidden; width: 100%;
		.nw, .footer, .homepage-post-intro, .slider, .homepage-intro-left, .homepage-pw, .homepage-cw, .homepage-about{display: none;}
	}
	/*------- /760 navigation -------*/

	/*------- 760 cart widget -------*/
	.ww{width: 35px; height: 35px; top: 11px; right: 55px;}
	.ww-items{
		width: 100%; height: 100%;
		&:before{top: 11px; left: 6px; font-size: 16px; line-height: 18px;}
	}
	.ww-counter{width: 16px; height: 16px; top: 4px; left: 15px; font-size: 8px; line-height: 14px;}
	/*------- /760 cart widget -------*/

	/*------- 760 auth login -------*/
	.aw{width: 35px; height: 35px; top: 11px; right: 95px; border: 1px solid @borderColor2; border-radius: 3px;}
	.aw-login:before{font-size: 20px; line-height: 35px;}
	/*------- /760 auth login -------*/

	/*------- 760 search widget -------*/
	.sw{position: relative; display: block; width: 100%; top: auto; right: auto; bottom: auto; left: auto; border-top: 1px solid @borderColor2;}
	.sw-form{
		display: block; position: relative; width: 100%; height: 46px; line-height: normal;
		&:before{display: none;}
	}
	.sw-input{padding: 0 60px 0 15px; font-size: 14px;}
	.sw-btn{
		background: none; width: 46px; height: 46px; font-size: 0; padding: 0; top: 0; right: 12px;
		&:before{width: 100%; height: 100%; color: @lblue; line-height: 46px; top: 0; left: 0; font-size: 16px;}
	}
	.sw-toggle{display: none;}
	.ui-autocomplete{ 
		.image{
			width: 60px; height: 60px; line-height: 60px;
			img{max-height: 100%;}
		}
		.autocomplete-cnt{font-size: 14px; line-height: 22px;}
	}
	/*------- /760 search widget -------*/

	/*------- 760 main -------*/
	.sidebar{display: none;}
	.main{padding: 16px 15px 40px;}
	.main-content{display: block; float: none; width: 100%; padding: 0;}
	.cms-content{
		ul{
			margin-bottom: 15px;
			li{
				padding: 0 0 10px 18px;
				&:last-child{padding-bottom: 0;}
				&:before{width: 6px; height: 6px; top: 7px;}
			}
		}
		.btn{margin: 0 0 5px;}
		img{margin: 5px 0;}
	}
	/*------- /760 main -------*/

	/*------- 760 share -------*/
	.share-cnt{
		margin-top: 18px;
		.share-item:last-child{margin-right: 0;}
	}
	.share-item{
		width: 28px; height: 28px; margin-right: 2px;
		&:before{font-size: 13px; line-height: 13px; left: 11px; top: 8px;}
	}
	.ss_googleplus:before{font-size: 12px; line-height: 12px; left: 7px;}
	.ss_email:before{left: 6.5px; top: 10px; font-size: 10px; line-height: 10px;}
	.ss_whatsapp:before{left: 7px; top: 8px;}
	.ss_viber:before{left: 8px; top: 8px;}
	.ss_copy:before{left: 8px; top: 8px; }
	.share-title{font-size: 12px; line-height: 28px; margin-bottom: 5px;}
	.share-standard{margin-top: 4px;}
	/*------- /760 share -------*/

	/*------- 760 newsletter -------*/
	.nw{
		.wrapper{height: 100%; padding: 25px 15px 32px;}
	}
	.nw-title{display: block; position: relative; top: auto; right: auto; bottom: auto; left: auto; text-align: left; font-size: 36px; line-height: 44px; margin-bottom: 7px;}
	.nw-subtitle{display: block; position: relative; top: auto; right: auto; bottom: auto; left: auto; text-align: left; font-size: 16px; line-height: 22px; width: 100%; margin-bottom: 16px;}
	.nw-cnt{position: relative; margin: 0 auto; width: 100%; top: auto; right: auto; bottom: auto; left: auto;}
	.nw-form{
		.nw-error{padding-left: 18px;}
		&:before{display: none;}
	}
	.nw-input{border: none; padding: 0 85px 0 18px;}
	.nw-button{font-size: 14px; width: 75px; height: 44px;}
	.nw-success{line-height: 18px;}
	/*------- /760 newsletter -------*/

	/*------- 760 footer -------*/
	.footer{
		padding-top: 0; padding-bottom: 25px;
		.wrapper{padding: 0 15px;}
	}
	.footer-col{display: block; width: 100%; text-align: left;}
	.footer-col1, .footer-col2{
		&.active{
			.footer-title{
				padding-bottom: 10px;
				&:before{display: none;}
			}
			.nav-footer{opacity: 1; visibility: visible; display: block;}
		}
		display: block; width: calc(~"100% - -30px"); margin-left: -15px; text-align: left; padding: 0; border-bottom: 1px solid @borderColor2;
		.footer-title{
			font-size: 16px; line-height: 22px; padding: 16px 15px; position: relative;
			&:before{.pseudo(2px, 12px); background: @lblue; right: 20px; top: 21px;}
			&:after{.pseudo(12px, 2px); background: @lblue; right: 15px; top: 26px;}
		}
	}
	.nav-footer{padding: 0 15px 15px; opacity: 0; visibility: hidden; display: none;}
	.footer-col3{
		padding-top: 16px;
		.item-tel, .item-mail{
			padding-left: 30px; font-size: 16px!important; line-height: 22px!important;
			a{text-decoration: underline;}
			&:before{display: block; top: 3px;}
		}
		.item-mail:before{top: 6px;}
		.social {
			text-align: left;
			a{margin-right: 10px;}
		}
	}
	.footer-title{font-size: 18px; line-height: 24px; color: @textColor; padding-bottom: 8px;}
	.item-tel, .item-mail{
		padding-left: 0; font-size: 18px!important; line-height: 24px!important;
		a{color: @lblue!important;}
		&:before{display: none;}
	}
	.item-tel{margin-bottom: 5px;}
	.social{
		text-align: center; margin-top: 24px;
		p{padding-bottom: 0;}
		a{
			width: 40px; height: 40px; margin-right: 6px;
			&:before{width: 100%; height: 100%; text-align: center; line-height: 40px; top: 0; left: 0;}
			&:last-child{margin-right: 0;}
		}
		.yt, .ins, .gp, .tw, .in{
			&:before{top: 0; left: 0; line-height: 40px; text-align: center;}
		}
	}
	.footer-login{
		display: block; width: 100%;
		a{width: 100%; margin: 0 0 10px!important; text-align: center;}
	}
	.footer-col4{
		padding-top: 3px; margin-bottom: 5px;
		p{display: inline-block; width: 50%; padding-bottom: 15px;}
		a{max-width: 100%;}
		img{max-width: 130px;}
		.convatec{
			padding-top: 7px; text-align: right;
			img{display: inline-block;}
		}
	}
	.footer-col5{
		display: flex; flex-flow: column; width: auto; font-size: 0; float: none; justify-content: inherit; left: 15px; right: 15px; bottom: 35px; position: absolute;
		p{
			display: inline-block; vertical-align: middle; width: 50%;
			&:first-child{margin: 0; margin-top: 10px;}
		}
		.payway{width: 100%;}
	}
	.footer-bottom{float: none; width: 100%; padding-top: 10px; text-align: center;}
	.dev{
		margin-bottom: 0; position: relative; float: left; font-size: 0; width: 26px; height: 15px; padding: 0; font-size: 0;
		a{
			font-size: 0; display: block;
			&:first-child{padding: 0;}
			&:first-child:before{top: -6px;}
		}
	}
	.copy{float: right; width: auto; font-size: 12px; line-height: 16px;}

	.footer-exchange-rate{font-size: 14px; line-height: 20px; margin-top: 10px; margin-bottom: 17px; text-align: left; display: block;}
	/*------- /760 footer -------*/

	/*------- 760 homepage -------*/
	.homepage-intro{padding: 25px 15px;}
	.homepage-intro-left{padding-top: 0; float: none; display: block; text-align: center;}
	.hil-contact{text-align: center; font-size: 14px; line-height: 18px;
		a[href^=tel]{font-size: 38px; line-height: 44px;}
	}
	.hil-label{font-size: 18px; line-height: 24px; text-align: center;}
	.hir-note-buttons{
		float: none; margin-right: 0; width: 100%; text-align: center; display: block; padding-top: 0;
		a{width: 220px; padding: 12px 0; margin: auto auto 10px; font-size: 14px; font-weight: 500;}
	}
	.homepage-intro-right{
		float: none; width: auto; display: block; text-align: center; position: absolute; bottom: 70px; left: 15px; right: 15px;
		&.active{
			position: fixed; top: 57px; top: 3; padding: 15px; height: calc(~"100% - 57px"); overflow: auto;
			.hir-close-btn{
				top: 30px; right: 30px; width: 20px; height: 20px;
				span{
					font-size: 0; padding: 0;
					&:after{top: 2px; right: 2px;}
				}
			}
		}
	}
	.homepage-slider{
		.owl-dots{bottom: 25px; left: 15px; right: 15px; width: auto; text-align: center;}
		.owl-dot{width: 12px; height: 12px; margin: 0 5px;}
	}
	.homepage-post-intro{
		padding-top: 0; padding-bottom: 0; border-bottom: 1px solid @borderColor2;
		.wrapper{padding: 0 15px;}
	}
	.hpi-left:after{.pseudo(auto,1px); background: rgba(255,255,255,.15); bottom: 0; left: -15px; right: -15px;}
	.hpi-left, .hpi-right{
		padding: 15px 0 15px 40px; display: block; width: 100%; margin: 0;
		&:before{width: 25px; height: 34px; font-size: 34px; line-height: 34px; top: 15px;}
		strong{font-size: 16px;}
	}
	.hpi-info{position: relative; top: auto; right: auto; bottom: auto; left: auto; width: 100%; display: block; width: 100%; border-radius: 0; background: @white; padding: 25px 15px 10px; box-shadow: none;}
	.hpi-info-title{
		font-size: 20px; line-height: 30px;
		span{font-size: 14px; line-height: 18px; font-weight: 500;}
	}
	.hpi-cnt{font-size: 14px; line-height: 18px; padding-bottom: 15px;}
	.hpi-phone{
		padding-right: 60px;
		a[href^=tel]{text-decoration: underline;}
	}
	.hpi-business-time{padding-right: 70px;}
	.hir-note{
		text-align: left; padding: 15px 20px 15px 47px;
		&:before{width: 24px; height: 30px; font-size: 30px; line-height: 30px; left: 14px; top: 18px;}
	}
	.hir-note-title{font-size: 16px; line-height: 22px; max-width: 67%;}
	.hir-note-subtitle{font-size: 12px; line-height: 15px; max-width: 67%;}
	.homepage-form{
		.field{
			margin: 0 0 20px; width: 100%; display: block;
			&:nth-child(7){width: 100%; margin-right: 0;}
			&:nth-child(8){width: 100%;}
		}
		.submit{width: 100%; display: block;}
	}
	.field-health_insurance{
		text-align: left; font-size: 0;
		label{padding-bottom: 10px!important;}
		span{
			display: inline-block; vertical-align: top; width: 50%;
			label{display: block; width: 100%;}
		}
	}
	.field-insurance_provider{margin-top: 12px;}
	.homepage-req{text-align: left; padding: 5px 0 20px; width: 100%; display: block;}
	.hir-form-container{
		.homepage-req{display: none;}
		.m-req{display: block!important; text-align: center;}
	}
	/*------- /760 homepage -------*/

	/*------- 760 homepage publish widget -------*/
	.homepage-pw{
		text-align: center; margin-top: 25px;
		.wrapper{padding: 0 15px;}
	}
	.hpw-title{font-size: 33px; line-height: 41px; width: 100%;}
	.hpw-subtitle{width: 100%;}
	.hpw-container{display: none;}
	.mobile-hpw-container{display: block; margin-top: 20px;}
	.m-hpw-slider{
		.pp{width: 100%; margin: 0;}
		.pp-image{
			text-align: center;
			img{display: inline-block;}
		}
		.pp-title{font-size: 20px; line-height: 26px;}
		.pp-desc{display: none;}
		.pp-cnt{padding-top: 5px; margin: 0;}
		.owl-nav{height: 65px; top: 63px; margin-top: auto;}
		.owl-prev, .owl-next{
			background: @white; width: 65px; height: 65px;
			&:before{width: 20px; height: 20px; font-size: 20px; line-height: 20px; top: 23px;}
		}
		.owl-next{
			border-radius: 50% 0 0 50%; right: -31px;
			&:before{left: 18px;}
		}
		.owl-prev{
			border-radius: 0 50% 50% 0; left: -31px;
			&:before{left: auto; right: 18px;}
		}
	}
	.hpw-btn{margin-top: 25px; float: none; font-size: 14px;}
	/*------- /760 homepage publish widget -------*/

	/*------- 760 homepage catalog widget -------*/
	.homepage-cw{
		margin: 40px 0 2px;
		.wrapper{padding: 0;}
	}
	.hcw-col{display: block; font-size: 0; margin-bottom: 2px; display: flex;}
	.hcw-image{width: 50%; height: auto;}
	.hcw-cnt{
		width: 50%; padding: 15px; text-align: center; display: flex; justify-content: center;
		.btn{display: none;}
	}
	.hcw-cnt-desc{display: none;}
	.hcw-cnt-title{font-size: 16px; line-height: 22px; align-self: center;}
	/*------- /760 homepage catalog widget -------*/

	/*------- 760 homepage about -------*/
	.homepage-about{
		.wrapper{padding: 0 15px; flex-flow: column;}
	}
	.ha-left{width: 100%; float: none; width: 100%;}
	.ha-content{
		h2{font-size: 26px; line-height: 32px;}
		.btn{margin-top: 10px;}
	}
	.ha-right{float: none; width: 100%; display: block; padding-top: 30px;}
	.ha-right-list li{padding-bottom: 30px;}
	.ha-link{
		font-size: 12px; line-height: 19px;
		strong{font-size: 14px; line-height: 18px;}
		&:before{top: -1px;}
	}
	.ha-image{
		max-height: 97px;
		img{max-height: 97px;}
	}
	/*------- /760 homepage about -------*/

	/*------- 760 contact -------*/
	.page-contact{
		.main{padding-bottom: 0;}
		.main-content{padding-left: 0; width: 100%; font-size: 14px; line-height: 20px;}
		h2{font-size: 18px; line-height: 26px;}
	}
	.contact-left{
		border-bottom: 0; padding-bottom: 0; margin-bottom: 20px;
		a{text-decoration: underline;}
	}
	.mail, .phone{
		font-size: 16px; line-height: 22px;
		&:before{top: 2px;}
	}
	.contact-right{
		img{margin-bottom: 0;}
	}
	.map{margin-left: -15px; width: 100vw; height: 100vw;}
	.infoBox{
		padding-bottom: 10px;
		a[href^=tel]{text-decoration: underline;}
		.contact{padding-bottom: 0;}
	}
	/*------- /760 contact -------*/

	/*------- 760 faq -------*/
	.page-faq{
		.main{padding-bottom: 40px;}
		.main-content{padding-left: 0;}
	}
	.faq-group-title{font-size: 18px; line-height: 24px;}
	.fp-title{
		font-size: 16px; line-height: 22px; 
		span{
			padding-left: 25px;
			&:after{width: 10px; height: 10px; font-size: 10px; line-height: 10px; top: 5px;}
		}
	}
	.fp-cnt{padding-left: 25px;}
	/*------- /760 faq -------*/

	/*------- 760 publish index -------*/
	.page-publish-index{
		.main{padding-top: 10px;}
		h1{padding-bottom: 20px;}
	}
	.p-categories-cnt{
		position: relative; display: block; width: 100%;
		&.active{
			.m-p-categories{border-radius: 3px 3px 0 0; box-shadow: 0 10px 20px 0 rgba(0, 0, 0, 0.15);}
			.p-categories{display: block; }
		}
	}
	.p-categories{
		display: none; background: @white; position: absolute; top: 100%; right: 0; left: 0; z-index: 5; width: 100%; height: auto; box-shadow: 0 10px 20px 0 rgba(0, 0, 0, 0.15); padding: 0; margin: 0; border-radius: 0 0 3px 3px;
		/* .m-p-categories-close{
			display: block; font-size: 20px; line-height: 26px; position: relative; display: block; text-align: center; color: @textColor; border-bottom: 1px solid @borderColor2; padding-bottom: 9px;
			&:after{.pseudo(12px,12px); .icon-close; font: 12px/12px @fonti; color: @textColor; top: 7px; right: 15px;}
		} */
		li{
			display: block; width: 100%; text-align: left; border: 1px solid @borderColor2; margin-top: -1px;
			a{display: block; font-size: 16px; line-height: 22px; padding: 13px 20px; text-decoration: none; color: @textColor;}
			&:last-child{border-radius: 0 0 3px 3px;}
		}
	}
	.p-desc{padding: 0 0 20px;}
	.m-p-categories{
		display: block; width: 100%; border: 1px solid @borderColor2; padding: 14px 20px; font-size: 16px; position: relative; border-radius: 3px; color: @textColor;
		&:after{.pseudo(12px,12px); .icon-arrow; font: 8px/12px @fonti; color: @blue; top: 19px; right: 20px;}
	}
	.btn-load-more{font-size: 14px;}
	/*------- /760 publish index -------*/

	/*------- 760 publish post -------*/
	.pp{display: block; width: 100%!important; margin: 0 0 45px;}
	.pp-cnt{text-align: center;}
	.pp-headline{font-size: 12px; line-height: 14px;}
	.pp-title{font-size: 20px; line-height: 26px;}
	.pp-desc{display: none;}
	.p-items .pp-big .pp-desc{display: none;}
	.pp-big{
		.pp-headline{font-size: 12px; line-height: 16px;}
		.pp-title{font-size: 20px; line-height: 26px;}
		.pp-cnt{padding-bottom: 0;}
	}
	/*------- /760 publish post -------*/

	/*------- 760 publish detail -------*/
	.page-publish-detail{
		.bc{
			display: flex; flex-wrap: wrap; justify-content: center;
			a{
				margin-bottom: 3px;
				&:before{top: 5px;}
				&:last-of-type:before{display: none;}
			}
		}
		.bc-last{display: none;}
		.pd-headline{text-align: center;}
		.pd-title{text-align: center; font-size: 24px; line-height: 30px;}
		.extra{padding-bottom: 0;}
	}
	.pd-content-cnt{padding: 10px 0 0; margin: 0; box-shadow: none;}
	.pd-date{font-size: 12px; line-height: 14px; padding-bottom: 10px;}
	.pd-other-items{
		position: relative;
		.pp{width: 100%;}
		.owl-nav{position: absolute; height: 65px; top: 108px; width: auto; left: 0; right: 0;}
		.owl-prev, .owl-next{
			background: @white; width: 65px; height: 65px; position: absolute; top: 0;
			&:before{color: @blue; font-size: 20px; line-height: 20px; width: 20px; height: 20px;}
		}
		.owl-prev{
			border-radius: 0 50% 50% 0; left: -31px;
			&:before{top: 22px; left: auto; right: 16px;}
		}
		.owl-next{
			border-radius: 50px 0 0 50px; right: -31px;
			&:before{top: 22px; left: 15px;}
		}
	}
	/*------- /760 publish detail -------*/

	/*------- 760 catalog index -------*/
	.page-catalog-index .main-content{width: 100%;}
	.catalog-index{
		.bc{padding-bottom: 12px;}
		h1{padding-bottom: 15px;}
		.sidebar{
			position: fixed; top: 0; right: 0; bottom: 0; left: 0; background: @white; width: auto; z-index: 5; border-right: 0; padding-bottom: 135px; overflow: auto;
			&.active{display: block;}
		}
	}
	.c-desc{
		padding-bottom: 20px; border-bottom: 0;
		h2{font-size: 20px; line-height: 26px; border-bottom: 0;}
	}
	
	.mobile-div{padding-bottom: 35px; margin-top: 20px;}
	.c-counter{display: none;}
	.c-toolbar{
		margin-top: 0; display: inline-block; vertical-align: top; width: 50%; border-bottom: 0; padding-bottom: 0;
		&.no-dropdown{margin-top: 0; display: inline-block; width: 50%;}
	}
	.c-sort{
		width: 100%;
		select{border-radius: 0; border-color: @borderColor2; border-top-right-radius: 3px; border-bottom-right-radius: 3px; font-size: 14px; border-left: 0; display: block; width: 100%; background-position: right 20px top 19px;}
	}
	.mobile-filters{
		font-size: 14px; line-height: 17px; font-weight: 300; border: 1px solid @borderColor2; border-radius: 3px 0 0 3px; color: @textColor; display: inline-block; vertical-align: top; padding: 13px 20px; width: 50%; position: relative;
		&:after{.pseudo(12px,12px); .icon-arrow; font: 7px/12px @fonti; color: @fblue; top: 16px; right: 20px;}
		&.special{border-radius: 3px; width: 100%; display: block;}
	}
	.c-categories1{overflow-y: auto; height: 100%; padding-bottom: 48px;}
	.m-close-filters{
		display: block; padding: 13px 15px; font-size: 16px; line-height: 22px; position: relative; display: block; border-bottom: 1px solid @borderColor2; color: @textColor;
		&:after{.pseudo(12px,12px); .icon-close; font: 12px/12px @fonti; color: @textColor; top: 19px; right: 15px;}
	}
	.c-extra-desc{margin-top: 0; padding-top: 20px; border-top: 1px solid @borderColor2;}
	.ced-container{
		&.active{
			.ced-title:after{top: 2px;}
		}
	}
	.c-extra-desc.special .ced-title, .ced-title{font-size: 20px; line-height: 26px; font-weight: 600; color: @textColor; padding-right: 25px;}
	.ced-title:after{top: 2px;}
	.ced-content img{width: 100%; height: auto; margin-bottom: 15px;}
	/*------- /760 catalog index -------*/

	/*------- 760 filters -------*/
	.cf-main-title{display: none;}
	.cf{padding-right: 0;}
	.filter-category{
		padding-top: 10px; padding-left: 15px; border-color: @borderColor2;
		&.active{
			.filters-category{display: none;}
			.filter-category-title:after{.icon-plus;}
		}
	}
	.filter-category-title{
		position: relative;
		&:after{.pseudo(12px,12px); .icon-minus; font: 12px/12px @fonti; color: @lblue; top: 4px; right: 18px;}
	}
	.cf-item-wrapper{padding-left: 15px;}
	.cf-title{
		padding-left: 15px; border-color: @borderColor2;
		&:after{width: 12px; height: 12px; font-size: 12px; line-height: 12px; right: 18px;}
	}
	.cf-active{padding: 10px 15px 30px; border-color: @borderColor2;}
	.m-filter-bottom{display: block; background: @white; padding: 15px; position: fixed; bottom: 0; left: 0; right: 0; width: auto;}
	.m-btn-cf-active-clear{
		display: block; text-align: center; margin-top: 10px; padding: 14px 15px!important; border: 1px solid @red; border-radius: 3px; font-size: 14px; color: @red;
		span:before{top: 6px;}
	}
	.confirm-filters{
		visibility: hidden;
		&.active{visibility: visible;}
	}
	/*------- /760 filters -------*/

	/*------- 760 catalog post -------*/
	.cp{border-bottom: 0; padding: 0 0 50px;}
	.cp-image{width: 140px;}
	.cp-cnt{width: 59%; padding-bottom: 0;}
	.cp-title{font-size: 14px; line-height: 20px; font-weight: 500; padding-bottom: 7px;}
	.cp-code{opacity: 1;}
	.cp-short-description{display: none;}
	.cp-price{float: none; width: 100%; display: block; margin-bottom: 15px;}
	.cp-btn-details{width: 100%; text-align: center; padding: 11px 10px; margin-top: 20px;}

	.cp-price{margin-top: 10px; margin-bottom: 0;}
	.cp-old-price, .cp-price-label{font-size: 10px; line-height: 14px;}
	.cp-current-price, .cp-discount-price{font-size: 14px; line-height: 20px;}
	.cp-bottom-container{left: 0; position: relative; flex-flow: column; justify-content: flex-end; height: 100%; flex-grow: 1; flex-shrink: 1;}
	.cp-lowest-price{font-size: 10px; line-height: 12px;}
	/*------- /760 catalog post -------*/

	/*------- 760 catalog detail -------*/
	.cd-main{
		.bc{
			display: flex; flex-wrap: wrap;
			a{
				margin-bottom: 3px;
				&:before{top: 5px;}
				&:last-of-type:before{display: none;}
			}
		}
		.bc-last{display: none;}
	}
	.cd-body{display: block;}
	.cd-main .bc{padding-bottom: 15px;}
	.cd-title{font-size: 16px; line-height: 22px; padding-bottom: 2px;}
	.cd-code{font-size: 12px; line-height: 16px;}
	.cd-col2-top-left{margin-bottom: 15px;}
	.cd-col1{width: 100%; float: none; display: block;}
	.cd-col2{padding-left: 0;}
	.cd-thumbs{
		width: 100%; display: block;
		.owl-dot{height: auto; line-height: inherit; display: inline-block; vertical-align: top; width: 20%; margin: 0;}
	}
	.cd-hero-image{width: 100%; display: block; margin-bottom: 5px;}
	.cd-col2{display: block; float: none; width: 100%;}
	.cd-old-price, .cd-price-label{font-size: 12px; line-height: 14px;}
	.cd-current-price{font-size: 22px; line-height: 26px;}
	.cd-col2-top-right{margin-top: 15px;}
	.cd-col2-top{border-bottom: 0; padding-bottom: 0;}
	.cd-short-desc ul li:before{width: 6px; height: 6px; top: 10px;}
	.cd-col2-short-description{
		.btn-special{display: block; width: 100%; margin: 0 0 15px; text-align: center;}
	}
	.cd-lowest-price{
		font-size: 12px; line-height: 15px;
		br{display: none;}
	}
	.related-other-colors{margin-top: 15px;}
	.roc-title{padding: 0 0 15px;}
	.related-other-size{margin-top: 15px;}
	.ros-item{min-width: 44px; min-height: 44px; padding: 5px 10px; margin: 0 5px 5px 0; font-size: 14px;}

	.cd-buy-container{margin-top: 15px;}
	.size-info-link{font-size: 10px;}
	.cd-variations-top{margin-bottom: 5px;}
	.cd-variations select{height: 50px; padding: 0 20px;}
	.cd-buttons-container a{margin-top: 15px;}
	.cd-btn-add{
		font-size: 14px;
		span{
			padding-left: 35px;
			&:before{font-size: 18px;}
		}
	}
	.cd-order-prescription{font-weight: 500; font-size: 14px;}
	.cd-tabs{
		border-top: 0;
		&:before{.pseudo(auto,1px); background: @borderColor2; top: 0; left: -15px; right: -15px;}
	}
	.cd-tab{
		border-bottom: 0; position: relative;
		&:after{.pseudo(auto,1px); background: @borderColor2; bottom: 0; left: -15px; right: -15px;}
	}
	.cd-tab-desc ul li{
		padding-left: 15px;
		&:before{width: 6px; height: 6px; }
	}
	.share-cnt{margin-top: 25px;}
	.cd-related-posts{text-align: center; margin-top: 35px;}
	.cd-related-posts-title{font-size: 20px; line-height: 28px;}
	.cd-related-post-image{width: auto;}
	.cd-related-posts-slider{
		.owl-nav{height: 65px; top: 96px;}
		.owl-prev, .owl-next{
			background: @white; width: 65px; height: 65px;
			&:before{color: @blue; font-size: 20px; line-height: 20px; width: 20px; height: 20px;}
		}
		.owl-prev{
			border-radius: 0 50% 50% 0; left: -31px;
			&:before{top: 22px; left: auto; right: 16px;}
		}
		.owl-next{
			border-radius: 50px 0 0 50px; right: -31px;
			&:before{top: 22px; left: 15px;}
		}
	}
	.cd-related-post-image{margin: 0; display: block;}
	.cd-related-post-cnt{width: 100%; padding: 15px 15px 0; position: relative; display: block;}
	.cd-related-post-title{font-size: 18px; line-height: 24px;}
	.cd-unavailable-form{
		input{height: 50px; font-size: 14px;}
		.success-message{font-size: 14px; line-height: 22px; width: 100%;}
	}
	.cd-prescription .homepage-form .field{
		display: block; width: 100%; margin: 0 0 10px;
		/*label{position: absolute; display: block; padding: 0; line-height: normal; font-size: 16px; font-weight: 300; top: 16px; left: 20px; .transition(all);}*/
		input{height: 45px; line-height: normal;}
		&.focus{
			label, textarea+label {top: 6px; .scale(0.68); transform-origin: top left; opacity: .8;}
			input{padding-top: 13px;}
		}
	}
	.cd-prescription{
		padding: 25px 15px 35px; top: 135px; left: 0; right: 0;
		&:before{right: auto; left: 50%; margin-left: -7.5px;}
		.homepage-form{
			.field label{font-size: 14px; font-weight: 500;}
			.field-insurance_provider{
				display: block!important; width: 100%!important;
				label{position: relative; display: block; top: auto; left: auto; padding-bottom: 5px;}
			}
			.field-special2 div{
				display: block; width: 100%; margin: 0 0 5px;
				label{display: block; width: 100%; position: relative; top: auto; left: auto; padding-bottom: 10px!important;}
				span{ 
					display: inline-block;
					label{padding-top: 2px; font-size: 14px;}
				}
			}
			.homepage-submit-btn{margin-left: 0; line-height: inherit; height: inherit;}
			.error{position: relative; bottom: auto;}
		}
	}
	.cd-prescription-close{
		margin-top: 0!important; top: 16px; right: 15px;
		&:after{font-size: 20px!important; .icon-close;}
	}
	/*------- /760 catalog detail -------*/

	/*------- 760 catalog related -------*/
	.cd-related-title{font-size: 20px; line-height: 26px;}
	.cd-related-content{margin-top: 15px;}
	.cd-related-item-image{height: auto; line-height: inherit;}
	.cd-related-item{padding: 0;}
	.cd-related-content{
		.owl-nav{height: 65px; top: 220px;}
		.owl-prev, .owl-next{
			background: @white; width: 65px; height: 65px;
			&:before{color: @blue; font-size: 20px; line-height: 20px; width: 20px; height: 20px;}
		}
		.owl-prev{
			border-radius: 0 50% 50% 0; left: -31px;
			&:before{top: 22px; left: auto; right: 16px;}
		}
		.owl-next{
			border-radius: 50px 0 0 50px; right: -31px;
			&:before{top: 22px; left: 15px;}
		}
		.cp-badge{font-size: 12px;}
		.btn{display: none;}
	}
	.cd-related-lowest-price{
		font-size: 11px; line-height: 15px;
		br{display: none;}
	}
	.cd-related-item-title{font-size: 16px; font-weight: 600;}
	.cd-related-item-label, .cd-related-item-old{font-size: 14px; line-height: 18px;}
	.cd-related-item-old ins{margin: 0 2px 0 7px;}
	.cd-related-item-current.red ins{margin: 0 0 0 7px;}
	.cd-related-item-current{font-size: 18px;}
	.cd-related{margin-top: 20px;}
	.cd-related2{margin-top: 45px;}
	/*------- /760 catalog related -------*/

	/*------- 760 add to cart modal -------*/
	.modal-box{
		top: 50px; padding: 32px 22px; display: block;
		.close-button{
			top: 15px; right: 15px; width: 12px; height: 12px;
			&:before{width: 12px; height: 12px; font-size: 12px; line-height: 12px; font-weight: bold;}
		}
		.image{display: none;}
		.desc{width: 100%; float: none;}
		.message{
			font-size: 16px; line-height: 22px; text-align: center; padding: 0 0 8px;
			&:before{width: 24px; height: 24px; font-size: 10px; line-height: 24px; display: inline-block; margin-right: 8px; top: -2px; position: relative;}
		}
		.content{width: 100%; float: none; padding: 0; text-align: center; margin-bottom: 15px;}
		.title{font-size: 14px; line-height: 18px; padding-bottom: 7px;}
		.modal-price{
			width: 100%; float: none; text-align: center; font-size: 20px; line-height: 26px; position: relative;
			.cd-price-label,.cd-tax{display: none!important;}
			.cd-old-price{
				font-size: 12px; line-height: 26px;
				ins{display: initial;}
				span{display: initial;}
			}
			.cd-current-price{
				font-size: 18px; line-height: 23px;
				ins{display: initial;}
				span{display: initial;}
			}
			.cd-lowest-price{
				font-size: 12px; line-height: 16px; margin: 3px 0 8px;
				br{display: inline;}
			}
		}
		.modal-old-price{font-size: 12px; line-height: 16px;}
	}
	.modal-buttons{padding-top: 15px; flex-flow: column; padding-left: 0;}
	.modal-attributes, .modal-code{font-size: 12px; line-height: 14px;}
	.modal-view-cart{margin-left: 0; width: 100%; font-size: 14px; margin-bottom: 12px; display: block; float: initial;}
	.modal-finish-shopping{width: 100%; font-size: 14px; display: block; margin: 0;}
	.modal-continue-shopping{
		float: none; margin: 0 auto; text-align: center; font-size: 14px; display: block; width: 130px; padding: 0; text-decoration: underline;
		&:before{display: none;}
	}
	/*------- /760 add to cart modal -------*/

	/*------- 760 cart -------*/
	.page-shopping-cart .main{padding: 15px 0 40px;}
	.w-title{
		text-align: left; font-size: 28px; line-height: 34px; padding: 0 15px;
		span{font-size: 16px;}
	}
	.wp{
		align-items: flex-start;
		&:last-child{border-bottom: 0;}
	}
	.w-counter{font-size: 16px; padding-left: 5px;}
	.cart-container{padding: 0 15px;}
	.wp-image{width: 80px;}
	.empty-cart{width: 100%; padding: 0 15px; text-align: left;}
	.wp-cnt{width: 100%; display: block; padding: 0 0 15px;}
	.wp-cnt-right{padding: 0 0 0 10px; flex-wrap: wrap;}
	.wp-title{font-size: 14px; line-height: 18px; padding-bottom: 3px;}
	.wp-code, .wp-variations{font-size: 12px; line-height: 15px; padding-bottom: 0;}
	.wp-qty{width: 100px; padding: 0;}
	.wp-btn-qty{
		width: 30px; height: 30px; 
		&:after{font-size: 12px; line-height: 30px;}
	}
	.wp-unit{font-size: 12px; line-height: 14px; margin-top: 3px;}
	.wp-input-qty{font-size: 18px;}
	.wp-total{width: auto; flex-grow: 1; padding-top: 0;}
	.wp-price-old, .wp-qty-count{font-size: 12px; line-height: 14px;}
	.wp-price-current, .wp-price-discount{font-size: 18px; line-height: 24px; font-weight: 500;}
	.wp-lowest-price-outer{height: 32px;}
	.wp-lowest-price{font-size: 12px; line-height: 16px;}
	.wp-btn-delete{display: none;}
	.wp-btn-delete-mobile{display: block; margin-top: 10px; margin-left: 15px;}
	.cart-right{
		float: none; display: block; width: 100%; padding-top: 20px;
		&:before{.pseudo(calc(~"100% - -30px"), 1px); margin-left: -15px; top: 0; left: 0; right: 0; background: @grayLight2;}
	}
	.cart-total-title{font-size: 18px; line-height: 22px; padding-bottom: 10px;}
	.cart-totals{font-size: 14px; line-height: 20px;}
	.cart-total{font-size: 14px;}

	.w-totals-label{width: 52%;}
	.w-totals-value{width: 46%;}
	/*------- /760 cart -------*/

	/*------- 760 checkout login -------*/
	.page-checkout-login .nw{display: none;}
	.checkout-login-main{
		h1{font-size: 24px; line-height: 30px; font-weight: 300; text-align: left; padding-bottom: 16px;}
		h2{font-size: 16px; line-height: 22px; padding-bottom: 7px;}
	}
	.wc-container{display: block; font-size: 14px; line-height: 20px;}
	.wc-col1, .wc-col2{display: block!important; width: 100%!important;}
	.wc-col1{
		padding-right: 0; border-right: 0; position: relative; padding-bottom: 25px; 
		&:after{.pseudo(auto,1px); bottom: 0; left: -15px; right: -15px; background: @borderColor2;}
	}
	.wc-col2{
		padding-left: 0; padding-top: 25px;
	}
	.wc-cnt{padding-bottom: 0;}
	.wc-auth-links a{font-size: 12px; line-height: 16px;}
	.wc-login-form{
		.login-buttons{
			display: flex;
			.btn-wc-login{margin-right: 5px;}
			.auth-links{
				margin-left: 5px!important;
				a{font-size: 12px;}
			}
		}
	}
	.checkout-footer{padding-top: 17px;}
	/*------- /760 checkout login -------*/	

	/*------- 760 checkout -------*/
	.header-checkout{
		height: 93px;
		.logo{top: 12px; left: 50%; margin-left: -82.5px; z-index: 1;}
	}
	.hc-steps{width: 100%; padding: 56px 15px 0; margin: 0; font-size: 14px; line-height: 20px;}
	.hc-label{display: none;}
	.step.current-step{
		font-weight: 500; position: relative;
		&:after{.pseudo(10px,10px); background: @white; border-left: 1px solid @borderColor2; border-top: 1px solid @borderColor2; transform: rotate(45deg); bottom: -22px; left: 50%; margin-left: -5px;}
	}
	.wc-subtitle{font-size: 18px; line-height: 24px; font-weight: 600; padding-bottom: 15px;}
	.checkout-main{
		.wc-col1{
			padding-right: 0; padding-bottom: 0;
			&:after{display: none;}
		}
		.wc-col2{padding-left: 0; padding-top: 15px;}
	}
	.checkout-form{
		input[type=checkbox]+label{font-size: 14px; padding-top: 3px!important;}
		select{font-size: 14px;}
		&.step3{
			.wc-subtitle{padding-bottom: 10px;}
		}
	}
	.checkout-last{margin-top: 15px;}
	.btn-checkout{margin-top: 5px;}
	.checkout-finish{font-size: 12px; line-height: 16px; margin-top: 12px;}
	.field-country label{display: none;}
	.step2{
		.field-zipcode{display: inline-block; vertical-align: top; width: calc(~"50% - 5px"); margin-right: 10px!important;}
		.field-city{width: calc(~"50% - 5px"); margin-right: 0; display: inline-block; vertical-align: top;}
	}
	.col2-section-shipping{margin-top: 32px;}
	.field-shipping{
		margin-top: 15px;
		&>label{font-size: 18px; line-height: 24px; font-weight: 600;}
		span{ 
			margin-bottom: 15px;
			div{font-size: 12px; line-height: 16px; padding-left: 28px;}
		}
	}
	.field-b_same_as_shipping{
		&>label{line-height: inherit!important; padding-top: 5px!important;}
	}
	.field-b_country label{display: none;}
	.field-payment, .section-shipping{
		label{padding-top: 0!important; font-size: 14px!important; font-weight: 500;}
		span{
			margin-bottom: 10px;
			div{font-size: 12px; line-height: 16px;}
		}
	}
	.cart-small{
		margin-top: 0; padding-top: 15px;
		.w-cart-title{font-size: 18px; line-height: 24px; font-weight: 600;}
		.counter{font-size: 14px; font-weight: normal;}
		.w-btn-change{font-size: 12px; line-height: 15px;}
		.wp-price-discount,.wp-price-current{font-size: 16px; line-height: 20px;}
		.w-totals-title{font-size: 18px; line-height: 24px; font-weight: 600;}
		.cart-totals{font-size: 14px; line-height: 20px;}
		.wp-qty-count{
			ins{display: initial;}
			span:last-child{display: initial;}
		}
		.wp-cnt{padding-bottom: 50px;}
		.wp-lowest-price{font-size: 12px; line-height: 16px;}
		.wp{padding: 8px 0;}
		.wp:last-child{padding-bottom: 16px;}
		.w-totals-cnt{margin-top: 16px;}
	}
	.w-table-small{
		margin-top: 15px;
		.wp-image{width: 80px;}
		.wp-cnt{width: 72%; display: inline-block; vertical-align: top;}
		.wp-title{font-size: 14px; line-height: 18px; padding-bottom: 5px;}
		.wp-code, .wp-variations{font-size: 12px; line-height: 14px;}
	}
	.page-payment{
		.wc-col1{
			padding-bottom: 20px;
			&:after{left: 0; right: 0;}
		}
		.wc-col2{
			padding-top: 0;
			.wc-subtitle{padding-bottom: 10px;}
		}
		.shipping-row{
			margin-bottom: 15px;
			div{padding-top: 0;}
		}
	}
	.cf-col{
		display: block; width: 100%; text-align: left; margin-bottom: 20px;
		.footer-title{font-size: 16px; line-height: 22px;}
		.item-tel, .item-mail{
			padding-left: 30px; font-size: 16px!important; line-height: 22px!important;
			a{text-decoration: underline;}
			&:before{display: block; top: 3px;}
		}
		.item-mail:before{top: 6px;}
	}
	.cf-col2{
		padding-top: 3px; margin-bottom: 20px;
		p{display: inline-block; width: 50%;}
		a{max-width: 100%;}
		img{max-width: 85%;}
		.convatec{
			padding-top: 7px; text-align: right;
			img{display: inline-block;}
		}
	}
	.cf-col3{
		display: flex; width: 100%; font-size: 0; float: none; justify-content: inherit; flex-flow: column; position: absolute; bottom: 30px; left: 0; align-items: flex-end;
		p{
			display: inline-block; vertical-align: middle; width: 50%; text-align: center;
			&:first-child{margin: 0;}
			&:last-child{padding-top: 8px;}
		}
		.payway{width: 100%;}
	}
	.checkout-footer{
		.dev{position: relative; float: left; margin-top: 1px; font-size: 0; right: 0;}
		.copy{position: relative; float: right;}
	}
	.wc-step3-col2 .error{font-size: 12px;}
	/*------- /760 checkout -------*/

	/*------- 760 thankyou -------*/
	.invoice-container{margin-top: 15px; padding: 15px 15px 5px;}
	.thank-you-content{font-size: 14px; line-height: 20px; margin-bottom: 20px;}
	.thank-you-login{width: 100%; margin-bottom: 15px;}
	.thank-you-safe{font-size: 14px; line-height: 20px;}
	.ty-payment-transfer-cnt{
		overflow: auto; margin-top: 0;
		.wide-table:before{display: none;}
	}
	/*------- /760 thankyou -------*/

	/*------- 760 auth login -------*/
	.pal-main{
		h1{font-size: 24px; line-height: 30px; font-weight: 300; padding-bottom: 20px;}
	}
	.pal-main-content{
		display: block;
		h2{font-size: 16px; line-height: 22px; padding-bottom: 10px; font-weight: bold;}
	}
	.pal-left{
		padding-right: 0; border-right: 0; width: 100%; display: block; padding-bottom: 30px;
		&:after{.pseudo(auto,1px); background: @borderColor2; bottom: 0; left: -15px; right: -15px;}
		h2{display: none;}
	}
	.pal-right{
		padding-left: 0; width: 100%; display: block; padding-top: 25px; font-size: 14px; line-height: 20px;
		ul li:before{top: 9px;}
	}
	.page-signup .pal-main-content h2{padding-bottom: 10px;}
	.a-have-account{font-weight: 600!important;}
	.pal-main-content .a-have-account{padding-top: 10px;}
	.login-section{display: flex;}
	.ls-left{margin-right: 5px;}
	.ls-right{margin-left: 5px; font-size: 12px;}
	/*------- /760 auth login -------*/

	/*------- 760 forgotten -------*/
	.forgotten-main h1{font-weight: 300; font-size: 24px; line-height: 30px; text-align: left; padding-bottom: 20px;}
	.auth-forgotten-password-form{width: 100%;}
	/*------- /760 forgotten -------*/

	/*------- 760 auth -------*/
	.auth-main{
		h1{font-size: 24px; line-height: 30px; font-weight: 300; text-align: left; padding-bottom: 15px;}
		.req{font-size: 14px; line-height: 18px; text-align: center; padding-bottom: 0;}
	}
	.auth-menu{
		text-align: left; font-size: 0;
		li{display: inline-block; width: 50%;}
		a{
			text-align: left; display: block; padding: 0; font-size: 14px; line-height: 20px; text-decoration: underline;
			&.active{text-decoration: none;}
		}
	}
	.a-intro{display: none;}
	.auth-box.orders-container{
		margin-top: 0; position: relative; padding-top: 15px;
		&:before{.pseudo(auto,1px); background: @borderColor2; top: 0; left: -15px; right: -15px;}
	}
	.a-section-title{font-size: 18px; line-height: 24px; padding-bottom: 15px;}
	.auth-change-password-form, .auth-edit-profile-form{width: 100%;}
	.auth-change-password-form .submit{margin-top: 0; padding-bottom: 0;}
	
	/*------- /760 auth -------*/

	/*------- 760 orders -------*/
	.table-order{font-size: 14px; line-height: 20px; position: relative; padding: 15px 0;}
	.orders .table-col{display: block; width: 100%; text-align: left; padding: 0 0 5px;}
	.col-order-btns{position: absolute; top: 15px; right: 0; width: auto!important;}
	.btn-order-details span{
		padding-right: 20px;
		&:after{width: 12px; height: 12px; font-size: 12px; line-height: 12px;}
	}
	.wp-details{padding: 0 0 20px;}
	.w-table-details{
		padding: 0;
		.wp-cnt{width: 72%; display: inline-block; vertical-align: top; padding-left: 10px; padding-right: 100px;}
		.wp-title{font-size: 14px; line-height: 18px; padding-bottom: 5px;}
		.wp-total{position: absolute; float: none; bottom: 20px; right: 0;}
		.wp-price-old, .wp-qty-count{font-size: 13px; line-height: 16px;}
		.wp-price-current, .wp-price-discount{font-size: 16px; line-height: 20px;}
	}
	.wp-details-sum{
		margin: 0 0 25px; font-size: 16px; line-height: 20px;
		span:first-child{width: 100%; padding-top: 20px; padding-left: 0; text-align: right; display: block;}
	}
	/*------- /760 orders -------*/

	/*------- 760 search -------*/
	.page-search{
		h1{
			font-size: 14px; line-height: 20px; padding-bottom: 15px;
			span{font-size: 24px; line-height: 30px;}
		}
		.main{padding-top: 15px;}
		.page-header-cnt2{
			border: none;
			&:before{left: -15px; right: -15px;}
			&:after{left: -15px; right: -15px;}
		}
		.page-header{margin-bottom: 0;}
		.main-content{width: 100%;} 
		.page-header-cnt{padding: 13px 0; align-items: center;}
		.c-sort{
			width: 50%;
			select{height: 45px; background-position: right 20px top 19px;}
		}
	}
	.s-nav{
		padding-top: 0;
		a{font-size: 14px; line-height: 20px;}
	}
	.search-main-content .pp{
		display: block; width: 100%; margin: 0 0 40px;
		&:nth-child(4n-2){margin-bottom: 40px;}
		&:nth-child(4n){margin-bottom: 40px;}
		&:nth-child(3n){width: 100%; margin-bottom: 40px;}
	}
	.page-search, .page-search{
		.page-header-cnt{
			&:before{left: -15px; right: -15px;}
			&:after{left: -15px; right: -15px;}
		}
	}
	.page-search-cms .search{padding: 0;}
	.s-item-title{font-size: 22px; line-height: 28px; padding-bottom: 15px;}
	.s-item{
		font-size: 14px; line-height: 20px; margin-bottom: 40px;
		.btn{width: 100%; display: block; font-size: 14px;}
	}
	/*------- /760 search -------*/
}

@media screen and (max-width: 360px) {
	.footer-col5{bottom: 45px;}
}


/*------- /Responsive -------*/
