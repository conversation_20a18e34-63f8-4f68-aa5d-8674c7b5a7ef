$(function() {
	cmswebshop = CmsWebshop();
	cmswebshop.init();
	
	// newsletter
	cmsnewsletter = CmsNewsletter();

	var winWidth = $(window).width();

	// search
	var searchEl = $('div.sw');
	searchEl.find('.sw-toggle').on('click', function(e) {
		searchEl.toggleClass('active');
		searchEl.find('input').focus();
	});

	if ($('[data-variations]').size()) {
		cmscatalog = CmsCatalog();
		cmscatalog.variation_choices('bxslider', 'input', 1);
		cmscatalog.change_variation_detail(0, $('[data-variations]').data('variation_active_id'));
	}

	// search autocomplete
	$("input[name='search_q']").CmsAutocomplete({
		module: 'catalog', 
		lang: site_lang,
		result_fields: 'all:image,price',
		layout: '<span class="image"><img src="%item_image%" alt=""></span><span class="autocomplete-cnt"><span class="search-title">%item_label%</span><span class="search-price">%item_price%</span></span>',
		auto_redirect: true,
		result_image: '80x80_r'
	});

	// close elements on outside click
	function closeActiveElement(el) {
		$(document).on('mouseup', function(e) {
			var container = el;
			if(!container.is(e.target) && container.has(e.target).length === 0) {
				container.removeClass('active');
			}
		});
	}
	closeActiveElement($('.sw'));
	closeActiveElement($('.categories li.has-children'));
	closeActiveElement($('.field-special'));
	closeActiveElement($('.p-categories-cnt'));

	if(winWidth > 760){
		closeActiveElement($('.m-menu, .nav-top'));
	}

	$('.m-menu').on('click', function() {
		$('body').toggleClass('active-nav');
	});

	$('.homepage-intro-form-button, .hir-close-btn').on('click', function() {
		$('body').toggleClass('active-hir');
	});
	
	$('input#field-healthy_worker').live('click', function() {
		$('.healthy_fields').toggle();
	});

	$(".size-info-link").fancybox({
		minWidth	: 400,
		minHeight	: 400,
		autoWidth   : true,
		fitToView	: true,
		autoSize	: true
	});

	//homepage slider
	$(".homepage-slider").owlCarousel({
		items: 1, 
		nav: false,
		dots: true,
		loop: true,
		autoHeight: true,
		animateIn: 'fadeIn',
		animateOut: 'fadeOut',
		autoplay: true,
		autoplayTimeout: 4000,
		onTranslated:function() {
			if (typeof bLazy === 'object') {bLazy.revalidate();}
		},
		responsive:{
			760:{autoplay: false}
		},
	});

	//homepage publish widget slider
	setTimeout(function(){
		$(".m-hpw-slider").owlCarousel({
			items: 1, 
			nav: true,
			dots: false,
			loop: true,
			//autoHeight: true,
			animateIn: 'fadeIn',
			animateOut: 'fadeOut',
			onTranslated:function() {
				if (typeof bLazy === 'object') {bLazy.revalidate();}
			},
		});
	}, 1000);
	

	if(winWidth > 1200 ){
		$('.field-special label').on('hover', function(){
			$(this).parent().toggleClass('active');
		});

		$('.hcw-col').on('hover', function(){
			$(this).toggleClass('active');
		});
	}

	// fp dropdowns
	$('.fp-title').on('click', function() {
		var $this = $(this),
			parent = $this.parent();

		if(parent.hasClass('active')) {
			parent.toggleClass('active');
		} else {
			$('.fp').removeClass('active');
			$this.parent().addClass('active');			
		}
	});

	//faq first element has class active
	$('.fp:first').addClass('active');

	//catalog index sidebar menu
	$('.c-categories li.has-children>a').on('click', function(e){
		var $this = $(this),
			parent = $this.parent();

		e.preventDefault();
		parent.toggleClass('active');
	});

	// Function to check if viewport is mobile (less than 990px)
	function isMobile() {
		return $(window).width() < 990;
	}

	// Helper function to handle responsive events
	function handleResponsiveEvent(e, callback) {
		const mobile = isMobile();
		// Only proceed with hover on desktop or click on mobile
		if ((e.type === 'mouseenter' && mobile) || (e.type === 'click' && !mobile)) {
			return;
		}
		// Prevent default for click events on mobile
		if (e.type === 'click' && mobile) {
			e.preventDefault();
		}
		callback.call(this);
	}

	// Handle category interactions based on viewport size
	$(document).on('mouseenter click', '.categories li', function(e) {
		handleResponsiveEvent.call(this, e, function() {
			const categoryId = $(this).data('category-id');
			const level = $(this).data('level');
			const submenu = $(this).find('>ul');
			if (level == 2) {
				$('.subcategories-col4').html('');
			}
			const container = $(`.subcategories-col${level+1}[data-category-id="${categoryId}"]`);
			container.html(submenu.clone());
		});
	});

	$('.categories>li').on('mouseenter click', function(e) {
		handleResponsiveEvent.call(this, e, function() {
			$(this).addClass('active');
		});
	});

	$('.categories>li').on('mouseleave', function() {
		if (!isMobile()) {
			$('.subcategories-col3, .subcategories-col4').html('');
			$('.categories li').removeClass('active');
		}
	});

	$('.categories-level2 li').on('mouseenter click', function(e) {
		handleResponsiveEvent.call(this, e, function() {
			$('.subcategories-container li').removeClass('active');
			$(this).addClass('active');
		});
	});

	$(document).on('mouseenter click', '.subcategories-col3 li', function(e) {
		handleResponsiveEvent.call(this, e, function() {
			$('.subcategories-col3 li').removeClass('active');
			$(this).addClass('active');
		});
	});

	// Close categories on mobile when clicking outside
	$(document).on('click', function(e) {
		if (isMobile() && !$(e.target).closest('.categories').length) {
			$('.subcategories-col3, .subcategories-col4').html('');
			$('.categories li').removeClass('active');
		}
	});

	if(winWidth <= 760){
		$('.c-categories>li').removeClass('active');
	}

	//catalog index dropdowns
	$('.ced-title').on('click', function() {
		var $this = $(this),
			parent = $this.parent();

		if(parent.hasClass('active')) {
			parent.toggleClass('active');
		} else {
			$('.ced-container').removeClass('active');
			$this.parent().addClass('active');			
		}
	});

	// catalog detail image slider
	setTimeout(function(){
		if ($('.cd-hero-slider').length) {
			owl_carousel = $(".cd-hero-slider").owlCarousel({
				items: 1,
				dots: true,
				dotsContainer: '.cd-thumbs',
				dotsEach: true,
				loop: false,
				autoHeight: true,
				animateIn: 'fadeIn',
				animateOut: 'fadeOut',
				mouseDrag: false,
				onTranslated:function() {
					if (typeof bLazy === 'object') {bLazy.revalidate();}
				}
				//animateIn: 'fadeIn'
			});
		}
	}, 1000);

	//catalog detail tabs
	$('.cd-tab-title').on('click', function() {
		var $this = $(this),
			parent = $this.parent();

		if(parent.hasClass('active')) {
			parent.toggleClass('active');
		} else {
			$('.cd-tab').removeClass('active');
			$this.parent().addClass('active');			
		}
	});

	//catalog detail related
	setTimeout(function(){	
		$(".cd-related-content").owlCarousel({
			items: 1, 
			nav: true,
			dots: false,
			loop: true,
			margin: 0,
			slideBy: 1,
			autoHeight: true,
			onTranslated:function() {
				if (typeof bLazy === 'object') {bLazy.revalidate();}
			},
			responsive:{
				760:{
					items: 4,
					margin: 10,
					slideBy: 4,
					loop: false,
				},
				1350:{
					margin: 30,
					items: 4,
					slideBy: 4,
					loop: false,
				}
			},
		});
	}, 1000);

	//publish detail related posts
	setTimeout(function(){
		$(".pd-other-items").owlCarousel({
			items: 1, 
			nav: true,
			dots: false,
			loop: true,
			slideBy: 1,
			margin: 0,
			autoHeight: true,
			onTranslated:function() {
				if (typeof bLazy === 'object') {bLazy.revalidate();}
			},
			responsive:{
				760:{
					items: 3,
					nav: false,
					loop: false,
					margin: 10,
				},
				990:{
					items: 4,
					nav: false,
					loop: false,
					margin: 10,
				}
			},
		});
	}, 1000);	

	// catalog filter
	$('#attribute_filters_select').CmsFilter({lang: site_lang});

	// close shopping cart preview
	$('.product-message-modal .close, .modal-continue-shopping').live('click', function() {
		$('.product-message-modal').removeClass('active');
	});

	$(document).on('mouseup', function(e) {
		var container = $('div.modal-box');

		if(!container.is(e.target) && container.has(e.target).length === 0) {
			$('div.product-message-modal').removeClass('active');
		}
	});

	//detail variations
	var rosItem = $('.related-other-size .ros-container .ros-item');
	if (rosItem.length > 0) {
		var rosContainer = $('.related-other-size');
		rosContainer.removeClass('empty-related-other-size');
	}

	//locations
	var location_map = $('#map_canvas');
	if (location_map.size()) {
		setTimeout(function() {
			cmslocation = CmsLocation();
			cmslocation.config.markeractive = '/media/images/pin.svg';
			cmslocation.config.markerinactive = '/media/images/pin.svg';
			cmslocation.config.scrollwheel = false;
			cmslocation.init(site_lang, {maxZoom: 30}, {maxWidth: 200, pixelOffset: new google.maps.Size(20, -30), closeBoxMargin: "0", closeBoxURL: "/media/images/icons/close3.svg"});
			cmslocation.set_points_detect();
		}, 2000);
	}

	
	// thank you page show password
	if ($('#field-password').size()) {
		$('#field-password').showPassword();
	}

	//input parent add focus class
	$('.form-label').CmsUtilsFormLabels();
	// floating labels
	/*
	$('.field-b_company_name, .field-b_company_oib, .field-b_company_address').addClass('field-cnt').removeClass('field');

	$('.field-b_r1').on('click', function() {
		setTimeout(function() {
			$('.field-cnt').floatingFormLabels();
		}, 200);
	});

	if($('.field').length) {
		$('.field').floatingFormLabels();
	}
	setTimeout(function() {
		if (winWidth > 720) {
			$('#webshop_form:not(".step3") .field:first').addClass('ffl-floated');
		}
		else{
			$('#webshop_form:not(".step3") .field:first input').blur();
		}
	},100);
	*/

	// social share
	//$(document).CmsShare({networks: 'facebook,googleplus,email'});
	$(document).CmsShare({networks: 'facebook,whatsapp,viber,email'});
	$('.ss_copy').on('click', function() {
		var dummy = document.createElement('input'),
			text = window.location.href;

		document.body.appendChild(dummy);
		dummy.value = text;
		dummy.select();
		document.execCommand('copy');
		document.body.removeChild(dummy);
	});

	// image titles
	$('.main .image-border').CmsUtilsImageTitle();

	$('.m-p-categories').on('click', function () {
		$(this).parent().toggleClass('active');
	});

	$('.cd-order-prescription').on('click', function() {
		if(!$(this).hasClass('active')) {
			$('html, body').animate({
				scrollTop: $("#buy-container").offset().top
			}, 1000);
		}
	});

	//footer title
	if (winWidth <= 980) {
		$('.footer .footer-title').on('click', function () {
			$(this).parent().toggleClass('active');
		});
	}

	//ssm 1350
	ssm.addState({
		query: '(max-width: 1350px)',
		onEnter: function() {
			$('.footer-login').insertBefore('.footer-col5');
			/* $('.checkout-footer .dev').appendTo('.cf-col1');
			$('.checkout-footer .copy').appendTo('.cf-col2'); */
		},
		onLeave: function() {
			$('.footer-login').insertBefore('.copy');
			/* $('.checkout-footer .dev').insertAfter('.cf-col3');
			$('.checkout-footer .copy').insertAfter('.copy'); */
		}
	});

	if(winWidth <= 1200){
		$('.categories li.has-children>a').on('click', function(e){
			e.preventDefault();

			$(this).parent().toggleClass('active');
		});

		//responsive table
		$('.cd-tab-desc table, .t-table-container>table, .cms-content>table').wrap('<div class="table-wrapper"/>');	

		$('table').each(function() {
			var $this = $(this);

			if($this.width() > $this.parent().parent().width()) {
				$this.parent().addClass('wide-table');
			}
		});

		var iOS = ( navigator.userAgent.match(/(iPad|iPhone|iPod)/g) ? true : false );
		if (iOS == true) {
			$('div.table-wrapper').addClass('ios');
		}

		//cart active on <1200
		$('.touch .cart.active').on('mouseover', function() {
			location.href='/webshop/';
		});
	}

	//ssm 990
	ssm.addState({
		query: '(max-width: 990px)',
		onEnter: function() {
			$('.footer-login').insertBefore('.footer-col4');
			if(!$('body').hasClass('page-checkout')){	
				$('.dev').insertBefore('.copy');
			}
			$('.cd-notify-form .error').insertBefore('.btn-unavailable');
		},
		onLeave: function() {
			$('.footer-login').insertBefore('.footer-col5');
			if(!$('body').hasClass('page-checkout')){	
				$('.copy').insertBefore('.dev');
			}
			$('.cd-prescription2 form').removeClass('form-input form-label');
		}
	});

	if(winWidth){
		$('.field-special label').on('click', function(){
			$(this).parent().toggleClass('active');
		});
	}

	//ssm 760
	ssm.addState({
		query: '(max-width: 760px)',
		onEnter: function() {
			$('.categories-cnt').insertBefore('.mobile-top-menu-list');
			$('.sw').insertBefore('.categories-cnt');
			$('.footer-col5 p:first-child').insertAfter('.footer-col5 p:last-child');
			$('.footer-bottom').addClass('clear');
			$('.catalog-index .sidebar').insertBefore('.header');
			$('.cd-col2-top-left').insertBefore('.cd-images');
			$('.cd-related-posts').insertAfter('.cd-col2');
			//$('.cd-prescription form').addClass('form-inline form-label');
			//$('.form-label').CmsUtilsFormLabels();
			$('.checkout-footer .dev').insertAfter('.cf-col3');
			$('.checkout-footer .copy').insertAfter('.dev');
			$('.search-sort').insertAfter('.mobile-filters');
			$('.cd-thumbs').insertAfter('.cd-hero-image');
			$('.footer-exchange-rate').insertBefore('.footer-login'); 
			$('.wc-step3-col2 .col2-section-shipping').insertAfter('.payment-options'); 
		},
		onLeave: function() {
			if(!$('body').hasClass('quick')) {
				setTimeout(function() {
					location.reload();
				},1000);
			}
		}
	});

	if(winWidth <= 760){
		$('.mobile-top-menu-list>li.has-children>a').on('click', function(e){
			e.preventDefault();
			var $this = $(this),
				parent = $this.parent();

			if(parent.hasClass('active')) {
				parent.toggleClass('active');
			} else {
				$('.mobile-top-menu-list>li.has-children').removeClass('active');
				$this.parent().addClass('active');			
			}	
		});

		$('.mobile-top-menu-list>li.has-children>ul>li.has-children>a').on('click', function(e){
			e.preventDefault();
			var $this = $(this),
				parent = $this.parent();

			if(parent.hasClass('active')) {
				parent.toggleClass('active');
			} else {
				$('.mobile-top-menu-list>ul>li.has-children>ul>li.has-children').removeClass('active');
				$this.parent().addClass('active');			
			}	
		});

		$('.filter-category-title').on('click', function(){
			$(this).parent().toggleClass('active');
		});

		//catalog detail related posts
		setTimeout(function(){
			$(".cd-related-posts-slider").owlCarousel({
				items: 1, 
				nav: true,
				dots: false,
				loop: true,
				margin: 0,
				slideBy: 1,
				autoHeight: false,
				onTranslated:function() {
					if (typeof bLazy === 'object') {bLazy.revalidate();}
				},
				responsive:{
					760:{
						nav: false
					}
				},
			});
		}, 1000);
	}

	// close shopping cart preview
	/*
	$('.product-message-modal .close, .modal-continue-shopping').live('click', function() {
		$('.product-message-modal').removeClass('active');
	});

	$(document).on('mouseup', function(e) {
		var container = $('div.modal-box');

		if(!container.is(e.target) && container.has(e.target).length === 0) {
			$('div.product-message-modal').removeClass('active');
		}
	});
	*/
});