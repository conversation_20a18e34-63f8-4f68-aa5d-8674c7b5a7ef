<svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid" class="lds-ring">
    <circle cx="50" cy="50" ng-attr-r="{{config.radius}}" ng-attr-stroke="{{config.base}}" ng-attr-stroke-width="{{config.width}}" fill="none" r="10" stroke="#ffffff" stroke-width="10"></circle>
    <circle cx="50" cy="50" ng-attr-r="{{config.radius}}" ng-attr-stroke="{{config.stroke}}" ng-attr-stroke-width="{{config.innerWidth}}" ng-attr-stroke-linecap="{{config.linecap}}" fill="none" r="10" stroke="#2c497e" stroke-width="2" stroke-linecap="square" transform="rotate(15.6522 50 50)">
      <animateTransform attributeName="transform" type="rotate" calcMode="linear" values="0 50 50;180 50 50;720 50 50" keyTimes="0;0.5;1" dur="2.3s" begin="0s" repeatCount="indefinite"></animateTransform>
      <animate attributeName="stroke-dasharray" calcMode="linear" values="6.283185307179586 56.548667764616276;15.707963267948966 47.12388980384689;6.283185307179586 56.548667764616276" keyTimes="0;0.5;1" dur="2.3" begin="0s" repeatCount="indefinite"></animate>
    </circle>
</svg>