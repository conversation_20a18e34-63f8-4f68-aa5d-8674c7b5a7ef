@import "defaults.less";


/*------- helpers -------*/
.float-left { float: left; }
.float-right { float: right; }
.strong { font-weight: bold; }
.italic { font-style: italic; }
.uppercase { text-transform: uppercase; }

.first{margin-left:0 !important;}
.last{margin-right:0 !important;}

.image-left, .alignleft { float: left; margin: 5px 20px 10px 0px; }
.image-right, .alignright { float: right; margin: 5px 0px 10px 20px; }

.align-left {text-align:left;}
.align-right {text-align:right;}
.center {text-align:center;}

.underline { text-decoration:underline; }
.nounderline { text-decoration:none; }
.rounded { border-radius: @borderRadius; }

.red {color: @red;}
.green {color: @green;}
.orange {color: @orange;}

.first-title { margin-top: 0; padding-top: 0; }
/*------- /helpers -------*/

/*------- selectors -------*/
* { margin: 0; padding: 0; border: none; }
body {background: #fff; padding: 10px 15px; color: @textColor; .font(@fontSize, @lineHeight, @font); }
table { border-spacing: 0; border: none; }
a {
	color: @linkColor;text-decoration: underline; .transition();
	&:hover { text-decoration: underline;color: @linkHoverColor; }
}
ul, ol {margin: 0px 0px 10px 35px;padding: 0px;}
ol { margin-left: 40px; }
h1, h2, h3, h4{
	font-weight: 300; padding-bottom: 13px;
	a, a:hover{text-decoration: none;}
}
h1{font-size: 64px; line-height: 78px; padding-bottom: 9px;}
h2{font-size: 44px; line-height: 53px; padding: 25px 0 13px;}
h3{font-size: 34px; line-height: 41px; padding: 25px 0 13px;}
h4{font-size: 24px; line-height: 29px; padding: 20px 0 8px;}
p{padding-bottom: 15px; }
table{
	display: block; width: auto;
	tbody{border: 1px solid @borderColor; border-right: 0;}
	tr{
		display: block; border-bottom: 1px solid @borderColor;
		&:last-child{border-bottom: 0;}	
	}
	td{padding: 5px 7px; border-right: 1px solid @borderColor2; width: auto; min-width: 115px;}
}
/*------- /selectors -------*/

/*------- buttons -------*/
.btn, input[type=submit], button{position: relative; display: inline-block; vertical-align: middle; padding: 0 70px; height: 54px; font-size: 16px; line-height: 55px; color: #fff; background: @lblue; text-decoration: none; border-radius: 3px; text-decoration: none !important; font-weight: 500; .transition(all);}
.btn-blue{background: @blue;}
.btn-green{background: @green;}
.btn-special{
	padding: 0 30px 0 30px; height: 50px; line-height: 50px; color: @lblue; font-size: 16px; display: inline-block; vertical-align: middle; border: 1px solid #0090C4; border-radius: 3px; font-weight: bold; margin-right: 10px; margin-top: 15px; text-decoration: none!important; text-transform: none; .transition(all);
	span{
		position: relative; padding-left: 40px;
		&:before{position: absolute; .icon-download; font: 20px/20px @fonti; color: @lblue; left: 0px; top: -1px; .transition(all);}
	}
}
.btn-special-pdf{
	span{
		&:before{position: absolute; .icon-pdf; font: 25px/25px @fonti; color: @lblue; left: 0px; top: -3px; .transition(all);}
	}
}
.btn-border{display: inline-block; vertical-align: top; padding: 0 20px; font-size: 14px; color: @lblue; font-weight: bold; height: 50px; line-height: 50px; border: 1px solid #E0E2DB; border-radius: 3px; font-family: Arial, sans-serif;}
.btn-white{background: @white; border: 1px solid @lblue; color: @lblue;}
/*------- /buttons -------*/

/*------- tables -------*/
.table { 
	width: 100%; border-spacing:0; margin: 10px 0px 20px; 
	tbody{border-color: @textColor;}
	th {border-bottom: 0; border-right: 1px solid @textColor; font-weight: normal; color: @white; background: @blue3; padding: 10px 0; text-align: center; font-size: 16px;  min-width: 139px;}
	td { border-bottom: 1px solid @gray; padding: 6px 0; min-width: 139px; font-size: 14px; text-align: center;}
	tr:nth-child(odd){
		td{background: #e1f3fb;}
	}
	&.stripe tbody tr:nth-child(even) { background: #E9E9E9; }
}
/*------- /tables -------*/

/*------- main -------*/
.blue2{color: @blue2};
.blue3{color: @blue3};
.blue4{color: @blue4};
.purple1{color: @purple1};
.purple2{color: @purple2};
.purple3{color: @purple3};
.green2{color: @green2};
.green3{color: @green3};
.yellow2{color: @yellow2};
.red2{color: @red2};
.orange2{color: @orange2};
/*------- /main -------*/