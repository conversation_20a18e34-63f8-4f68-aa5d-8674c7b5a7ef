/*------- mixins -------*/
.transition (@element: color, @speed: .3s) {
	transition: @arguments;
}
.grayscale (@bw: 100%) {
	filter: grayscale(@bw);
	-webkit-filter: grayscale(@bw);
	-moz-filter: grayscale(@bw);
}
.text-shadow (@color, @x: 1px, @y: 1px, @blur: 0px) {
	text-shadow: @x @y @blur @color;
}
.gradient(@startColor: #eee, @endColor: white) {
	background: @startColor;
	background-image: linear-gradient(180deg, @startColor 0%, @endColor 100%);
	background-image: -webkit-linear-gradient(180deg, @startColor 0%, @endColor 100%);
	background-image: -moz-linear-gradient(@startColor 0%, @endColor 100%);
	background-image: -ms-linear-gradient(180deg, @startColor 0%, @endColor 100%);
}
.horizontal-gradient (@startColor: #eee, @endColor: white) {
 	background-color: @startColor;
	background-image: -webkit-linear-gradient(90deg, @startColor, @endColor);
	background-image: -ms-linear-gradient(90deg, @startColor, @endColor);
	background-image: linear-gradient(90deg, @startColor, @endColor);
}
.font(@fontSize, @fontLineHeight, @fontType) {
	font-size: @fontSize;
	line-height: @fontLineHeight; 
	font-family: @fontType;
}
.pseudo(@width, @height) {
	content:"";
	position: absolute;
	display: block;
	width:@width;
	height:@height;
}
.animation (@name, @duration: 300ms, @delay: 0, @ease: ease) {
	-webkit-animation: @name @duration @delay @ease;
	-moz-animation:    @name @duration @delay @ease;
	-ms-animation:     @name @duration @delay @ease;
}
.transform(@string){
	transform: @string;
	-webkit-transform: @string;
	-ms-transform: 		 @string;
}
.scale (@factor) {
	transform: scale(@factor);
	-webkit-transform: scale(@factor);
	-ms-transform: 		 scale(@factor);
}
.rotate (@deg) {
	transform: rotate(@deg);
	-webkit-transform: rotate(@deg);
	-ms-transform: 		 rotate(@deg);
}
.skew (@deg, @deg2) {
	transform: skew(@deg, @deg2);
	-webkit-transform: skew(@deg, @deg2);
	-ms-transform: skew(@deg, @deg2);
}
.translate (@x, @y:0) {
	transform: translate(@x, @y);
	-webkit-transform: translate(@x, @y);
	-ms-transform: translate(@x, @y);
}
.translate3d (@x, @y: 0, @z: 0) {
	transform: translate3d(@x, @y, @z);
	-webkit-transform: translate3d(@x, @y, @z);
	-ms-transform: translate3d(@x, @y, @z);
}
.perspective (@value: 1000) {
	perspective: 		@value;
	-webkit-perspective: 	@value;
}
.clear { 
	*zoom: 1; clear:both;
	&:before, &:after {content:""; display: table; }
	&:after { clear: both; }
}
.placeholder(@color,@focusColor) {
	&::-webkit-input-placeholder { color: @color; }
	&:-ms-input-placeholder { color: @color; }
	&::-moz-placeholder { color: @color; }
	&:focus {
		&::-webkit-input-placeholder { color: @focusColor; }
		&:-ms-input-placeholder { color: @focusColor; }
		&::-moz-placeholder { color: @focusColor; }
	}
}
.shadow { content:""; position: absolute; left: 35px; right: 35px; top: 20px; bottom: 5px; box-shadow: 0px 10px 65px rgba(0,0,0,.6); }
.lloader {
	background: #fff url(images/loader.svg) no-repeat center center; display: block;
	img { opacity: 0; .transition(opacity); }
	&.loaded, .loaded {
		background: #fff;
		img { opacity: 1; }
	}
}
/*------- /mixins -------*/


/*------- fonts -------*/
@font-face {
	font-family: 'Helvetica';
	src: local('☺'), url('fonts/helveticaneueltpro-th.woff') format('woff');
	font-weight: 100;
	font-style: normal;
}
@font-face {
	font-family: 'Helvetica';
	src: local('☺'), url('fonts/helveticaneueltpro-lt.woff') format('woff');
	font-weight: 300;
	font-style: normal;
}
@font-face {
	font-family: 'Helvetica';
	src: local('☺'), url('fonts/helveticaneueltpro-roman.woff') format('woff');
	font-weight: normal;
	font-style: normal;
}
@font-face {
	font-family: 'Helvetica';
	src: local('☺'), url('fonts/helveticaneueltpro-md.woff') format('woff');
	font-weight: 500;
	font-style: normal;
}
@font-face {
	font-family: 'Helvetica';
	src: local('☺'), url('fonts/helveticaneueltpro-bd.woff') format('woff');
	font-weight: bold;
	font-style: normal;
}
@font-face {
    font-family: 'icomoon';
    src: url('fonts/icomoon.woff?v3') format('woff');
    font-weight: normal;
    font-style: normal;
}
/*------- /fonts -------*/


/*------- site vars -------*/
@pageWidth: 1680px;
@fontSize: 20px;
@lineHeight: 28px;
@textColor: #424656;
 
@linkColor: @lblue;
@linkHoverColor: @blue;

@primaryColor: #ccc;
@secondaryColor: #74ad11;
@red: #B60016;
@yred: #ff0000;
@blue: #2C497E;
@lblue: #0090C4;
@fblue: #526BA3;
@black: #4A4A4A;
@gray: #E0E2DB;
@grayLight: #A6ABBD;
@grayLight2: #D8D8D8;
@orange: #d89724;
@white: #fff;
@green: #40B04A;
@yellow: #e9ec00;
@borderColor: #E0E2DB;
@borderColor2: #F1F1F1;
@turquoise: #00D2B1;
@borderRadius: 3px;

@blue2: #2d4a7f;
@blue3: #236192;
@blue4: #007ac2;
@purple1: #5e1b88;
@purple2: #7b207f;
@purple3: #8a1e82;
@green2: #009984; 
@green3: #4c9934;
@yellow2: #f2a900;
@red2: #871e3f;
@orange2: #e87722;

@font: "Helvetica", Arial, Helvetica, sans-serif;
@fonti: "icomoon";

@errorColor: @red;
@warningColor: @orange;
@successColor: @green;

@d: 1700px;
@l: 1400px;
@t: 1350px;
@tp: 990px;
@m: 770px;
/*------- /site vars -------*/

/*------- icons -------*/
.icon-close2 {
  content: "\e923";
}
.icon-menu {
  content: "\e922";
}
.icon-phone {
  content: "\e921";
}
.icon-pharmacy {
  content: "\e91d";
}
.icon-payment {
  content: "\e91b";
}
.icon-years {
  content: "\e913";
}
.icon-info {
  content: "\e911";
}
.icon-arrow {
  content: "\e900";
}
.icon-cart {
  content: "\e901";
}
.icon-check {
  content: "\e902";
}
.icon-clock {
  content: "\e903";
}
.icon-close {
  content: "\e904";
}
.icon-danger-red {
  content: "\e905";
}
.icon-danger-white {
  content: "\e906";
}
.icon-download {
  content: "\e907";
}
.icon-email {
  content: "\e908";
}
.icon-facebook {
  content: "\e909";
}
.icon-google {
  content: "\e90a";
}
.icon-google-share {
  content: "\e90b";
}
.icon-instagram {
  content: "\e90c";
}
.icon-linkedin {
  content: "\e90d";
}
.icon-list {
  content: "\e90e";
}
.icon-mail-send {
  content: "\e90f";
}
.icon-marker {
  content: "\e910";
}
.icon-pdf {
  content: "\e912";
}
.icon-pin {
  content: "\e914";
}
.icon-search {
  content: "\e915";
}
.icon-tel {
  content: "\e916";
}
.icon-telechat {
  content: "\e917";
}
.icon-truck {
  content: "\e918";
}
.icon-twitter {
  content: "\e919";
}
.icon-user {
  content: "\e91a";
}
.icon-youtube {
  content: "\e91c";
}
.icon-minus {
  content: "\e91e";
}
.icon-plus {
  content: "\e91f";
}
.icon-arrow2 {
  content: "\e920";
}
.icon-copy {
  content: "\e924";
}
.icon-viber {
  content: "\e925";
}
.icon-whatsapp {
  content: "\e926"; 
}
.icon-twitter2 {
  content: "\e927"; 
}
/*------- /icons -------*/


/*
	LEGEND
	screen sizes: 1700, 1400, 1350, 990,

	- normalize
	- helpers
	- tables
	- selectors
	- forms
	- info messages
	- buttons
	- owl
	- header
	- auth widget
	- cart widget
	- categories
	- search widget
	- autocomplete
	- main
	- sideabar
	- breadcrumbs
	- share
	- newsletter
	- footer
	- homepage
	- homepage publish widget
	- homepage catalog widget
	- publish index
	- publish post
	- publish detail
	- catalog index
	- catalog post
	- catalog detail
	- catalog detail related
	- add to cart modal
	- fancybox quick
	- faq
	- shopping cart
	- auth
	- auth orders
	- auth form
	- auth login
	- forgotten password
	- checkout login
	- checkout
	- thankyou
	- search
	- prescription
	- tellfriend
	- loading
*/